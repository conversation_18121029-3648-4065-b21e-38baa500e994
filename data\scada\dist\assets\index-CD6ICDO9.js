const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Preview-DUc2K0YM.js","./Meta2DCanvas-9N-Tmw7R.js","./element-plus-D-V1KzVw.js","./meta2d-DtcXQZZw.js","./_plugin-vue_export-helper-DlAUqK2U.js","./Meta2DCanvas-CCe9ZYSf.css","./Preview-DLWqHJab.css","./el-button-CcaTBLwS.css","./PublishedPreview-DKClmfRN.js","./PublishedPreview-lKbXHGjM.css","./Login-BXEafrT4.js","./Login-DXCWGkmI.css","./el-form-item-DCFsf57O.css","./el-checkbox-DIj50LEB.css","./Meta2D-ETuptM_u.js","./Meta2D-CWsj8RQ7.css","./Shortcuts-CiJ7xsuO.js","./Shortcuts-BRP7pBxC.css","./ForgetPassword-CZG4B7un.js","./ForgetPassword-DX59jtNq.css"])))=>i.map(i=>d[i]);
var pr=Object.defineProperty;var mr=(e,t,n)=>t in e?pr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Nt=(e,t,n)=>mr(e,typeof t!="symbol"?t+"":t,n);import{r as At,A as gn,G as wn,H as He,I as bn,J as yr,B as vn,C as En,K as gr,L as Sn,M as $,N as wr,O as br,P as ie,Q as vr,R as Er,d as _t,u as Ee,S as Sr,T as tt,U as Rn,z as nt,V as Rr,a as Or,o as Ar,h as _r,W as Tr,X as Pr,Y as Cr}from"./element-plus-D-V1KzVw.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let On;const De=e=>On=e,An=Symbol();function ht(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Se;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Se||(Se={}));function xr(){const e=wn(!0),t=e.run(()=>At({}));let n=[],r=[];const s=Sn({install(o){De(s),s._a=o,o.provide(An,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const _n=()=>{};function Ht(e,t,n,r=_n){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&wr()&&br(s),s}function le(e,...t){e.slice().forEach(n=>{n(...t)})}const Lr=e=>e(),Bt=Symbol(),rt=Symbol();function dt(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];ht(s)&&ht(r)&&e.hasOwnProperty(n)&&!He(r)&&!bn(r)?e[n]=dt(s,r):e[n]=r}return e}const kr=Symbol();function Mr(e){return!ht(e)||!e.hasOwnProperty(kr)}const{assign:te}=Object;function zr(e){return!!(He(e)&&e.effect)}function Nr(e,t,n,r){const{state:s,actions:o,getters:i}=t,c=n.state.value[e];let u;function a(){c||(n.state.value[e]=s?s():{});const l=gr(n.state.value[e]);return te(l,o,Object.keys(i||{}).reduce((f,d)=>(f[d]=Sn($(()=>{De(n);const p=n._s.get(e);return i[d].call(p,p)})),f),{}))}return u=Tn(e,a,t,n,r,!0),u}function Tn(e,t,n={},r,s,o){let i;const c=te({actions:{}},n),u={deep:!0};let a,l,f=[],d=[],p;const y=r.state.value[e];!o&&!y&&(r.state.value[e]={}),At({});let w;function g(x){let T;a=l=!1,typeof x=="function"?(x(r.state.value[e]),T={type:Se.patchFunction,storeId:e,events:p}):(dt(r.state.value[e],x),T={type:Se.patchObject,payload:x,storeId:e,events:p});const D=w=Symbol();En().then(()=>{w===D&&(a=!0)}),l=!0,le(f,T,r.state.value[e])}const O=o?function(){const{state:T}=n,D=T?T():{};this.$patch(G=>{te(G,D)})}:_n;function E(){i.stop(),f=[],d=[],r._s.delete(e)}const R=(x,T="")=>{if(Bt in x)return x[rt]=T,x;const D=function(){De(r);const G=Array.from(arguments),Y=[],Q=[];function Qe(U){Y.push(U)}function ge(U){Q.push(U)}le(d,{args:G,name:D[rt],store:C,after:Qe,onError:ge});let Z;try{Z=x.apply(this&&this.$id===e?this:C,G)}catch(U){throw le(Q,U),U}return Z instanceof Promise?Z.then(U=>(le(Y,U),U)).catch(U=>(le(Q,U),Promise.reject(U))):(le(Y,Z),Z)};return D[Bt]=!0,D[rt]=T,D},z={_p:r,$id:e,$onAction:Ht.bind(null,d),$patch:g,$reset:O,$subscribe(x,T={}){const D=Ht(f,x,T.detached,()=>G()),G=i.run(()=>vn(()=>r.state.value[e],Y=>{(T.flush==="sync"?l:a)&&x({storeId:e,type:Se.direct,events:p},Y)},te({},u,T)));return D},$dispose:E},C=gn(z);r._s.set(e,C);const k=(r._a&&r._a.runWithContext||Lr)(()=>r._e.run(()=>(i=wn()).run(()=>t({action:R}))));for(const x in k){const T=k[x];if(He(T)&&!zr(T)||bn(T))o||(y&&Mr(T)&&(He(T)?T.value=y[x]:dt(T,y[x])),r.state.value[e][x]=T);else if(typeof T=="function"){const D=R(T,x);k[x]=D,c.actions[x]=T}}return te(C,k),te(yr(C),k),Object.defineProperty(C,"$state",{get:()=>r.state.value[e],set:x=>{g(T=>{te(T,x)})}}),r._p.forEach(x=>{te(C,i.run(()=>x({store:C,app:r._a,pinia:r,options:c})))}),y&&o&&n.hydrate&&n.hydrate(C.$state,y),a=!0,l=!0,C}/*! #__NO_SIDE_EFFECTS__ */function Hr(e,t,n){let r,s;const o=typeof t=="function";typeof e=="string"?(r=e,s=o?n:t):(s=e,r=e.id);function i(c,u){const a=vr();return c=c||(a?ie(An,null):null),c&&De(c),c=On,c._s.has(r)||(o?Tn(r,t,s,c):Nr(r,s,c)),c._s.get(r)}return i.$id=r,i}function Br(e){let t=document.location.toString();console.log("获取url地址参数",t);let n=t.split("?");if(n.length>1){let r=n[1].split("&"),s;for(let o=0;o<r.length;o++)if(s=r[o].split("="),s!=null&&s[0]==e)return s[1];return""}else return""}function Pn(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ir}=Object.prototype,{getPrototypeOf:Tt}=Object,{iterator:Ve,toStringTag:Cn}=Symbol,je=(e=>t=>{const n=Ir.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),W=e=>(e=e.toLowerCase(),t=>je(t)===e),qe=e=>t=>typeof t===e,{isArray:me}=Array,Ae=qe("undefined");function Pe(e){return e!==null&&!Ae(e)&&e.constructor!==null&&!Ae(e.constructor)&&j(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const xn=W("ArrayBuffer");function Ur(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&xn(e.buffer),t}const Fr=qe("string"),j=qe("function"),Ln=qe("number"),Ce=e=>e!==null&&typeof e=="object",Dr=e=>e===!0||e===!1,Me=e=>{if(je(e)!=="object")return!1;const t=Tt(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Cn in e)&&!(Ve in e)},Vr=e=>{if(!Ce(e)||Pe(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},jr=W("Date"),qr=W("File"),$r=W("Blob"),Kr=W("FileList"),Wr=e=>Ce(e)&&j(e.pipe),Jr=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||j(e.append)&&((t=je(e))==="formdata"||t==="object"&&j(e.toString)&&e.toString()==="[object FormData]"))},Gr=W("URLSearchParams"),[Qr,Xr,Yr,Zr]=["ReadableStream","Request","Response","Headers"].map(W),es=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function xe(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),me(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{if(Pe(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function kn(e,t){if(Pe(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const re=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Mn=e=>!Ae(e)&&e!==re;function pt(){const{caseless:e}=Mn(this)&&this||{},t={},n=(r,s)=>{const o=e&&kn(t,s)||s;Me(t[o])&&Me(r)?t[o]=pt(t[o],r):Me(r)?t[o]=pt({},r):me(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&xe(arguments[r],n);return t}const ts=(e,t,n,{allOwnKeys:r}={})=>(xe(t,(s,o)=>{n&&j(s)?e[o]=Pn(s,n):e[o]=s},{allOwnKeys:r}),e),ns=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),rs=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ss=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&Tt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},os=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},is=e=>{if(!e)return null;if(me(e))return e;let t=e.length;if(!Ln(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},cs=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Tt(Uint8Array)),as=(e,t)=>{const r=(e&&e[Ve]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},ls=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},us=W("HTMLFormElement"),fs=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),It=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),hs=W("RegExp"),zn=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};xe(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},ds=e=>{zn(e,(t,n)=>{if(j(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(j(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ps=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return me(e)?r(e):r(String(e).split(t)),n},ms=()=>{},ys=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function gs(e){return!!(e&&j(e.append)&&e[Cn]==="FormData"&&e[Ve])}const ws=e=>{const t=new Array(10),n=(r,s)=>{if(Ce(r)){if(t.indexOf(r)>=0)return;if(Pe(r))return r;if(!("toJSON"in r)){t[s]=r;const o=me(r)?[]:{};return xe(r,(i,c)=>{const u=n(i,s+1);!Ae(u)&&(o[c]=u)}),t[s]=void 0,o}}return r};return n(e,0)},bs=W("AsyncFunction"),vs=e=>e&&(Ce(e)||j(e))&&j(e.then)&&j(e.catch),Nn=((e,t)=>e?setImmediate:t?((n,r)=>(re.addEventListener("message",({source:s,data:o})=>{s===re&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),re.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",j(re.postMessage)),Es=typeof queueMicrotask<"u"?queueMicrotask.bind(re):typeof process<"u"&&process.nextTick||Nn,Ss=e=>e!=null&&j(e[Ve]),h={isArray:me,isArrayBuffer:xn,isBuffer:Pe,isFormData:Jr,isArrayBufferView:Ur,isString:Fr,isNumber:Ln,isBoolean:Dr,isObject:Ce,isPlainObject:Me,isEmptyObject:Vr,isReadableStream:Qr,isRequest:Xr,isResponse:Yr,isHeaders:Zr,isUndefined:Ae,isDate:jr,isFile:qr,isBlob:$r,isRegExp:hs,isFunction:j,isStream:Wr,isURLSearchParams:Gr,isTypedArray:cs,isFileList:Kr,forEach:xe,merge:pt,extend:ts,trim:es,stripBOM:ns,inherits:rs,toFlatObject:ss,kindOf:je,kindOfTest:W,endsWith:os,toArray:is,forEachEntry:as,matchAll:ls,isHTMLForm:us,hasOwnProperty:It,hasOwnProp:It,reduceDescriptors:zn,freezeMethods:ds,toObjectSet:ps,toCamelCase:fs,noop:ms,toFiniteNumber:ys,findKey:kn,global:re,isContextDefined:Mn,isSpecCompliantForm:gs,toJSONObject:ws,isAsyncFn:bs,isThenable:vs,setImmediate:Nn,asap:Es,isIterable:Ss};function A(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}h.inherits(A,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:h.toJSONObject(this.config),code:this.code,status:this.status}}});const Hn=A.prototype,Bn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Bn[e]={value:e}});Object.defineProperties(A,Bn);Object.defineProperty(Hn,"isAxiosError",{value:!0});A.from=(e,t,n,r,s,o)=>{const i=Object.create(Hn);return h.toFlatObject(e,i,function(u){return u!==Error.prototype},c=>c!=="isAxiosError"),A.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Rs=null;function mt(e){return h.isPlainObject(e)||h.isArray(e)}function In(e){return h.endsWith(e,"[]")?e.slice(0,-2):e}function Ut(e,t,n){return e?e.concat(t).map(function(s,o){return s=In(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function Os(e){return h.isArray(e)&&!e.some(mt)}const As=h.toFlatObject(h,{},null,function(t){return/^is[A-Z]/.test(t)});function $e(e,t,n){if(!h.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=h.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,g){return!h.isUndefined(g[w])});const r=n.metaTokens,s=n.visitor||l,o=n.dots,i=n.indexes,u=(n.Blob||typeof Blob<"u"&&Blob)&&h.isSpecCompliantForm(t);if(!h.isFunction(s))throw new TypeError("visitor must be a function");function a(y){if(y===null)return"";if(h.isDate(y))return y.toISOString();if(h.isBoolean(y))return y.toString();if(!u&&h.isBlob(y))throw new A("Blob is not supported. Use a Buffer instead.");return h.isArrayBuffer(y)||h.isTypedArray(y)?u&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function l(y,w,g){let O=y;if(y&&!g&&typeof y=="object"){if(h.endsWith(w,"{}"))w=r?w:w.slice(0,-2),y=JSON.stringify(y);else if(h.isArray(y)&&Os(y)||(h.isFileList(y)||h.endsWith(w,"[]"))&&(O=h.toArray(y)))return w=In(w),O.forEach(function(R,z){!(h.isUndefined(R)||R===null)&&t.append(i===!0?Ut([w],z,o):i===null?w:w+"[]",a(R))}),!1}return mt(y)?!0:(t.append(Ut(g,w,o),a(y)),!1)}const f=[],d=Object.assign(As,{defaultVisitor:l,convertValue:a,isVisitable:mt});function p(y,w){if(!h.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+w.join("."));f.push(y),h.forEach(y,function(O,E){(!(h.isUndefined(O)||O===null)&&s.call(t,O,h.isString(E)?E.trim():E,w,d))===!0&&p(O,w?w.concat(E):[E])}),f.pop()}}if(!h.isObject(e))throw new TypeError("data must be an object");return p(e),t}function Ft(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Pt(e,t){this._pairs=[],e&&$e(e,this,t)}const Un=Pt.prototype;Un.append=function(t,n){this._pairs.push([t,n])};Un.toString=function(t){const n=t?function(r){return t.call(this,r,Ft)}:Ft;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function _s(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Fn(e,t,n){if(!t)return e;const r=n&&n.encode||_s;h.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=h.isURLSearchParams(t)?t.toString():new Pt(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Dt{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){h.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Dn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ts=typeof URLSearchParams<"u"?URLSearchParams:Pt,Ps=typeof FormData<"u"?FormData:null,Cs=typeof Blob<"u"?Blob:null,xs={isBrowser:!0,classes:{URLSearchParams:Ts,FormData:Ps,Blob:Cs},protocols:["http","https","file","blob","url","data"]},Ct=typeof window<"u"&&typeof document<"u",yt=typeof navigator=="object"&&navigator||void 0,Ls=Ct&&(!yt||["ReactNative","NativeScript","NS"].indexOf(yt.product)<0),ks=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ms=Ct&&window.location.href||"http://localhost",zs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ct,hasStandardBrowserEnv:Ls,hasStandardBrowserWebWorkerEnv:ks,navigator:yt,origin:Ms},Symbol.toStringTag,{value:"Module"})),F={...zs,...xs};function Ns(e,t){return $e(e,new F.classes.URLSearchParams,{visitor:function(n,r,s,o){return F.isNode&&h.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Hs(e){return h.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Bs(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Vn(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),u=o>=n.length;return i=!i&&h.isArray(s)?s.length:i,u?(h.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!h.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&h.isArray(s[i])&&(s[i]=Bs(s[i])),!c)}if(h.isFormData(e)&&h.isFunction(e.entries)){const n={};return h.forEachEntry(e,(r,s)=>{t(Hs(r),s,n,0)}),n}return null}function Is(e,t,n){if(h.isString(e))try{return(t||JSON.parse)(e),h.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Le={transitional:Dn,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=h.isObject(t);if(o&&h.isHTMLForm(t)&&(t=new FormData(t)),h.isFormData(t))return s?JSON.stringify(Vn(t)):t;if(h.isArrayBuffer(t)||h.isBuffer(t)||h.isStream(t)||h.isFile(t)||h.isBlob(t)||h.isReadableStream(t))return t;if(h.isArrayBufferView(t))return t.buffer;if(h.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Ns(t,this.formSerializer).toString();if((c=h.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return $e(c?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Is(t)):t}],transformResponse:[function(t){const n=this.transitional||Le.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(h.isResponse(t)||h.isReadableStream(t))return t;if(t&&h.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?A.from(c,A.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:F.classes.FormData,Blob:F.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};h.forEach(["delete","get","head","post","put","patch"],e=>{Le.headers[e]={}});const Us=h.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Fs=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Us[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Vt=Symbol("internals");function be(e){return e&&String(e).trim().toLowerCase()}function ze(e){return e===!1||e==null?e:h.isArray(e)?e.map(ze):String(e)}function Ds(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Vs=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function st(e,t,n,r,s){if(h.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!h.isString(t)){if(h.isString(r))return t.indexOf(r)!==-1;if(h.isRegExp(r))return r.test(t)}}function js(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function qs(e,t){const n=h.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let q=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,u,a){const l=be(u);if(!l)throw new Error("header name must be a non-empty string");const f=h.findKey(s,l);(!f||s[f]===void 0||a===!0||a===void 0&&s[f]!==!1)&&(s[f||u]=ze(c))}const i=(c,u)=>h.forEach(c,(a,l)=>o(a,l,u));if(h.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(h.isString(t)&&(t=t.trim())&&!Vs(t))i(Fs(t),n);else if(h.isObject(t)&&h.isIterable(t)){let c={},u,a;for(const l of t){if(!h.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[a=l[0]]=(u=c[a])?h.isArray(u)?[...u,l[1]]:[u,l[1]]:l[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=be(t),t){const r=h.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Ds(s);if(h.isFunction(n))return n.call(this,s,r);if(h.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=be(t),t){const r=h.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||st(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=be(i),i){const c=h.findKey(r,i);c&&(!n||st(r,r[c],c,n))&&(delete r[c],s=!0)}}return h.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||st(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return h.forEach(this,(s,o)=>{const i=h.findKey(r,o);if(i){n[i]=ze(s),delete n[o];return}const c=t?js(o):String(o).trim();c!==o&&delete n[o],n[c]=ze(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return h.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&h.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Vt]=this[Vt]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=be(i);r[c]||(qs(s,i),r[c]=!0)}return h.isArray(t)?t.forEach(o):o(t),this}};q.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);h.reduceDescriptors(q.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});h.freezeMethods(q);function ot(e,t){const n=this||Le,r=t||n,s=q.from(r.headers);let o=r.data;return h.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function jn(e){return!!(e&&e.__CANCEL__)}function ye(e,t,n){A.call(this,e??"canceled",A.ERR_CANCELED,t,n),this.name="CanceledError"}h.inherits(ye,A,{__CANCEL__:!0});function qn(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new A("Request failed with status code "+n.status,[A.ERR_BAD_REQUEST,A.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function $s(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ks(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(u){const a=Date.now(),l=r[o];i||(i=a),n[s]=u,r[s]=a;let f=o,d=0;for(;f!==s;)d+=n[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),a-i<t)return;const p=l&&a-l;return p?Math.round(d*1e3/p):void 0}}function Ws(e,t){let n=0,r=1e3/t,s,o;const i=(a,l=Date.now())=>{n=l,s=null,o&&(clearTimeout(o),o=null),e(...a)};return[(...a)=>{const l=Date.now(),f=l-n;f>=r?i(a,l):(s=a,o||(o=setTimeout(()=>{o=null,i(s)},r-f)))},()=>s&&i(s)]}const Be=(e,t,n=3)=>{let r=0;const s=Ks(50,250);return Ws(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,u=i-r,a=s(u),l=i<=c;r=i;const f={loaded:i,total:c,progress:c?i/c:void 0,bytes:u,rate:a||void 0,estimated:a&&c&&l?(c-i)/a:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},jt=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},qt=e=>(...t)=>h.asap(()=>e(...t)),Js=F.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,F.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(F.origin),F.navigator&&/(msie|trident)/i.test(F.navigator.userAgent)):()=>!0,Gs=F.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];h.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),h.isString(r)&&i.push("path="+r),h.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Qs(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Xs(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function $n(e,t,n){let r=!Qs(t);return e&&(r||n==!1)?Xs(e,t):t}const $t=e=>e instanceof q?{...e}:e;function ce(e,t){t=t||{};const n={};function r(a,l,f,d){return h.isPlainObject(a)&&h.isPlainObject(l)?h.merge.call({caseless:d},a,l):h.isPlainObject(l)?h.merge({},l):h.isArray(l)?l.slice():l}function s(a,l,f,d){if(h.isUndefined(l)){if(!h.isUndefined(a))return r(void 0,a,f,d)}else return r(a,l,f,d)}function o(a,l){if(!h.isUndefined(l))return r(void 0,l)}function i(a,l){if(h.isUndefined(l)){if(!h.isUndefined(a))return r(void 0,a)}else return r(void 0,l)}function c(a,l,f){if(f in t)return r(a,l);if(f in e)return r(void 0,a)}const u={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(a,l,f)=>s($t(a),$t(l),f,!0)};return h.forEach(Object.keys({...e,...t}),function(l){const f=u[l]||s,d=f(e[l],t[l],l);h.isUndefined(d)&&f!==c||(n[l]=d)}),n}const Kn=e=>{const t=ce({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=q.from(i),t.url=Fn($n(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let u;if(h.isFormData(n)){if(F.hasStandardBrowserEnv||F.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((u=i.getContentType())!==!1){const[a,...l]=u?u.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([a||"multipart/form-data",...l].join("; "))}}if(F.hasStandardBrowserEnv&&(r&&h.isFunction(r)&&(r=r(t)),r||r!==!1&&Js(t.url))){const a=s&&o&&Gs.read(o);a&&i.set(s,a)}return t},Ys=typeof XMLHttpRequest<"u",Zs=Ys&&function(e){return new Promise(function(n,r){const s=Kn(e);let o=s.data;const i=q.from(s.headers).normalize();let{responseType:c,onUploadProgress:u,onDownloadProgress:a}=s,l,f,d,p,y;function w(){p&&p(),y&&y(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let g=new XMLHttpRequest;g.open(s.method.toUpperCase(),s.url,!0),g.timeout=s.timeout;function O(){if(!g)return;const R=q.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),C={data:!c||c==="text"||c==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:R,config:e,request:g};qn(function(k){n(k),w()},function(k){r(k),w()},C),g=null}"onloadend"in g?g.onloadend=O:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(O)},g.onabort=function(){g&&(r(new A("Request aborted",A.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new A("Network Error",A.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let z=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const C=s.transitional||Dn;s.timeoutErrorMessage&&(z=s.timeoutErrorMessage),r(new A(z,C.clarifyTimeoutError?A.ETIMEDOUT:A.ECONNABORTED,e,g)),g=null},o===void 0&&i.setContentType(null),"setRequestHeader"in g&&h.forEach(i.toJSON(),function(z,C){g.setRequestHeader(C,z)}),h.isUndefined(s.withCredentials)||(g.withCredentials=!!s.withCredentials),c&&c!=="json"&&(g.responseType=s.responseType),a&&([d,y]=Be(a,!0),g.addEventListener("progress",d)),u&&g.upload&&([f,p]=Be(u),g.upload.addEventListener("progress",f),g.upload.addEventListener("loadend",p)),(s.cancelToken||s.signal)&&(l=R=>{g&&(r(!R||R.type?new ye(null,e,g):R),g.abort(),g=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const E=$s(s.url);if(E&&F.protocols.indexOf(E)===-1){r(new A("Unsupported protocol "+E+":",A.ERR_BAD_REQUEST,e));return}g.send(o||null)})},eo=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(a){if(!s){s=!0,c();const l=a instanceof Error?a:this.reason;r.abort(l instanceof A?l:new ye(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,o(new A(`timeout ${t} of ms exceeded`,A.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(a=>{a.unsubscribe?a.unsubscribe(o):a.removeEventListener("abort",o)}),e=null)};e.forEach(a=>a.addEventListener("abort",o));const{signal:u}=r;return u.unsubscribe=()=>h.asap(c),u}},to=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},no=async function*(e,t){for await(const n of ro(e))yield*to(n,t)},ro=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Kt=(e,t,n,r)=>{const s=no(e,t);let o=0,i,c=u=>{i||(i=!0,r&&r(u))};return new ReadableStream({async pull(u){try{const{done:a,value:l}=await s.next();if(a){c(),u.close();return}let f=l.byteLength;if(n){let d=o+=f;n(d)}u.enqueue(new Uint8Array(l))}catch(a){throw c(a),a}},cancel(u){return c(u),s.return()}},{highWaterMark:2})},Ke=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Wn=Ke&&typeof ReadableStream=="function",so=Ke&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Jn=(e,...t)=>{try{return!!e(...t)}catch{return!1}},oo=Wn&&Jn(()=>{let e=!1;const t=new Request(F.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Wt=64*1024,gt=Wn&&Jn(()=>h.isReadableStream(new Response("").body)),Ie={stream:gt&&(e=>e.body)};Ke&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ie[t]&&(Ie[t]=h.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new A(`Response type '${t}' is not supported`,A.ERR_NOT_SUPPORT,r)})})})(new Response);const io=async e=>{if(e==null)return 0;if(h.isBlob(e))return e.size;if(h.isSpecCompliantForm(e))return(await new Request(F.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(h.isArrayBufferView(e)||h.isArrayBuffer(e))return e.byteLength;if(h.isURLSearchParams(e)&&(e=e+""),h.isString(e))return(await so(e)).byteLength},co=async(e,t)=>{const n=h.toFiniteNumber(e.getContentLength());return n??io(t)},ao=Ke&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:u,responseType:a,headers:l,withCredentials:f="same-origin",fetchOptions:d}=Kn(e);a=a?(a+"").toLowerCase():"text";let p=eo([s,o&&o.toAbortSignal()],i),y;const w=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let g;try{if(u&&oo&&n!=="get"&&n!=="head"&&(g=await co(l,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),I;if(h.isFormData(r)&&(I=C.headers.get("content-type"))&&l.setContentType(I),C.body){const[k,x]=jt(g,Be(qt(u)));r=Kt(C.body,Wt,k,x)}}h.isString(f)||(f=f?"include":"omit");const O="credentials"in Request.prototype;y=new Request(t,{...d,signal:p,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:O?f:void 0});let E=await fetch(y,d);const R=gt&&(a==="stream"||a==="response");if(gt&&(c||R&&w)){const C={};["status","statusText","headers"].forEach(T=>{C[T]=E[T]});const I=h.toFiniteNumber(E.headers.get("content-length")),[k,x]=c&&jt(I,Be(qt(c),!0))||[];E=new Response(Kt(E.body,Wt,k,()=>{x&&x(),w&&w()}),C)}a=a||"text";let z=await Ie[h.findKey(Ie,a)||"text"](E,e);return!R&&w&&w(),await new Promise((C,I)=>{qn(C,I,{data:z,headers:q.from(E.headers),status:E.status,statusText:E.statusText,config:e,request:y})})}catch(O){throw w&&w(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new A("Network Error",A.ERR_NETWORK,e,y),{cause:O.cause||O}):A.from(O,O&&O.code,e,y)}}),wt={http:Rs,xhr:Zs,fetch:ao};h.forEach(wt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Jt=e=>`- ${e}`,lo=e=>h.isFunction(e)||e===null||e===!1,Gn={getAdapter:e=>{e=h.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!lo(n)&&(r=wt[(i=String(n)).toLowerCase()],r===void 0))throw new A(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,u])=>`adapter ${c} `+(u===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Jt).join(`
`):" "+Jt(o[0]):"as no adapter specified";throw new A("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:wt};function it(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ye(null,e)}function Gt(e){return it(e),e.headers=q.from(e.headers),e.data=ot.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Gn.getAdapter(e.adapter||Le.adapter)(e).then(function(r){return it(e),r.data=ot.call(e,e.transformResponse,r),r.headers=q.from(r.headers),r},function(r){return jn(r)||(it(e),r&&r.response&&(r.response.data=ot.call(e,e.transformResponse,r.response),r.response.headers=q.from(r.response.headers))),Promise.reject(r)})}const Qn="1.11.0",We={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{We[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Qt={};We.transitional=function(t,n,r){function s(o,i){return"[Axios v"+Qn+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new A(s(i," has been removed"+(n?" in "+n:"")),A.ERR_DEPRECATED);return n&&!Qt[i]&&(Qt[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};We.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function uo(e,t,n){if(typeof e!="object")throw new A("options must be an object",A.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],u=c===void 0||i(c,o,e);if(u!==!0)throw new A("option "+o+" must be "+u,A.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new A("Unknown option "+o,A.ERR_BAD_OPTION)}}const Ne={assertOptions:uo,validators:We},J=Ne.validators;let se=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Dt,response:new Dt}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=ce(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&Ne.assertOptions(r,{silentJSONParsing:J.transitional(J.boolean),forcedJSONParsing:J.transitional(J.boolean),clarifyTimeoutError:J.transitional(J.boolean)},!1),s!=null&&(h.isFunction(s)?n.paramsSerializer={serialize:s}:Ne.assertOptions(s,{encode:J.function,serialize:J.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ne.assertOptions(n,{baseUrl:J.spelling("baseURL"),withXsrfToken:J.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&h.merge(o.common,o[n.method]);o&&h.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=q.concat(i,o);const c=[];let u=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(n)===!1||(u=u&&w.synchronous,c.unshift(w.fulfilled,w.rejected))});const a=[];this.interceptors.response.forEach(function(w){a.push(w.fulfilled,w.rejected)});let l,f=0,d;if(!u){const y=[Gt.bind(this),void 0];for(y.unshift(...c),y.push(...a),d=y.length,l=Promise.resolve(n);f<d;)l=l.then(y[f++],y[f++]);return l}d=c.length;let p=n;for(f=0;f<d;){const y=c[f++],w=c[f++];try{p=y(p)}catch(g){w.call(this,g);break}}try{l=Gt.call(this,p)}catch(y){return Promise.reject(y)}for(f=0,d=a.length;f<d;)l=l.then(a[f++],a[f++]);return l}getUri(t){t=ce(this.defaults,t);const n=$n(t.baseURL,t.url,t.allowAbsoluteUrls);return Fn(n,t.params,t.paramsSerializer)}};h.forEach(["delete","get","head","options"],function(t){se.prototype[t]=function(n,r){return this.request(ce(r||{},{method:t,url:n,data:(r||{}).data}))}});h.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(ce(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}se.prototype[t]=n(),se.prototype[t+"Form"]=n(!0)});let fo=class Xn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new ye(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Xn(function(s){t=s}),cancel:t}}};function ho(e){return function(n){return e.apply(null,n)}}function po(e){return h.isObject(e)&&e.isAxiosError===!0}const bt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bt).forEach(([e,t])=>{bt[t]=e});function Yn(e){const t=new se(e),n=Pn(se.prototype.request,t);return h.extend(n,se.prototype,t,{allOwnKeys:!0}),h.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Yn(ce(e,s))},n}const N=Yn(Le);N.Axios=se;N.CanceledError=ye;N.CancelToken=fo;N.isCancel=jn;N.VERSION=Qn;N.toFormData=$e;N.AxiosError=A;N.Cancel=N.CanceledError;N.all=function(t){return Promise.all(t)};N.spread=ho;N.isAxiosError=po;N.mergeConfig=ce;N.AxiosHeaders=q;N.formToJSON=e=>Vn(h.isHTMLForm(e)?new FormData(e):e);N.getAdapter=Gn.getAdapter;N.HttpStatusCode=bt;N.default=N;const{Axios:Ni,AxiosError:Hi,CanceledError:Bi,isCancel:Ii,CancelToken:Ui,VERSION:Fi,all:Di,Cancel:Vi,isAxiosError:ji,spread:qi,toFormData:$i,AxiosHeaders:Ki,HttpStatusCode:Wi,formToJSON:Ji,getAdapter:Gi,mergeConfig:Qi}=N,mo="modulepreload",yo=function(e,t){return new URL(e,t).href},Xt={},ue=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link"),c=document.querySelector("meta[property=csp-nonce]"),u=(c==null?void 0:c.nonce)||(c==null?void 0:c.getAttribute("nonce"));s=Promise.allSettled(n.map(a=>{if(a=yo(a,r),a in Xt)return;Xt[a]=!0;const l=a.endsWith(".css"),f=l?'[rel="stylesheet"]':"";if(!!r)for(let y=i.length-1;y>=0;y--){const w=i[y];if(w.href===a&&(!l||w.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${f}`))return;const p=document.createElement("link");if(p.rel=l?"stylesheet":mo,l||(p.as="script"),p.crossOrigin="",p.href=a,u&&p.setAttribute("nonce",u),document.head.appendChild(p),l)return new Promise((y,w)=>{p.addEventListener("load",y),p.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${a}`)))})}))}function o(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return s.then(i=>{for(const c of i||[])c.status==="rejected"&&o(c.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const fe=typeof document<"u";function Zn(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function go(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Zn(e.default)}const L=Object.assign;function ct(e,t){const n={};for(const r in t){const s=t[r];n[r]=K(s)?s.map(e):e(s)}return n}const Re=()=>{},K=Array.isArray,er=/#/g,wo=/&/g,bo=/\//g,vo=/=/g,Eo=/\?/g,tr=/\+/g,So=/%5B/g,Ro=/%5D/g,nr=/%5E/g,Oo=/%60/g,rr=/%7B/g,Ao=/%7C/g,sr=/%7D/g,_o=/%20/g;function xt(e){return encodeURI(""+e).replace(Ao,"|").replace(So,"[").replace(Ro,"]")}function To(e){return xt(e).replace(rr,"{").replace(sr,"}").replace(nr,"^")}function vt(e){return xt(e).replace(tr,"%2B").replace(_o,"+").replace(er,"%23").replace(wo,"%26").replace(Oo,"`").replace(rr,"{").replace(sr,"}").replace(nr,"^")}function Po(e){return vt(e).replace(vo,"%3D")}function Co(e){return xt(e).replace(er,"%23").replace(Eo,"%3F")}function xo(e){return e==null?"":Co(e).replace(bo,"%2F")}function _e(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Lo=/\/$/,ko=e=>e.replace(Lo,"");function at(e,t,n="/"){let r,s={},o="",i="";const c=t.indexOf("#");let u=t.indexOf("?");return c<u&&c>=0&&(u=-1),u>-1&&(r=t.slice(0,u),o=t.slice(u+1,c>-1?c:t.length),s=e(o)),c>-1&&(r=r||t.slice(0,c),i=t.slice(c,t.length)),r=Ho(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:_e(i)}}function Mo(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Yt(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function zo(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&he(t.matched[r],n.matched[s])&&or(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function he(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function or(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!No(e[n],t[n]))return!1;return!0}function No(e,t){return K(e)?Zt(e,t):K(t)?Zt(t,e):e===t}function Zt(e,t){return K(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Ho(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,c;for(i=0;i<r.length;i++)if(c=r[i],c!==".")if(c==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const ee={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Te;(function(e){e.pop="pop",e.push="push"})(Te||(Te={}));var Oe;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Oe||(Oe={}));function Bo(e){if(!e)if(fe){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ko(e)}const Io=/^[^#]+#/;function Uo(e,t){return e.replace(Io,"#")+t}function Fo(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Je=()=>({left:window.scrollX,top:window.scrollY});function Do(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Fo(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function en(e,t){return(history.state?history.state.position-t:-1)+e}const Et=new Map;function Vo(e,t){Et.set(e,t)}function jo(e){const t=Et.get(e);return Et.delete(e),t}let qo=()=>location.protocol+"//"+location.host;function ir(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let c=s.includes(e.slice(o))?e.slice(o).length:1,u=s.slice(c);return u[0]!=="/"&&(u="/"+u),Yt(u,"")}return Yt(n,e)+r+s}function $o(e,t,n,r){let s=[],o=[],i=null;const c=({state:d})=>{const p=ir(e,location),y=n.value,w=t.value;let g=0;if(d){if(n.value=p,t.value=d,i&&i===y){i=null;return}g=w?d.position-w.position:0}else r(p);s.forEach(O=>{O(n.value,y,{delta:g,type:Te.pop,direction:g?g>0?Oe.forward:Oe.back:Oe.unknown})})};function u(){i=n.value}function a(d){s.push(d);const p=()=>{const y=s.indexOf(d);y>-1&&s.splice(y,1)};return o.push(p),p}function l(){const{history:d}=window;d.state&&d.replaceState(L({},d.state,{scroll:Je()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:u,listen:a,destroy:f}}function tn(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Je():null}}function Ko(e){const{history:t,location:n}=window,r={value:ir(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(u,a,l){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+u:qo()+e+u;try{t[l?"replaceState":"pushState"](a,"",d),s.value=a}catch(p){console.error(p),n[l?"replace":"assign"](d)}}function i(u,a){const l=L({},t.state,tn(s.value.back,u,s.value.forward,!0),a,{position:s.value.position});o(u,l,!0),r.value=u}function c(u,a){const l=L({},s.value,t.state,{forward:u,scroll:Je()});o(l.current,l,!0);const f=L({},tn(r.value,u,null),{position:l.position+1},a);o(u,f,!1),r.value=u}return{location:r,state:s,push:c,replace:i}}function Wo(e){e=Bo(e);const t=Ko(e),n=$o(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=L({location:"",base:e,go:r,createHref:Uo.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Jo(e){return typeof e=="string"||e&&typeof e=="object"}function cr(e){return typeof e=="string"||typeof e=="symbol"}const ar=Symbol("");var nn;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(nn||(nn={}));function de(e,t){return L(new Error,{type:e,[ar]:!0},t)}function X(e,t){return e instanceof Error&&ar in e&&(t==null||!!(e.type&t))}const rn="[^/]+?",Go={sensitive:!1,strict:!1,start:!0,end:!0},Qo=/[.+*?^${}()[\]/\\]/g;function Xo(e,t){const n=L({},Go,t),r=[];let s=n.start?"^":"";const o=[];for(const a of e){const l=a.length?[]:[90];n.strict&&!a.length&&(s+="/");for(let f=0;f<a.length;f++){const d=a[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(s+="/"),s+=d.value.replace(Qo,"\\$&"),p+=40;else if(d.type===1){const{value:y,repeatable:w,optional:g,regexp:O}=d;o.push({name:y,repeatable:w,optional:g});const E=O||rn;if(E!==rn){p+=10;try{new RegExp(`(${E})`)}catch(z){throw new Error(`Invalid custom RegExp for param "${y}" (${E}): `+z.message)}}let R=w?`((?:${E})(?:/(?:${E}))*)`:`(${E})`;f||(R=g&&a.length<2?`(?:/${R})`:"/"+R),g&&(R+="?"),s+=R,p+=20,g&&(p+=-8),w&&(p+=-20),E===".*"&&(p+=-50)}l.push(p)}r.push(l)}if(n.strict&&n.end){const a=r.length-1;r[a][r[a].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function c(a){const l=a.match(i),f={};if(!l)return null;for(let d=1;d<l.length;d++){const p=l[d]||"",y=o[d-1];f[y.name]=p&&y.repeatable?p.split("/"):p}return f}function u(a){let l="",f=!1;for(const d of e){(!f||!l.endsWith("/"))&&(l+="/"),f=!1;for(const p of d)if(p.type===0)l+=p.value;else if(p.type===1){const{value:y,repeatable:w,optional:g}=p,O=y in a?a[y]:"";if(K(O)&&!w)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const E=K(O)?O.join("/"):O;if(!E)if(g)d.length<2&&(l.endsWith("/")?l=l.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);l+=E}}return l||"/"}return{re:i,score:r,keys:o,parse:c,stringify:u}}function Yo(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function lr(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Yo(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(sn(r))return 1;if(sn(s))return-1}return s.length-r.length}function sn(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Zo={type:0,value:""},ei=/[a-zA-Z0-9_]/;function ti(e){if(!e)return[[]];if(e==="/")return[[Zo]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${a}": ${p}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let c=0,u,a="",l="";function f(){a&&(n===0?o.push({type:0,value:a}):n===1||n===2||n===3?(o.length>1&&(u==="*"||u==="+")&&t(`A repeatable param (${a}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:a,regexp:l,repeatable:u==="*"||u==="+",optional:u==="*"||u==="?"})):t("Invalid state to consume buffer"),a="")}function d(){a+=u}for(;c<e.length;){if(u=e[c++],u==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:u==="/"?(a&&f(),i()):u===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:u==="("?n=2:ei.test(u)?d():(f(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&c--);break;case 2:u===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+u:n=3:l+=u;break;case 3:f(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&c--,l="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${a}"`),f(),i(),s}function ni(e,t,n){const r=Xo(ti(e.path),n),s=L(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function ri(e,t){const n=[],r=new Map;t=ln({strict:!1,end:!0,sensitive:!1},t);function s(f){return r.get(f)}function o(f,d,p){const y=!p,w=cn(f);w.aliasOf=p&&p.record;const g=ln(t,f),O=[w];if("alias"in f){const z=typeof f.alias=="string"?[f.alias]:f.alias;for(const C of z)O.push(cn(L({},w,{components:p?p.record.components:w.components,path:C,aliasOf:p?p.record:w})))}let E,R;for(const z of O){const{path:C}=z;if(d&&C[0]!=="/"){const I=d.record.path,k=I[I.length-1]==="/"?"":"/";z.path=d.record.path+(C&&k+C)}if(E=ni(z,d,g),p?p.alias.push(E):(R=R||E,R!==E&&R.alias.push(E),y&&f.name&&!an(E)&&i(f.name)),ur(E)&&u(E),w.children){const I=w.children;for(let k=0;k<I.length;k++)o(I[k],E,p&&p.children[k])}p=p||E}return R?()=>{i(R)}:Re}function i(f){if(cr(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function c(){return n}function u(f){const d=ii(f,n);n.splice(d,0,f),f.record.name&&!an(f)&&r.set(f.record.name,f)}function a(f,d){let p,y={},w,g;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw de(1,{location:f});g=p.record.name,y=L(on(d.params,p.keys.filter(R=>!R.optional).concat(p.parent?p.parent.keys.filter(R=>R.optional):[]).map(R=>R.name)),f.params&&on(f.params,p.keys.map(R=>R.name))),w=p.stringify(y)}else if(f.path!=null)w=f.path,p=n.find(R=>R.re.test(w)),p&&(y=p.parse(w),g=p.record.name);else{if(p=d.name?r.get(d.name):n.find(R=>R.re.test(d.path)),!p)throw de(1,{location:f,currentLocation:d});g=p.record.name,y=L({},d.params,f.params),w=p.stringify(y)}const O=[];let E=p;for(;E;)O.unshift(E.record),E=E.parent;return{name:g,path:w,params:y,matched:O,meta:oi(O)}}e.forEach(f=>o(f));function l(){n.length=0,r.clear()}return{addRoute:o,resolve:a,removeRoute:i,clearRoutes:l,getRoutes:c,getRecordMatcher:s}}function on(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function cn(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:si(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function si(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function an(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function oi(e){return e.reduce((t,n)=>L(t,n.meta),{})}function ln(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function ii(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;lr(e,t[o])<0?r=o:n=o+1}const s=ci(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function ci(e){let t=e;for(;t=t.parent;)if(ur(t)&&lr(e,t)===0)return t}function ur({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function ai(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(tr," "),i=o.indexOf("="),c=_e(i<0?o:o.slice(0,i)),u=i<0?null:_e(o.slice(i+1));if(c in t){let a=t[c];K(a)||(a=t[c]=[a]),a.push(u)}else t[c]=u}return t}function un(e){let t="";for(let n in e){const r=e[n];if(n=Po(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(K(r)?r.map(o=>o&&vt(o)):[r&&vt(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function li(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=K(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const ui=Symbol(""),fn=Symbol(""),Ge=Symbol(""),fr=Symbol(""),St=Symbol("");function ve(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ne(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((c,u)=>{const a=d=>{d===!1?u(de(4,{from:n,to:t})):d instanceof Error?u(d):Jo(d)?u(de(2,{from:t,to:d})):(i&&r.enterCallbacks[s]===i&&typeof d=="function"&&i.push(d),c())},l=o(()=>e.call(r&&r.instances[s],t,n,a));let f=Promise.resolve(l);e.length<3&&(f=f.then(a)),f.catch(d=>u(d))})}function lt(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const c in i.components){let u=i.components[c];if(!(t!=="beforeRouteEnter"&&!i.instances[c]))if(Zn(u)){const l=(u.__vccOpts||u)[t];l&&o.push(ne(l,n,r,i,c,s))}else{let a=u();o.push(()=>a.then(l=>{if(!l)throw new Error(`Couldn't resolve component "${c}" at "${i.path}"`);const f=go(l)?l.default:l;i.mods[c]=l,i.components[c]=f;const p=(f.__vccOpts||f)[t];return p&&ne(p,n,r,i,c,s)()}))}}return o}function hn(e){const t=ie(Ge),n=ie(fr),r=$(()=>{const u=Ee(e.to);return t.resolve(u)}),s=$(()=>{const{matched:u}=r.value,{length:a}=u,l=u[a-1],f=n.matched;if(!l||!f.length)return-1;const d=f.findIndex(he.bind(null,l));if(d>-1)return d;const p=dn(u[a-2]);return a>1&&dn(l)===p&&f[f.length-1].path!==p?f.findIndex(he.bind(null,u[a-2])):d}),o=$(()=>s.value>-1&&mi(n.params,r.value.params)),i=$(()=>s.value>-1&&s.value===n.matched.length-1&&or(n.params,r.value.params));function c(u={}){if(pi(u)){const a=t[Ee(e.replace)?"replace":"push"](Ee(e.to)).catch(Re);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>a),a}return Promise.resolve()}return{route:r,href:$(()=>r.value.href),isActive:o,isExactActive:i,navigate:c}}function fi(e){return e.length===1?e[0]:e}const hi=_t({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:hn,setup(e,{slots:t}){const n=gn(hn(e)),{options:r}=ie(Ge),s=$(()=>({[pn(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[pn(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&fi(t.default(n));return e.custom?o:Rn("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),di=hi;function pi(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function mi(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!K(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function dn(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const pn=(e,t,n)=>e??t??n,yi=_t({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ie(St),s=$(()=>e.route||r.value),o=ie(fn,0),i=$(()=>{let a=Ee(o);const{matched:l}=s.value;let f;for(;(f=l[a])&&!f.components;)a++;return a}),c=$(()=>s.value.matched[i.value]);tt(fn,$(()=>i.value+1)),tt(ui,c),tt(St,s);const u=At();return vn(()=>[u.value,c.value,e.name],([a,l,f],[d,p,y])=>{l&&(l.instances[f]=a,p&&p!==l&&a&&a===d&&(l.leaveGuards.size||(l.leaveGuards=p.leaveGuards),l.updateGuards.size||(l.updateGuards=p.updateGuards))),a&&l&&(!p||!he(l,p)||!d)&&(l.enterCallbacks[f]||[]).forEach(w=>w(a))},{flush:"post"}),()=>{const a=s.value,l=e.name,f=c.value,d=f&&f.components[l];if(!d)return mn(n.default,{Component:d,route:a});const p=f.props[l],y=p?p===!0?a.params:typeof p=="function"?p(a):p:null,g=Rn(d,L({},y,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[l]=null)},ref:u}));return mn(n.default,{Component:g,route:a})||g}}});function mn(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const gi=yi;function wi(e){const t=ri(e.routes,e),n=e.parseQuery||ai,r=e.stringifyQuery||un,s=e.history,o=ve(),i=ve(),c=ve(),u=Er(ee);let a=ee;fe&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=ct.bind(null,m=>""+m),f=ct.bind(null,xo),d=ct.bind(null,_e);function p(m,v){let b,S;return cr(m)?(b=t.getRecordMatcher(m),S=v):S=m,t.addRoute(S,b)}function y(m){const v=t.getRecordMatcher(m);v&&t.removeRoute(v)}function w(){return t.getRoutes().map(m=>m.record)}function g(m){return!!t.getRecordMatcher(m)}function O(m,v){if(v=L({},v||u.value),typeof m=="string"){const _=at(n,m,v.path),B=t.resolve({path:_.path},v),we=s.createHref(_.fullPath);return L(_,B,{params:d(B.params),hash:_e(_.hash),redirectedFrom:void 0,href:we})}let b;if(m.path!=null)b=L({},m,{path:at(n,m.path,v.path).path});else{const _=L({},m.params);for(const B in _)_[B]==null&&delete _[B];b=L({},m,{params:f(_)}),v.params=f(v.params)}const S=t.resolve(b,v),M=m.hash||"";S.params=l(d(S.params));const H=Mo(r,L({},m,{hash:To(M),path:S.path})),P=s.createHref(H);return L({fullPath:H,hash:M,query:r===un?li(m.query):m.query||{}},S,{redirectedFrom:void 0,href:P})}function E(m){return typeof m=="string"?at(n,m,u.value.path):L({},m)}function R(m,v){if(a!==m)return de(8,{from:v,to:m})}function z(m){return k(m)}function C(m){return z(L(E(m),{replace:!0}))}function I(m){const v=m.matched[m.matched.length-1];if(v&&v.redirect){const{redirect:b}=v;let S=typeof b=="function"?b(m):b;return typeof S=="string"&&(S=S.includes("?")||S.includes("#")?S=E(S):{path:S},S.params={}),L({query:m.query,hash:m.hash,params:S.path!=null?{}:m.params},S)}}function k(m,v){const b=a=O(m),S=u.value,M=m.state,H=m.force,P=m.replace===!0,_=I(b);if(_)return k(L(E(_),{state:typeof _=="object"?L({},M,_.state):M,force:H,replace:P}),v||b);const B=b;B.redirectedFrom=v;let we;return!H&&zo(r,S,b)&&(we=de(16,{to:B,from:S}),Mt(S,S,!0,!1)),(we?Promise.resolve(we):D(B,S)).catch(V=>X(V)?X(V,2)?V:Ye(V):Xe(V,B,S)).then(V=>{if(V){if(X(V,2))return k(L({replace:P},E(V.to),{state:typeof V.to=="object"?L({},M,V.to.state):M,force:H}),v||B)}else V=Y(B,S,!0,P,M);return G(B,S,V),V})}function x(m,v){const b=R(m,v);return b?Promise.reject(b):Promise.resolve()}function T(m){const v=ke.values().next().value;return v&&typeof v.runWithContext=="function"?v.runWithContext(m):m()}function D(m,v){let b;const[S,M,H]=bi(m,v);b=lt(S.reverse(),"beforeRouteLeave",m,v);for(const _ of S)_.leaveGuards.forEach(B=>{b.push(ne(B,m,v))});const P=x.bind(null,m,v);return b.push(P),ae(b).then(()=>{b=[];for(const _ of o.list())b.push(ne(_,m,v));return b.push(P),ae(b)}).then(()=>{b=lt(M,"beforeRouteUpdate",m,v);for(const _ of M)_.updateGuards.forEach(B=>{b.push(ne(B,m,v))});return b.push(P),ae(b)}).then(()=>{b=[];for(const _ of H)if(_.beforeEnter)if(K(_.beforeEnter))for(const B of _.beforeEnter)b.push(ne(B,m,v));else b.push(ne(_.beforeEnter,m,v));return b.push(P),ae(b)}).then(()=>(m.matched.forEach(_=>_.enterCallbacks={}),b=lt(H,"beforeRouteEnter",m,v,T),b.push(P),ae(b))).then(()=>{b=[];for(const _ of i.list())b.push(ne(_,m,v));return b.push(P),ae(b)}).catch(_=>X(_,8)?_:Promise.reject(_))}function G(m,v,b){c.list().forEach(S=>T(()=>S(m,v,b)))}function Y(m,v,b,S,M){const H=R(m,v);if(H)return H;const P=v===ee,_=fe?history.state:{};b&&(S||P?s.replace(m.fullPath,L({scroll:P&&_&&_.scroll},M)):s.push(m.fullPath,M)),u.value=m,Mt(m,v,b,P),Ye()}let Q;function Qe(){Q||(Q=s.listen((m,v,b)=>{if(!zt.listening)return;const S=O(m),M=I(S);if(M){k(L(M,{replace:!0,force:!0}),S).catch(Re);return}a=S;const H=u.value;fe&&Vo(en(H.fullPath,b.delta),Je()),D(S,H).catch(P=>X(P,12)?P:X(P,2)?(k(L(E(P.to),{force:!0}),S).then(_=>{X(_,20)&&!b.delta&&b.type===Te.pop&&s.go(-1,!1)}).catch(Re),Promise.reject()):(b.delta&&s.go(-b.delta,!1),Xe(P,S,H))).then(P=>{P=P||Y(S,H,!1),P&&(b.delta&&!X(P,8)?s.go(-b.delta,!1):b.type===Te.pop&&X(P,20)&&s.go(-1,!1)),G(S,H,P)}).catch(Re)}))}let ge=ve(),Z=ve(),U;function Xe(m,v,b){Ye(m);const S=Z.list();return S.length?S.forEach(M=>M(m,v,b)):console.error(m),Promise.reject(m)}function dr(){return U&&u.value!==ee?Promise.resolve():new Promise((m,v)=>{ge.add([m,v])})}function Ye(m){return U||(U=!m,Qe(),ge.list().forEach(([v,b])=>m?b(m):v()),ge.reset()),m}function Mt(m,v,b,S){const{scrollBehavior:M}=e;if(!fe||!M)return Promise.resolve();const H=!b&&jo(en(m.fullPath,0))||(S||!b)&&history.state&&history.state.scroll||null;return En().then(()=>M(m,v,H)).then(P=>P&&Do(P)).catch(P=>Xe(P,m,v))}const Ze=m=>s.go(m);let et;const ke=new Set,zt={currentRoute:u,listening:!0,addRoute:p,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:g,getRoutes:w,resolve:O,options:e,push:z,replace:C,go:Ze,back:()=>Ze(-1),forward:()=>Ze(1),beforeEach:o.add,beforeResolve:i.add,afterEach:c.add,onError:Z.add,isReady:dr,install(m){const v=this;m.component("RouterLink",di),m.component("RouterView",gi),m.config.globalProperties.$router=v,Object.defineProperty(m.config.globalProperties,"$route",{enumerable:!0,get:()=>Ee(u)}),fe&&!et&&u.value===ee&&(et=!0,z(s.location).catch(M=>{}));const b={};for(const M in ee)Object.defineProperty(b,M,{get:()=>u.value[M],enumerable:!0});m.provide(Ge,v),m.provide(fr,Sr(b)),m.provide(St,u);const S=m.unmount;ke.add(m),m.unmount=function(){ke.delete(m),ke.size<1&&(a=ee,Q&&Q(),Q=null,u.value=ee,et=!1,U=!1),S()}}};function ae(m){return m.reduce((v,b)=>v.then(()=>T(b)),Promise.resolve())}return zt}function bi(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const c=t.matched[i];c&&(e.matched.find(a=>he(a,c))?r.push(c):n.push(c));const u=e.matched[i];u&&(t.matched.find(a=>he(a,u))||s.push(u))}return[n,r,s]}function Xi(){return ie(Ge)}var oe=(e=>(e.BASE_LOGIN="/login",e.BASE_HOME="/",e.ERROR_404="/404",e))(oe||{});const vi=()=>typeof window<"u"&&window.location.port==="4173",Ei=vi()?"/publishedpreview":"/meta2d",Si=[{path:"/",redirect:Ei},{path:"/preview",name:"Preview",component:()=>ue(()=>import("./Preview-DUc2K0YM.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]),import.meta.url),meta:{title:"预览模式"}},{path:"/publishedpreview",name:"PublishedPreview",component:()=>ue(()=>import("./PublishedPreview-DKClmfRN.js"),__vite__mapDeps([8,1,2,3,4,5,9,7]),import.meta.url),meta:{title:"发布模式",requiresAuth:!1}},{path:"/login",name:"Login",component:()=>ue(()=>import("./Login-BXEafrT4.js"),__vite__mapDeps([10,2,4,11,7,12,13]),import.meta.url),meta:{title:"登录",requiresAuth:!1}},{path:"/meta2d",name:"Meta2D",component:()=>ue(()=>import("./Meta2D-ETuptM_u.js").then(e=>e.Q),__vite__mapDeps([14,2,1,3,4,5,15,7,12,13]),import.meta.url),meta:{title:"Meta2D画布"}},{path:"/shortcuts",name:"Shortcuts",component:()=>ue(()=>import("./Shortcuts-CiJ7xsuO.js"),__vite__mapDeps([16,4,2,17]),import.meta.url),meta:{title:"快捷键指南"}},{path:"/forgot-password",name:"ForgotPassword",component:()=>ue(()=>import("./ForgetPassword-CZG4B7un.js"),__vite__mapDeps([18,2,4,19,7,12]),import.meta.url),meta:{title:"忘记密码",requiresAuth:!1}}],pe=wi({history:Wo(),routes:Si});pe.beforeEach(async(e,t,n)=>{var i;(i=e.meta)!=null&&i.title&&(document.title=e.meta.title);const r=Fe(),s=e.meta.requiresAuth!==!1,o=await r.checkLoginStatus();if(s&&!o){n({path:oe.BASE_LOGIN,query:{redirect:e.fullPath}});return}if(o&&e.path===oe.BASE_LOGIN){n(oe.BASE_HOME);return}n()});var Lt=(e=>(e.TOKEN_KEY="TOKEN_KEY",e.USER_INFO_KEY="USER_INFO_KEY",e))(Lt||{});const{TOKEN_KEY:Ue}=Lt;function ut(){const e=localStorage.getItem(Ue);if(e)return e||null}function Ri(e){e?localStorage.setItem(Ue,e):localStorage.removeItem(Ue)}function yn(){localStorage.removeItem(Ue)}const Fe=Hr("app-user",{state:()=>({userInfo:null,token:"",roleList:[],dictItems:null,sessionTimeout:!1,lastUpdateTime:0,tenantid:"",shareTenantId:null,loginInfo:null}),getters:{isLogin(){return!!this.token||!!ut()},getUserInfo(){return this.userInfo||{}},getToken(){return this.token||ut()||""},getRoleList(){return this.roleList},getTenantId(){return this.tenantid||""},getShareTenantId(){return this.shareTenantId||""},getLoginInfo(){return this.loginInfo||null},getDictItems(){return this.dictItems||null},getSessionTimeout(){return!!this.sessionTimeout},getLastUpdateTime(){return this.lastUpdateTime}},actions:{setToken(e){this.token=e??"",Ri(e)},setUserInfo(e){this.userInfo=e},setLoginInfo(e){this.loginInfo=e,this.lastUpdateTime=new Date().getTime()},setTenant(e){this.tenantid=e},setTimeout(e){this.sessionTimeout=e},setSessionTimeout(e){this.sessionTimeout=e},async login(e){try{const t=await Ai(e);return this.setToken(t.token),this.setUserInfo(t.userInfo),t}catch(t){throw t}},async fetchUserInfo(){try{const e=await ft();return this.userInfo=e,e}catch(e){throw e}},async checkLoginStatus(){try{const e=ut();if(!e)return this.token="",this.userInfo=null,!1;const t=await ft();return this.userInfo=t,this.token=e,this.lastUpdateTime=new Date().getTime(),!0}catch(e){return console.error("检查登录状态失败:",e),this.token="",this.userInfo=null,yn(),!1}},async afterLoginAction(e,t){if(!this.token)return null;console.log("此时地token为",this.token);const n=await ft();return this.sessionTimeout?this.setSessionTimeout(!1):(await this.setLoginInfo({...t,isLogin:!0}),Br("ticket")?e&&window.location.replace(n&&n.homePath||oe.BASE_HOME):n&&n.userIdentity===2?(console.log("当前登录的用户信息:",n),e&&await pe.push("/guide")):e&&await pe.replace(n&&n.homePath||oe.BASE_HOME)),t},async logout(){await _i(),this.setToken(null),this.userInfo=null,this.roleList=[],yn()}}});var Rt=(e=>(e.GET="GET",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE",e))(Rt||{}),Ot=(e=>(e[e.SUCCESS=200]="SUCCESS",e[e.ERROR=500]="ERROR",e[e.TIMEOUT=408]="TIMEOUT",e[e.UNAUTHORIZED=401]="UNAUTHORIZED",e[e.FORBIDDEN=403]="FORBIDDEN",e))(Ot||{}),hr=(e=>(e.JSON="application/json",e.FORM_URLENCODED="application/x-www-form-urlencoded",e.FORM_DATA="multipart/form-data",e))(hr||{});const{TOKEN_KEY:Yi}=Lt;let Oi=class{constructor(t){Nt(this,"instance");this.instance=N.create(t),this.instance.interceptors.request.use(n=>{const r=Fe().getToken;return r&&(n.headers["x-access-token"]=r),n.headers["Content-Type"]||(n.headers["Content-Type"]=hr.JSON),n},n=>Promise.reject(n)),this.instance.interceptors.response.use(n=>{if(n.config.responseType==="blob")return n;const{data:r}=n;return r.code===Ot.SUCCESS||r.success?r.result:(this.handleError(r),Promise.reject(r))},n=>{const{response:r}=n;return r?this.handleError(r.data):window.navigator.onLine||nt.error("网络连接已断开，请检查网络后重试"),Promise.reject(n)})}handleError(t){const{code:n,message:r}=t;switch(n){case Ot.TIMEOUT:case 401:Fe().logout(),pe.push({path:oe.BASE_LOGIN,query:{redirect:pe.currentRoute.value.fullPath}});break;case 403:nt.error("权限不足");break;default:nt.error(r||"请求失败")}}request(t){return this.instance.request(t)}get(t,n,r){return this.request({...r,url:t,method:Rt.GET,params:n})}post(t,n,r){return this.request({...r,url:t,method:Rt.POST,data:n})}};const kt=new Oi({baseURL:"http://localhost:8090/hcms",timeout:1e4,withCredentials:!0});function Ai(e){return kt.post("/sys/login",e)}function ft(){return kt.get("/sys/user/getUserInfo")}async function _i(){return kt.get("/sys/logout")}function Ti(){let e=document.location.protocol+"//"+window.location.host+"/";const t="http://localhost:8443/cas";async function n(){Fe().getToken,console.log("开始单点登录",e)}async function r(){window.location.href=t+"/logout?service="+encodeURIComponent(e)}return{ssoLogin:n,ssoLoginOut:r}}if(typeof window<"u"){let e=function(){var t=document.body,n=document.getElementById("__svg__icons__dom__");n||(n=document.createElementNS("http://www.w3.org/2000/svg","svg"),n.style.position="absolute",n.style.width="0",n.style.height="0",n.id="__svg__icons__dom__",n.setAttribute("xmlns","http://www.w3.org/2000/svg"),n.setAttribute("xmlns:link","http://www.w3.org/1999/xlink")),n.innerHTML='<symbol class="icon" viewBox="0 0 1024 1024"  id="i-addAnchor"><path d="M960 160h-96V64h-64v96h-96v64h96v96h64v-96h96z" /><path d="M560 176c0-26.5-21.5-48-48-48s-48 21.5-48 48v672c0 26.5 21.5 48 48 48s48-21.5 48-48V176z" fill="#FFF" /><path d="M512.6 128h1.3l.6.1h.6l.6.1h1.2l.*******.*******h.6l.6.2h.6l.*******.5.1.*******.*******.*******.5.1.*******.*******.*******.5.2.*******.5.3.*******.*******.5.3.5.3.5.3.5.2.5.4.5.2.5.4.5.3.4.3.5.3.5.4.5.3.4.4.5.3.4.3.5.4.5.4.4.3.4.4.4.4.5.4.4.4.4.4.5.4.4.4.4.4.4.4.4.4.4.4.3.5.4.4.4.4.3.5.4.4.4.5.3.4.4.5.3.5.4.4.3.5.3.5.3.4.3.5.4.5.3.5.3.*******.5.3.5.3.5.3.*******.*******.*******.5.3.6.2.*******.5.2.*******.*******.*******.5.1.*******.*******.5.2.*******.*******.6v.6l.1.6.1.6v.6l.1.6v.6l.1.6v677l-.1.6v.6l-.1.6v.6l-.1.6-.1.6v.6l-.1.6-.1.6-.1.6-.1.6-.2.6-.1.5-.1.6-.2.6-.1.6-.1.6-.2.5-.2.6-.2.6-.1.5-.2.6-.2.6-.2.5-.2.5-.2.6-.2.5-.3.6-.2.5-.2.6-.3.5-.2.5-.3.5-.2.5-.3.6-.3.5-.3.5-.3.5-.2.5-.3.5-.3.5-.4.5-.3.5-.3.4-.3.5-.3.5-.4.4-.3.5-.4.5-.3.4-.4.5-.4.4-.3.5-.4.4-.4.4-.3.5-.4.4-.4.4-.4.4-.4.4-.4.4-.5.4-.4.4-.4.4-.5.4-.4.4-.4.4-.4.3-.5.4-.5.4-.4.3-.5.3-.4.4-.5.3-.5.4-.5.3-.4.3-.5.3-.5.4-.5.2-.5.4-.5.2-.5.3-.5.3-.5.3-.5.3-.5.2-.6.3-.5.2-.5.3-.6.2-.5.2-.5.2-.6.3-.5.2-.6.2-.5.2-.6.2-.6.2-.5.1-.6.2-.5.2-.6.1-.6.2-.6.1-.6.2-.5.1-.6.1-.6.2h-.6l-.6.2h-.6l-.6.1-.6.1-.6.1-.6.1h-1.2l-.6.1h-.6l-.6.1h-3.7l-.6-.1h-.7l-.6-.1h-1.2l-.6-.1-.6-.1-.6-.1-.6-.1h-.6l-.6-.2h-.6l-.5-.2-.6-.1-.6-.1-.6-.2-.6-.1-.6-.2-.5-.1-.6-.2-.5-.2-.6-.1-.6-.2-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.5-.2-.6-.2-.5-.2-.5-.3-.6-.2-.5-.3-.5-.2-.6-.3-.4-.3-.6-.3-.5-.3-.5-.2-.5-.4-.5-.2-.4-.4-.5-.3-.5-.3-.5-.3-.5-.4-.4-.3-.5-.4-.5-.3-.4-.3-.5-.4-.4-.4-.5-.3-.4-.4-.4-.4-.4-.4-.5-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.5-.4-.4-.3-.4-.4-.5-.4-.4-.4-.5-.3-.4-.3-.5-.4-.5-.3-.4-.3-.5-.4-.5-.3-.4-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.2-.6-.3-.5-.2-.5-.3-.5-.3-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.6-.2-.5-.2-.5-.1-.6-.2-.6-.2-.5-.2-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.6-.1-.6-.1-.5-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6v-.6l-.1-.6v-.6l-.1-.6v-.6l-.1-.7V174.2l.1-.7v-.6l.1-.6v-.6l.1-.6v-.6l.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.5.1-.6.2-.6.1-.6.2-.6.1-.5.2-.6.2-.6.2-.5.2-.6.1-.6.2-.5.2-.5.3-.6.2-.5.2-.6.2-.5.2-.6.3-.5.3-.5.2-.5.3-.5.2-.6.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.4.4-.5.3-.5.3-.4.4-.5.3-.5.3-.4.4-.5.4-.4.4-.5.3-.4.4-.4.4-.5.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.5-.4.4-.4.4-.4.4-.4.5-.3.4-.4.5-.4.4-.3.5-.3.5-.4.4-.3.5-.4.5-.3.5-.3.5-.3.4-.4.5-.2.5-.4.5-.2.5-.3.6-.3.4-.3.6-.3.5-.2.5-.3.6-.2.5-.3.5-.2.6-.2.5-.2.5-.3.6-.2.5-.2.6-.2.5-.2.6-.2.6-.1.5-.2.6-.2.5-.1.6-.2.6-.1.6-.2.6-.1.6-.1.5-.2h.6l.6-.2h.6l.6-.1.6-.1.6-.1.6-.1h1.2l.6-.1h.7l.6-.1h1.8z" /><path d="M687.7 416c26.5 0 48-21.5 48-48s-21.5-48-48-48h-352c-26.5 0-48 21.5-48 48s21.5 48 48 48h352z" fill="#FFF" /><path d="M688.3 320h1.3l.6.1h.6l.6.1h1.2l.*******.*******h.6l.6.2h.6l.6.2.*******.5.2.6.1.*******.*******.6.1.*******.*******.*******.6.2.*******.5.3.*******.*******.5.3.5.3.5.3.5.2.5.4.5.2.5.4.5.3.5.3.4.3.5.4.5.3.4.4.5.3.5.3.4.4.4.4.5.3.4.4.5.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.5.4.4.4.4.4.5.3.4.4.5.3.4.4.5.3.5.3.4.4.5.3.5.3.4.4.5.3.5.3.5.3.5.3.*******.5.3.5.3.*******.*******.*******.*******.*******.*******.*******.5.1.*******.*******.*******.*******.*******.6.1.6v.6l.1.*******.6v.6l.1.6v1.2l.1.7v3.6l-.1.7v1.2l-.1.6v.6l-.1.6-.1.6-.1.6v.6l-.1.6-.1.6-.1.6-.1.6-.2.5-.1.6-.1.6-.2.6-.2.6-.1.5-.2.6-.1.6-.2.5-.2.6-.2.6-.2.5-.2.5-.2.6-.2.5-.2.6-.3.5-.2.6-.3.5-.2.5-.3.5-.2.5-.3.6-.3.5-.3.5-.2.5-.3.5-.3.5-.3.5-.3.5-.4.5-.3.4-.3.5-.4.5-.3.4-.3.5-.4.5-.3.4-.4.5-.3.4-.4.5-.4.4-.4.4-.4.5-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.5.4-.4.4-.5.3-.4.4-.4.4-.5.3-.5.3-.4.4-.5.3-.5.4-.4.3-.5.3-.5.3-.5.4-.5.2-.5.4-.5.2-.5.3-.5.3-.5.3-.5.3-.6.2-.5.3-.5.2-.5.3-.6.2-.5.2-.6.2-.5.3-.5.2-.6.2-.5.2-.6.2-.5.2-.6.1-.6.2-.6.2-.5.1-.6.2-.6.1-.5.2-.6.1-.6.1-.6.2h-.6l-.6.2h-.6l-.6.1-.6.1-.6.1-.6.1h-1.2l-.6.1h-.6l-.6.1H333.9l-.7-.1h-.6l-.6-.1h-1.2l-.6-.1-.6-.1-.6-.1-.6-.1h-.6l-.6-.2h-.6l-.6-.2-.5-.1-.6-.1-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.6-.2-.5-.1-.6-.2-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.6-.2-.5-.2-.5-.2-.6-.3-.5-.2-.5-.3-.5-.2-.5-.3-.6-.3-.5-.3-.5-.3-.5-.2-.5-.4-.4-.2-.6-.4-.4-.3-.5-.3-.5-.3-.4-.4-.5-.3-.5-.4-.4-.3-.5-.3-.5-.4-.4-.4-.4-.3-.5-.4-.4-.4-.5-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.5-.4-.4-.4-.4-.3-.5-.4-.4-.3-.5-.4-.4-.3-.5-.4-.5-.3-.4-.4-.5-.3-.5-.3-.4-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.2-.6-.3-.5-.3-.5-.2-.5-.2-.5-.3-.6-.2-.5-.2-.6-.3-.5-.2-.6-.2-.5-.2-.5-.2-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.5-.2-.6-.1-.6-.1-.6-.2-.6-.1-.5-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6v-.6l-.1-.6v-.6l-.1-.6v-1.3l-.1-.6v-2.4l.1-.6v-1.3l.1-.6v-.6l.1-.6v-.6l.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.5.2-.6.1-.6.1-.6.2-.6.2-.5.1-.6.2-.6.1-.5.2-.6.2-.6.2-.5.2-.5.2-.6.3-.5.2-.6.2-.5.3-.6.2-.5.2-.5.3-.5.3-.5.2-.6.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.4.3-.5.4-.5.3-.4.4-.5.3-.5.4-.4.3-.5.4-.4.3-.5.4-.4.4-.4.4-.5.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.5-.4.4-.4.5-.4.4-.3.4-.4.5-.4.5-.3.4-.3.5-.4.5-.3.4-.4.5-.3.5-.3.4-.3.6-.4.4-.2.5-.4.5-.2.5-.3.5-.3.6-.3.5-.3.5-.2.5-.3.5-.2.6-.3.5-.2.5-.2.6-.2.5-.3.6-.2.5-.2.6-.2.5-.2.6-.2.5-.1.6-.2.6-.2.6-.1.5-.2.6-.1.6-.2.6-.1.5-.1.6-.2h.6l.6-.2h.6l.6-.1.6-.1.6-.1.6-.1h1.2l.6-.1h.6l.7-.1h353.8zM848 512c-53 0-96 43-96 96 0 27.2 11.4 51.8 29.6 69.3-3 7.9-6.3 15.8-10 23.4-39.3 81.5-118.7 142.4-207.7 158.6-128.1 23.4-258.9-47.7-312.3-160.1-3.4-7.2-6.5-14.4-9.2-21.8 18.2-17.5 29.6-42.1 29.6-69.3 0-53-43-96-96-96s-96 43-96 96c0 43.4 28.9 80.2 68.5 92 53.2 155 207 260.5 364.7 260 145.8-.5 283.6-89.6 345.4-218.6 6.4-13.5 12-27.3 16.7-41.3 39.7-11.8 68.6-48.6 68.6-92C944 555 901 512 848 512zM176 656c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm672 0c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-address"><path d="M512 320c-11 0-20 9-20 20s9 20 20 20 20-9 20-20-9-20-20-20z" /><path d="M808.8 794c-42-18.8-97.2-32.8-160.8-41.4l143.2-218.8c39.6-57 60.6-124 60.6-193.8C852 152.6 699.4 0 512 0S172 152.6 172 340c0 69.8 21 136.8 60.6 193.8L376 752.6c-63.6 8.4-118.8 22.6-160.8 41.4-68.8 30.8-83.2 65.8-83.2 90 0 42 41.2 77.8 119.2 103.8 70 23.4 162.6 36.2 260.8 36.2s191-12.8 260.8-36.2C850.8 961.8 892 926 892 884c0-24.2-14.4-59.2-83.2-90zM266 511.8c-.2-.2-.2-.4-.4-.4-35-50.6-53.6-109.8-53.6-171.4 0-165.4 134.6-300 300-300s300 134.6 300 300c0 61.6-18.6 120.8-53.6 171.2-.2.2-.2.4-.4.4L512 887.4 266 511.8zM512 984c-210.6 0-340-58.2-340-100 0-34 85.2-78.2 228.4-94l95 145c3.6 5.6 10 9 16.8 9 6.8 0 13-3.4 16.8-9l95-145c143 15.8 228.4 60 228.4 94-.4 41.8-129.8 100-340.4 100z" /><path d="M512 240c-55.2 0-100 44.8-100 100s44.8 100 100 100 100-44.8 100-100-44.8-100-100-100zm0 160c-33 0-60-27-60-60s27-60 60-60 60 27 60 60-27 60-60 60z" /><path d="M752 320h-21c-9.6-105.4-93.8-189.6-199-199v-21c0-11-9-20-20-20s-20 9-20 20v21c-105.4 9.6-189.6 93.8-199 199h-21c-11 0-20 9-20 20s9 20 20 20h21c9.6 105.4 93.8 189.6 199 199v21c0 11 9 20 20 20s20-9 20-20v-21c105.4-9.6 189.6-93.8 199-199h21c11 0 20-9 20-20s-9-20-20-20zM512 520c-99.2 0-180-80.8-180-180s80.8-180 180-180 180 80.8 180 180-80.8 180-180 180z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-aim"><path d="M313.6 358.4H177.067c-17.067 0-32 14.933-32 32s14.933 32 32 32H390.4c4.267 0 8.533 0 10.667-2.133C409.6 416 416 411.733 418.133 403.2c2.134-4.267 2.134-8.533 2.134-10.667V179.2c0-17.067-14.934-32-32-32s-32 14.933-32 32v136.533L172.8 125.867c-12.8-12.8-32-12.8-44.8 0-12.8 12.8-12.8 32 0 44.8L313.6 358.4zm381.867 292.267H832c17.067 0 32-14.934 32-32s-14.933-32-32-32H618.667c-4.267 0-8.534 0-10.667 2.133-8.533 4.267-14.933 8.533-17.067 17.067-2.133 4.266-2.133 8.533-2.133 10.666v213.334c0 17.066 14.933 32 32 32s32-14.934 32-32V693.333l200.533 200.534c6.4 6.4 14.934 8.533 23.467 8.533s17.067-2.133 23.467-8.533c12.8-12.8 12.8-32 0-44.8l-204.8-198.4zm-260.267-44.8c-4.267-8.534-8.533-14.934-17.067-17.067-4.266-2.133-8.533-2.133-10.666-2.133H192c-17.067 0-32 14.933-32 32s14.933 32 32 32h136.533L128 851.2c-12.8 12.8-12.8 32 0 44.8 6.4 6.4 14.933 8.533 23.467 8.533s17.066-2.133 23.466-8.533l200.534-200.533V832c0 17.067 14.933 32 32 32s32-14.933 32-32V618.667c-2.134-4.267-2.134-8.534-4.267-12.8zM603.733 403.2c4.267 8.533 8.534 14.933 17.067 17.067 4.267 2.133 8.533 2.133 10.667 2.133H844.8c17.067 0 32-14.933 32-32s-14.933-32-32-32H708.267L896 170.667c12.8-12.8 12.8-32 0-44.8-12.8-12.8-32-12.8-44.8 0L663.467 313.6V177.067c0-17.067-14.934-32-32-32s-32 14.933-32 32V390.4c2.133 4.267 2.133 8.533 4.266 12.8z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-align-center"><path d="M460.16 1024V0h103.68v1024H460.16zM64 577.152h896v296.32H64v-296.32zm89.6 98.816v98.752h716.8v-98.752H153.6zm0-525.44h716.8v296.32H153.6v-296.32zm89.6 98.752v98.752h537.6V249.28H243.2z" fill="#262626" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-align-left"><path d="M85.312 0v1024H0V0h85.312zM1024 256H170.688V0H1024v256zm-85.312-85.312V85.312H256v85.376h682.688zM1024 1024H170.688V768H1024v256zm-85.312-85.312v-85.376H256v85.376h682.688zM853.312 640H170.688V384h682.624v256zM768 554.688v-85.376H256v85.376h512z" fill="#262626" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-align-right"><path d="M938.688 1024V0H1024v1024h-85.312zM0 768h853.312v256H0V768zm85.312 85.312v85.376H768v-85.376H85.312zM0 0h853.312v256H0V0zm85.312 85.312v85.376H768V85.312H85.312zM170.688 384h682.624v256H170.688V384zM256 469.312v85.376h512v-85.376H256z" fill="#262626" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-align"><path d="M128 640h426.667a42.667 42.667 0 0 0 0-85.333H128A42.667 42.667 0 0 0 128 640zm0-170.667h426.667a42.667 42.667 0 0 0 0-85.333H128a42.667 42.667 0 0 0 0 85.333zm0-170.666h768a42.667 42.667 0 0 0 0-85.334H128a42.667 42.667 0 0 0 0 85.334zm768 426.666H128a42.667 42.667 0 0 0 0 85.334h768a42.667 42.667 0 0 0 0-85.334zM837.973 408.32a42.667 42.667 0 1 0-54.613 65.28l46.08 38.4-46.08 38.4a42.667 42.667 0 0 0-5.547 60.16 42.667 42.667 0 0 0 32.854 15.36 42.667 42.667 0 0 0 27.306-10.24l85.334-70.827a42.667 42.667 0 0 0 0-65.706z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-border-bottom"><path d="M0 938.688h1024V1024H0v-85.312zM256 0v853.312H0V0h256zm-85.312 85.312H85.312V768h85.376V85.312zM1024 0v853.312H768V0h256zm-85.312 85.312h-85.376V768h85.376V85.312zM640 170.688v682.624H384V170.688h256zM554.688 256h-85.376v512h85.376V256z" fill="#262626" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-border-top"><path d="M1024 85.312H0V0h1024v85.312zM768 1024V170.688h256V1024H768zm85.312-85.312h85.376V256h-85.376v682.688zM0 1024V170.688h256V1024H0zm85.312-85.312h85.376V256H85.312v682.688zM384 853.312V170.688h256v682.624H384zM469.312 768h85.376V256h-85.376v512z" fill="#262626" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-combine"><path d="M891.8 65h-632c-35.5 0-64.4 28.8-64.4 64.4v125.3h-66.3c-35.5 0-64.4 28.8-64.4 64.4v575.6c0 35.5 28.8 64.4 64.4 64.4h572.6c35.5 0 64.4-28.8 64.4-64.4v-65.3h125.6c35.5 0 64.4-28.8 64.4-64.4V129.4c.1-35.6-28.7-64.4-64.3-64.4zM702.2 894.6v.1c0 .1-.2.2-.2.2H129.1c-.1 0-.2-.2-.2-.2V318.9c0-.1.2-.2.2-.2h.1l66.3.1S194 794.1 194 797.4c0 17.7 14.3 32 32 32h476.2v65.2zM259.5 129.2c0-.1.2-.2.2-.2h632.2c.1 0 .2.2.2.2v635.9c0 .1-.2.2-.2.2H259.5V129.2z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-delete"><path d="M768 928H257.28c-16.96 0-31.04-13.28-32-30.4L192 257.6c-.48-8.8 2.72-17.28 8.8-23.68 6.08-6.4 14.4-9.92 23.2-9.92h576c8.8 0 17.12 3.52 23.2 9.92 6.08 6.4 9.28 14.88 8.8 23.68l-32 640c-.96 16.96-14.88 30.4-32 30.4zm-480.32-64H737.6l28.8-576H257.76l29.92 576z" /><path d="M864 288H160c-17.6 0-32-14.4-32-32s14.4-32 32-32h704c17.6 0 32 14.4 32 32s-14.4 32-32 32zM672 160H352c-17.6 0-32-14.4-32-32s14.4-32 32-32h320c17.6 0 32 14.4 32 32s-14.4 32-32 32zM416 768c-16.96 0-31.04-13.28-32-30.4l-16-320c-.96-17.6 12.64-32.64 30.4-33.6 17.92-.8 32.64 12.64 33.6 30.4l16 320c.96 17.6-12.64 32.64-30.4 33.6H416zm192 0h-1.6c-17.6-.8-31.2-15.84-30.4-33.6l16-320c.8-17.6 16.16-30.88 33.6-30.4 17.6.96 31.2 15.84 30.4 33.6l-16 320c-.96 17.12-15.04 30.4-32 30.4z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-fullScreen"><path d="M149.333 394.667c17.067 0 32-14.934 32-32V226.133l187.734 187.734c6.4 6.4 14.933 8.533 23.466 8.533s17.067-2.133 23.467-8.533c12.8-12.8 12.8-32 0-44.8L228.267 181.333h134.4c17.066 0 32-14.933 32-32s-14.934-32-32-32H149.333c-4.266 0-8.533 0-10.666 2.134-8.534 4.266-14.934 10.666-19.2 17.066-2.134 4.267-2.134 8.534-2.134 12.8v213.334c0 17.066 14.934 32 32 32zm725.334 234.666c-17.067 0-32 14.934-32 32v136.534L642.133 597.333c-12.8-12.8-32-12.8-44.8 0s-12.8 32 0 44.8l200.534 200.534H661.333c-17.066 0-32 14.933-32 32s14.934 32 32 32h213.334c4.266 0 8.533 0 10.666-2.134 8.534-4.266 14.934-8.533 17.067-17.066 2.133-4.267 2.133-8.534 2.133-10.667V661.333c2.134-17.066-12.8-32-29.866-32zm-492.8-34.133L181.333 795.733v-134.4c0-17.066-14.933-32-32-32s-32 14.934-32 32v213.334c0 4.266 0 8.533 2.134 10.666 4.266 8.534 8.533 14.934 17.066 17.067 4.267 2.133 8.534 2.133 10.667 2.133h213.333c17.067 0 32-14.933 32-32s-14.933-32-32-32H224L424.533 640c12.8-12.8 12.8-32 0-44.8s-29.866-10.667-42.666 0zm522.666-456.533c0-2.134 0-2.134 0 0-4.266-8.534-10.666-14.934-17.066-17.067-4.267-2.133-8.534-2.133-10.667-2.133H661.333c-17.066 0-32 14.933-32 32s14.934 32 32 32h136.534L610.133 371.2c-12.8 12.8-12.8 32 0 44.8 6.4 6.4 14.934 8.533 23.467 8.533s17.067-2.133 23.467-8.533L844.8 228.267v134.4c0 17.066 14.933 32 32 32s32-14.934 32-32V149.333c-2.133-4.266-2.133-8.533-4.267-10.666z" /></symbol><symbol viewBox="0 0 1024 1024"  id="i-pen"><path d="M938.667 308.907a42.667 42.667 0 0 0-12.374-30.294L745.387 97.707a42.667 42.667 0 0 0-30.294-12.374A42.667 42.667 0 0 0 684.8 97.707L564.053 218.453 97.707 684.8a42.667 42.667 0 0 0-12.374 30.293V896A42.667 42.667 0 0 0 128 938.667h180.907a42.667 42.667 0 0 0 32.426-12.374L805.12 459.947l121.173-118.614a50.773 50.773 0 0 0 9.387-14.08 42.667 42.667 0 0 0 0-10.24 29.867 29.867 0 0 0 0-5.973zM291.413 853.333H170.667V732.587l423.68-423.68 120.746 120.746zm483.84-483.84L654.507 248.747l60.586-60.16 120.32 120.32z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-pencil"><path d="M734.4 622.4c-24 0-49.6-9.6-67.2-28.8L430.4 358.4c-35.2-35.2-38.4-89.6-6.4-128L569.6 49.6c6.4-6.4 14.4-11.2 24-11.2s17.6 3.2 24 9.6l361.6 361.6c6.4 6.4 9.6 16 9.6 24 0 9.6-4.8 17.6-11.2 24L796.8 603.2c-20.8 12.8-41.6 19.2-62.4 19.2zm-24-73.6c11.2 11.2 30.4 12.8 43.2 1.6l153.6-123.2-312-310.4L472 270.4c-9.6 12.8-9.6 30.4 1.6 43.2l236.8 235.2z" /><path d="M118.4 937.6c-8 0-16-3.2-22.4-9.6-6.4-6.4-9.6-14.4-9.6-24l11.2-440c0-14.4 9.6-25.6 22.4-30.4l350.4-102.4c11.2-3.2 24 0 32 8L683.2 520c8 8 11.2 20.8 8 32L588.8 902.4c-3.2 12.8-16 22.4-30.4 22.4l-440 12.8zm41.6-448-9.6 384 384-9.6L624 553.6 470.4 400 160 489.6z" /><path d="M353.6 766.4c-25.6 0-49.6-9.6-67.2-28.8-17.6-17.6-28.8-41.6-28.8-67.2s9.6-49.6 28.8-67.2c17.6-17.6 41.6-28.8 67.2-28.8s49.6 9.6 67.2 28.8c17.6 17.6 28.8 41.6 28.8 67.2s-9.6 49.6-28.8 67.2c-17.6 19.2-41.6 28.8-67.2 28.8zm0-128c-8 0-16 3.2-22.4 9.6-6.4 6.4-9.6 14.4-9.6 22.4s3.2 16 9.6 22.4c12.8 12.8 33.6 12.8 44.8 0 6.4-6.4 9.6-14.4 9.6-22.4s-3.2-16-9.6-22.4c-6.4-6.4-14.4-9.6-22.4-9.6z" /><path d="M140.8 915.2c-8 0-16-3.2-22.4-9.6-12.8-12.8-12.8-32 0-44.8l168-168c12.8-12.8 32-12.8 44.8 0 12.8 12.8 12.8 32 0 44.8l-168 168c-6.4 6.4-14.4 9.6-22.4 9.6z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-redo"><path d="M772.721 64v143.213l-47.738-47.738c-41.617-41.617-89.355-69.158-143.213-82.623-53.858-13.464-107.716-13.464-161.574 0s-100.984 40.393-141.377 80.787-67.934 87.519-82.623 141.377-14.689 107.716 0 161.574c14.689 53.858 41.618 101.596 80.787 143.213L636.852 960l47.738-47.738-356.197-359.869c-31.825-29.377-53.858-65.486-66.098-108.328-12.24-42.842-12.24-85.683 0-128.525s33.661-79.563 64.262-110.164 67.322-51.41 110.164-62.426 85.071-11.016 126.689 0c41.617 11.016 78.339 32.437 110.164 64.262l66.098 66.098H563.41v66.098h242.361l33.049-33.049V64h-66.099z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-refresh"><path d="M981.448 133.18a35.368 35.368 0 0 0-35.367 35.368v85.103A505.092 505.092 0 0 0 6.63 474.698a35.368 35.368 0 0 0 32.605 38.13 35.368 35.368 0 0 0 35.367-32.603 434.357 434.357 0 0 1 819.532-165.786H800.19a35.368 35.368 0 1 0 0 71.288h181.258a35.368 35.368 0 0 0 35.368-35.368V168.55a35.368 35.368 0 0 0-35.368-35.368zm0 379.096a35.368 35.368 0 0 0-38.13 32.605 434.357 434.357 0 0 1-819.532 165.785H223.81a35.368 35.368 0 1 0 0-71.288H42.552a35.368 35.368 0 0 0-35.368 35.368v181.258a35.368 35.368 0 1 0 71.288 0V770.35a505.092 505.092 0 0 0 939.45-221.047 35.368 35.368 0 0 0-34.816-37.026z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-removeAnchor"><path d="m945.2 124.1-45.3-45.2-67.9 67.9-67.9-67.9-45.2 45.2 67.9 67.9-67.9 67.9 45.2 45.2 67.9-67.9 67.9 67.9 45.3-45.2-67.9-67.9z" /><path d="M560 176c0-26.5-21.5-48-48-48s-48 21.5-48 48v672c0 26.5 21.5 48 48 48s48-21.5 48-48V176z" fill="#FFF" /><path d="M512.6 128h1.3l.6.1h.6l.6.1h1.2l.*******.*******h.6l.6.2h.6l.*******.5.1.*******.*******.*******.5.1.*******.*******.*******.5.2.*******.5.3.*******.*******.5.3.5.3.5.3.5.2.5.4.5.2.5.4.5.3.4.3.5.3.5.4.5.3.4.4.5.3.4.3.5.4.5.4.4.3.4.4.4.4.5.4.4.4.4.4.5.4.4.4.4.4.4.4.4.4.4.4.3.5.4.4.4.4.3.5.4.4.4.5.3.4.4.5.3.5.4.4.3.5.3.5.3.4.3.5.4.5.3.5.3.*******.5.3.5.3.5.3.*******.*******.*******.5.3.6.2.*******.5.2.*******.*******.*******.5.1.*******.*******.5.2.*******.*******.6v.6l.1.6.1.6v.6l.1.6v.6l.1.6v677l-.1.6v.6l-.1.6v.6l-.1.6-.1.6v.6l-.1.6-.1.6-.1.6-.1.6-.2.6-.1.5-.1.6-.2.6-.1.6-.1.6-.2.5-.2.6-.2.6-.1.5-.2.6-.2.6-.2.5-.2.5-.2.6-.2.5-.3.6-.2.5-.2.6-.3.5-.2.5-.3.5-.2.5-.3.6-.3.5-.3.5-.3.5-.2.5-.3.5-.3.5-.4.5-.3.5-.3.4-.3.5-.3.5-.4.4-.3.5-.4.5-.3.4-.4.5-.4.4-.3.5-.4.4-.4.4-.3.5-.4.4-.4.4-.4.4-.4.4-.4.4-.5.4-.4.4-.4.4-.5.4-.4.4-.4.4-.4.3-.5.4-.5.4-.4.3-.5.3-.4.4-.5.3-.5.4-.5.3-.4.3-.5.3-.5.4-.5.2-.5.4-.5.2-.5.3-.5.3-.5.3-.5.3-.5.2-.6.3-.5.2-.5.3-.6.2-.5.2-.5.2-.6.3-.5.2-.6.2-.5.2-.6.2-.6.2-.5.1-.6.2-.5.2-.6.1-.6.2-.6.1-.6.2-.5.1-.6.1-.6.2h-.6l-.6.2h-.6l-.6.1-.6.1-.6.1-.6.1h-1.2l-.6.1h-.6l-.6.1h-3.7l-.6-.1h-.7l-.6-.1h-1.2l-.6-.1-.6-.1-.6-.1-.6-.1h-.6l-.6-.2h-.6l-.5-.2-.6-.1-.6-.1-.6-.2-.6-.1-.6-.2-.5-.1-.6-.2-.5-.2-.6-.1-.6-.2-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.5-.2-.6-.2-.5-.2-.5-.3-.6-.2-.5-.3-.5-.2-.6-.3-.4-.3-.6-.3-.5-.3-.5-.2-.5-.4-.5-.2-.4-.4-.5-.3-.5-.3-.5-.3-.5-.4-.4-.3-.5-.4-.5-.3-.4-.3-.5-.4-.4-.4-.5-.3-.4-.4-.4-.4-.4-.4-.5-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.5-.4-.4-.3-.4-.4-.5-.4-.4-.4-.5-.3-.4-.3-.5-.4-.5-.3-.4-.3-.5-.4-.5-.3-.4-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.2-.6-.3-.5-.2-.5-.3-.5-.3-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.6-.2-.5-.2-.5-.1-.6-.2-.6-.2-.5-.2-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.6-.1-.6-.1-.5-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6v-.6l-.1-.6v-.6l-.1-.6v-.6l-.1-.7V174.2l.1-.7v-.6l.1-.6v-.6l.1-.6v-.6l.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.5.1-.6.2-.6.1-.6.2-.6.1-.5.2-.6.2-.6.2-.5.2-.6.1-.6.2-.5.2-.5.3-.6.2-.5.2-.6.2-.5.2-.6.3-.5.3-.5.2-.5.3-.5.2-.6.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.4.4-.5.3-.5.3-.4.4-.5.3-.5.3-.4.4-.5.4-.4.4-.5.3-.4.4-.4.4-.5.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.5-.4.4-.4.4-.4.4-.4.5-.3.4-.4.5-.4.4-.3.5-.3.5-.4.4-.3.5-.4.5-.3.5-.3.5-.3.4-.4.5-.2.5-.4.5-.2.5-.3.6-.3.4-.3.6-.3.5-.2.5-.3.6-.2.5-.3.5-.2.6-.2.5-.2.5-.3.6-.2.5-.2.6-.2.5-.2.6-.2.6-.1.5-.2.6-.2.5-.1.6-.2.6-.1.6-.2.6-.1.6-.1.5-.2h.6l.6-.2h.6l.6-.1.6-.1.6-.1.6-.1h1.2l.6-.1h.7l.6-.1h1.8z" /><path d="M687.7 416c26.5 0 48-21.5 48-48s-21.5-48-48-48h-352c-26.5 0-48 21.5-48 48s21.5 48 48 48h352z" fill="#FFF" /><path d="M688.3 320h1.3l.6.1h.6l.6.1h1.2l.*******.*******h.6l.6.2h.6l.6.2.*******.5.2.6.1.*******.*******.6.1.*******.*******.*******.6.2.*******.5.3.*******.*******.5.3.5.3.5.3.5.2.5.4.5.2.5.4.5.3.5.3.4.3.5.4.5.3.4.4.5.3.5.3.4.4.4.4.5.3.4.4.5.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.4.5.4.4.4.4.4.5.3.4.4.5.3.4.4.5.3.5.3.4.4.5.3.5.3.4.4.5.3.5.3.5.3.5.3.*******.5.3.5.3.*******.*******.*******.*******.*******.*******.*******.5.1.*******.*******.*******.*******.*******.6.1.6v.6l.1.*******.6v.6l.1.6v1.2l.1.7v3.6l-.1.7v1.2l-.1.6v.6l-.1.6-.1.6-.1.6v.6l-.1.6-.1.6-.1.6-.1.6-.2.5-.1.6-.1.6-.2.6-.2.6-.1.5-.2.6-.1.6-.2.5-.2.6-.2.6-.2.5-.2.5-.2.6-.2.5-.2.6-.3.5-.2.6-.3.5-.2.5-.3.5-.2.5-.3.6-.3.5-.3.5-.2.5-.3.5-.3.5-.3.5-.3.5-.4.5-.3.4-.3.5-.4.5-.3.4-.3.5-.4.5-.3.4-.4.5-.3.4-.4.5-.4.4-.4.4-.4.5-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.5.4-.4.4-.5.3-.4.4-.4.4-.5.3-.5.3-.4.4-.5.3-.5.4-.4.3-.5.3-.5.3-.5.4-.5.2-.5.4-.5.2-.5.3-.5.3-.5.3-.5.3-.6.2-.5.3-.5.2-.5.3-.6.2-.5.2-.6.2-.5.3-.5.2-.6.2-.5.2-.6.2-.5.2-.6.1-.6.2-.6.2-.5.1-.6.2-.6.1-.5.2-.6.1-.6.1-.6.2h-.6l-.6.2h-.6l-.6.1-.6.1-.6.1-.6.1h-1.2l-.6.1h-.6l-.6.1H333.9l-.7-.1h-.6l-.6-.1h-1.2l-.6-.1-.6-.1-.6-.1-.6-.1h-.6l-.6-.2h-.6l-.6-.2-.5-.1-.6-.1-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.6-.2-.5-.1-.6-.2-.5-.2-.6-.2-.5-.2-.6-.2-.5-.3-.6-.2-.5-.2-.5-.2-.6-.3-.5-.2-.5-.3-.5-.2-.5-.3-.6-.3-.5-.3-.5-.3-.5-.2-.5-.4-.4-.2-.6-.4-.4-.3-.5-.3-.5-.3-.4-.4-.5-.3-.5-.4-.4-.3-.5-.3-.5-.4-.4-.4-.4-.3-.5-.4-.4-.4-.5-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.4-.5-.4-.4-.4-.4-.3-.5-.4-.4-.3-.5-.4-.4-.3-.5-.4-.5-.3-.4-.4-.5-.3-.5-.3-.4-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.3-.5-.2-.6-.3-.5-.3-.5-.2-.5-.2-.5-.3-.6-.2-.5-.2-.6-.3-.5-.2-.6-.2-.5-.2-.5-.2-.6-.2-.6-.1-.5-.2-.6-.1-.6-.2-.5-.2-.6-.1-.6-.1-.6-.2-.6-.1-.5-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6-.1-.6v-.6l-.1-.6v-.6l-.1-.6v-1.3l-.1-.6v-2.4l.1-.6v-1.3l.1-.6v-.6l.1-.6v-.6l.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.6.1-.5.2-.6.1-.6.1-.6.2-.6.2-.5.1-.6.2-.6.1-.5.2-.6.2-.6.2-.5.2-.5.2-.6.3-.5.2-.6.2-.5.3-.6.2-.5.2-.5.3-.5.3-.5.2-.6.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.5.3-.4.3-.5.4-.5.3-.4.4-.5.3-.5.4-.4.3-.5.4-.4.3-.5.4-.4.4-.4.4-.5.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.4-.4.5-.4.4-.4.5-.4.4-.3.4-.4.5-.4.5-.3.4-.3.5-.4.5-.3.4-.4.5-.3.5-.3.4-.3.6-.4.4-.2.5-.4.5-.2.5-.3.5-.3.6-.3.5-.3.5-.2.5-.3.5-.2.6-.3.5-.2.5-.2.6-.2.5-.3.6-.2.5-.2.6-.2.5-.2.6-.2.5-.1.6-.2.6-.2.6-.1.5-.2.6-.1.6-.2.6-.1.5-.1.6-.2h.6l.6-.2h.6l.6-.1.6-.1.6-.1.6-.1h1.2l.6-.1h.6l.7-.1h353.8zM848 512c-53 0-96 43-96 96 0 27.2 11.4 51.8 29.6 69.3-3 7.9-6.3 15.8-10 23.4-39.3 81.5-118.7 142.4-207.7 158.6-128.1 23.4-258.9-47.7-312.3-160.1-3.4-7.2-6.5-14.4-9.2-21.8 18.2-17.5 29.6-42.1 29.6-69.3 0-53-43-96-96-96s-96 43-96 96c0 43.4 28.9 80.2 68.5 92 53.2 155 207 260.5 364.7 260 145.8-.5 283.6-89.6 345.4-218.6 6.4-13.5 12-27.3 16.7-41.3 39.7-11.8 68.6-48.6 68.6-92C944 555 901 512 848 512zM176 656c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48zm672 0c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-selectAll"><path d="M825.1 924H198.8c-12.1 0-21.8-9.8-21.8-21.8 0-12.1 9.8-21.8 21.8-21.8h626.3c30.4 0 55.1-24.7 55.1-55.1V539.4c0-12.1 9.8-21.8 21.8-21.8 12.1 0 21.8 9.8 21.8 21.8v285.9c0 54.4-44.3 98.7-98.7 98.7zM433.5 612.8h-.8c-6.1-.2-11.8-3-15.7-7.6L241.1 400.6c-7.9-9.1-6.8-22.9 2.3-30.8 9.1-7.9 22.9-6.8 30.8 2.3l160.5 186.8L887.4 106c8.5-8.5 22.4-8.5 30.9 0s8.5 22.4 0 30.9L448.9 606.4c-4.1 4.1-9.6 6.4-15.4 6.4z" /><path d="M716.5 815.6H197.4c-54.5 0-98.8-44.3-98.8-98.8v-.3l6.9-518.9c.1-54.4 44.4-98.6 98.8-98.6h475.3c12.1 0 21.8 9.8 21.8 21.8 0 12.1-9.8 21.8-21.8 21.8H204.3c-30.4 0-55.1 24.7-55.1 55.1v.3l-6.9 518.9c.1 30.3 24.8 55 55.1 55h519.1c30.4 0 55.1-24.7 55.1-55.1V418.4c0-12.1 9.8-21.8 21.8-21.8 12.1 0 21.8 9.8 21.8 21.8v298.4c0 54.5-44.3 98.8-98.7 98.8z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-uncombine"><path d="M885 950.5H419.4c-42.2 0-76.5-34.3-76.5-76.5V381.7c0-27.5 22.4-49.9 49.9-49.9H885c42.2 0 76.5 34.3 76.5 76.5v465.6c.1 42.2-34.3 76.6-76.5 76.6zM392.8 378.4c-1.8 0-3.3 1.5-3.3 3.3v492.2c0 16.5 13.4 30 30 30H885c16.5 0 30-13.4 30-30V408.3c0-16.5-13.4-30-30-30H392.8z" /><path d="M605.2 693.2H139.5c-42.2 0-76.5-34.3-76.5-76.5V124.4c0-27.5 22.4-49.9 49.9-49.9h492.2c42.2 0 76.5 34.3 76.5 76.5v465.6c.1 42.3-34.2 76.6-76.4 76.6zM112.9 121.1c-1.8 0-3.3 1.5-3.3 3.3v492.2c0 16.5 13.4 30 30 30h465.6c16.5 0 30-13.4 30-30V151.1c0-16.5-13.4-30-30-30H112.9z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-undo"><path d="M251.278 64v143.213l51.41-47.738c39.169-41.617 85.683-69.158 139.541-82.623s107.716-13.464 161.574 0c53.858 13.464 100.984 40.393 141.377 80.787 40.393 40.393 67.934 87.519 82.623 141.377s14.689 107.716 0 161.574-41.617 101.596-80.787 143.213L387.147 960l-47.738-47.738 356.197-359.869c31.825-29.377 53.858-65.486 66.098-108.328s12.24-85.683 0-128.525-33.661-79.563-64.262-110.164-67.322-51.41-110.164-62.426-85.071-11.016-126.689 0-78.339 32.437-110.164 64.262l-66.098 66.098h176.262v66.098h-242.36l-33.049-33.049V64h66.098z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-zoomin"><path d="M919.264 905.984 780.352 767.072C851.808 692.32 896 591.328 896 480c0-229.376-186.624-416-416-416S64 250.624 64 480s186.624 416 416 416c95.008 0 182.432-32.384 252.544-86.208l141.44 141.44a31.904 31.904 0 0 0 45.248 0 32 32 0 0 0 .032-45.248zM128 480c0-194.08 157.92-352 352-352s352 157.92 352 352-157.92 352-352 352-352-157.92-352-352z" /><path d="M625.792 448H512V336a32 32 0 0 0-64 0v112H336a32 32 0 0 0 0 64h112v112a32 32 0 1 0 64 0V512h113.792a32 32 0 1 0 0-64z" /></symbol><symbol class="icon" viewBox="0 0 1024 1024"  id="i-zoomout"><path d="M919.264 905.984 780.352 767.072C851.808 692.32 896 591.328 896 480c0-229.376-186.624-416-416-416S64 250.624 64 480s186.624 416 416 416c95.008 0 182.432-32.384 252.544-86.208l141.44 141.44a31.904 31.904 0 0 0 45.248 0 32 32 0 0 0 .032-45.248zM128 480c0-194.08 157.92-352 352-352s352 157.92 352 352-157.92 352-352 352-352-157.92-352-352z" /><path d="M625.792 448H336a32 32 0 0 0 0 64h289.792a32 32 0 1 0 0-64z" /></symbol>',t.insertBefore(n,t.lastChild)};document.readyState==="loading"?document.addEventListener("DOMContentLoaded",e):e()}const Pi={id:"app"},Ci=_t({__name:"App",setup(e){return(t,n)=>{const r=Rr("router-view");return Ar(),Or("div",Pi,[_r(r)])}}});async function xi(){const e=Tr(Ci);for(const[t,n]of Object.entries(Pr))e.component(t,n);e.use(xr()),e.use(pe),e.use(Cr);try{await Ti().ssoLogin()}catch(t){console.error("SSO 初始化失败:",t)}e.mount("#app")}xi();export{ue as _,Fe as a,kt as b,Hr as d,pe as r,Xi as u};
