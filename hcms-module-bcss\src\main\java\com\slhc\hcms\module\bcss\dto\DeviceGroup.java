package com.slhc.hcms.module.bcss.dto;

import lombok.Data;

import java.util.List;

// 设备组类（对应data数组的元素）
@Data
public class DeviceGroup {
    private String type;  // 如"轨道"、"提升机"
    private List<Device> list;  // 设备列表

    // getter和setter
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Device> getList() {
        return list;
    }

    public void setList(List<Device> list) {
        this.list = list;
    }
}