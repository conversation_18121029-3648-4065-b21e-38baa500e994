package com.slhc.hcms.module.bcss.service;

import com.slhc.hcms.module.bcss.entity.BcssDeviceSignal;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 箱式设备信号表
 * @Author: jeecg-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
public interface IBcssDeviceSignalService extends IService<BcssDeviceSignal> {

    List<BcssDeviceSignal> queryBySingleId(String id);
    
    /**
     * 带完整业务校验的Excel导入功能
     * @param file 上传的Excel文件
     * @return
     */
    Result<?> importExcelWithValidation(MultipartFile file);
}
