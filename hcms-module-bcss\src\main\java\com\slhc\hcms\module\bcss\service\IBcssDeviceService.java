package com.slhc.hcms.module.bcss.service;

import com.slhc.hcms.module.bcss.entity.BcssDevice;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.api.vo.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-21
 * @Version: V1.0
 */
public interface IBcssDeviceService extends IService<BcssDevice> {

    /**
     * 带校验的Excel导入功能
     * @param request
     * @param response
     * @return
     */
    Result<?> importExcelWithValidation(HttpServletRequest request, HttpServletResponse response);

}
