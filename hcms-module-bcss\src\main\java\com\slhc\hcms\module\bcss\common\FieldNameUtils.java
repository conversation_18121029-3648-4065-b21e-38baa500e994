package com.slhc.hcms.module.bcss.common;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class FieldNameUtils {

    /**
     * 获取类的字段名并排除指定字段后拼接成字符串
     *
     * @param clazz         类的 Class 对象
     * @param excludeFields 需要排除的字段名列表
     * @return 拼接后的字段名字符串
     */
    public static String getFieldNameString(Class<?> clazz, String excludeFields) {
        // 将排除字段的字符串转换为列表
        List<String> excludeList = Arrays.asList(excludeFields.split(","));
        // 使用反射获取类的所有字段
        Field[] fields = clazz.getDeclaredFields();

        // 过滤掉需要排除的字段
        List<String> filteredFieldNames = Arrays.stream(fields)
                .map(Field::getName)
                .filter(fieldName -> !excludeList.contains(fieldName))
                .collect(Collectors.toList());

        // 将字段名拼接成字符串
        return String.join(",", filteredFieldNames);
    }
}