package com.slhc.hcms.iot.utils;

import com.google.common.reflect.ClassPath;
import com.slhc.hcms.iot.model.DeviceManager;
import com.slhc.hcms.iot.protocol.ProtocolPlugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.ArrayList;

public class IotInspector {
    private static final Logger log = LoggerFactory.getLogger(IotInspector.class);
    /**
     * 扫描指定包下，带有特定注解的类
     *
     * @param packageName 包名，例如 "com.slhc.hcms"
     * @param clz 注解类型，例如 DeviceControl.class
     * @return Set<Class<?>> 满足条件的类集合
     */
    public static ArrayList<Class<?>> scanPackage(String packageName, Class<? extends Annotation> clz) {
        ArrayList<Class<?>>  classes = new ArrayList<>();
        try {
            for (ClassPath.ClassInfo classInfo :
                    ClassPath.from(Thread.currentThread().getContextClassLoader())
                            .getTopLevelClassesRecursive(packageName)) {
                Class<?> clazz = classInfo.load();
                if (clazz.isAnnotationPresent(clz)) {
                    System.out.println("找到类: " + clazz.getName());
                    classes.add(clazz);
                }
            }
        } catch (IOException e) {
            log.error("扫描IoT业务层类异常:", e);
        }
        return classes;
    }
    /**
     * 扫描指定包下，带有特定注解的类
     *
     * @param packageName 包名，例如 "com.slhc.hcms"
     * @param clz 注解类型，例如 DeviceControl.class
     * @return Set<Class<?>> 满足条件的类集合
     */
    public static ArrayList<Class<?>> scanPackagess(String packageName, Class<? extends ProtocolPlugin> clz) {
        ArrayList<Class<?>>  classes = new ArrayList<>();
        try {
            for (ClassPath.ClassInfo classInfo :
                    ClassPath.from(Thread.currentThread().getContextClassLoader())
                            .getTopLevelClassesRecursive(packageName)) {
                Class<?> clazz = classInfo.load();
                if (clazz.isInterface()) {
                    System.out.println("找到类: " + clazz.getName());
                    classes.add(clazz);
                }
            }
        } catch (IOException e) {
            log.error("扫描IoT业务层类异常:", e);
        }
        return classes;
    }
}
