package com.slhc.hcms.module.bcss.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.slhc.hcms.module.bcss.entity.BcssDashboard;
import com.slhc.hcms.module.bcss.entity.BcssDevice;
import com.slhc.hcms.module.bcss.entity.BcssTask;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface BcssDashboardMapper extends BaseMapper<BcssDashboard> {
    /**
     * 根据站点编号查询相关任务
     * @param stationCode 站点编号
     * @return 任务列表（源站点或目标站点匹配）
     */
    List<BcssTask> selectTasksByStationCode(@Param("stationCode")String stationCode);
    /**
     * 根据站点编号查询相关任务
     * @param stationCode 站点编号
     * @return 设备列表（源站点或目标站点匹配）
     */
    List<BcssDevice> selectDevicesByStationCode(@Param("stationCode")String stationCode);
}
