package com.slhc.hcms.module.bcss.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用来承载redis本地存储数据的模板类
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BcssLocal implements Serializable {
    private static final long serialVersionUID = 1L;
    //站点id
    public String stationId;
    //站点名
    public String stationName;
    //站点编号
    public String stationCode;
    //模块类型
    public String moduleType;
    //需要设置站点？
    public Boolean requireSettingStation;
}
