import{E as R,U as gn,a as He,b as be,x as z,y as I,D as nt,k as Xt,G as xn,t as E,T as H,I as ue,M as L,w as Z,R as Y,H as mn,P as pt,B as tt,e as V,J as yn,K as _n,i as Kt,s as Ft,L as N,j as q,N as bn,n as Ee,O as Sn}from"./Meta2D-ETuptM_u.js";import{g as wn,C as gt}from"./CanvasPool-4QqOkCau.js";import{D as Tn,U as xt,B as br,p as Se,R as we,c as Ct,b as X,S as Yt,o as Zt,y as Te,m as de,V as Dt,i as Pe,k as Pn,G as vn,e as Sr,j as Cn,r as wr,s as Tr,v as Mn,w as Pr,g as kn,h as Fn,t as Bn,u as Rn,x as Gn,n as vr}from"./colorToUniform-CCOUWOPT.js";import{a0 as Un}from"./element-plus-D-V1KzVw.js";/* empty css                  */import"./Meta2DCanvas-9N-Tmw7R.js";import"./index-CD6ICDO9.js";import"./meta2d-DtcXQZZw.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css                     *//* empty css                    */function Ve(s,t,e=2){const r=t&&t.length,n=r?t[0]*e:s.length;let i=Cr(s,0,n,e,!0);const o=[];if(!i||i.next===i.prev)return o;let a,l,c;if(r&&(i=In(s,t,i,e)),s.length>80*e){a=s[0],l=s[1];let h=a,u=l;for(let f=e;f<n;f+=e){const d=s[f],p=s[f+1];d<a&&(a=d),p<l&&(l=p),d>h&&(h=d),p>u&&(u=p)}c=Math.max(h-a,u-l),c=c!==0?32767/c:0}return Bt(i,o,e,a,l,c,0),o}function Cr(s,t,e,r,n){let i;if(n===Xn(s,t,e,r)>0)for(let o=t;o<e;o+=r)i=Oe(o/r|0,s[o],s[o+1],i);else for(let o=e-r;o>=t;o-=r)i=Oe(o/r|0,s[o],s[o+1],i);return i&&mt(i,i.next)&&(Gt(i),i=i.next),i}function dt(s,t){if(!s)return s;t||(t=s);let e=s,r;do if(r=!1,!e.steiner&&(mt(e,e.next)||W(e.prev,e,e.next)===0)){if(Gt(e),e=t=e.prev,e===e.next)break;r=!0}else e=e.next;while(r||e!==t);return t}function Bt(s,t,e,r,n,i,o){if(!s)return;!o&&i&&On(s,r,n,i);let a=s;for(;s.prev!==s.next;){const l=s.prev,c=s.next;if(i?An(s,r,n,i):zn(s)){t.push(l.i,s.i,c.i),Gt(s),s=c.next,a=c.next;continue}if(s=c,s===a){o?o===1?(s=Wn(dt(s),t),Bt(s,t,e,r,n,i,2)):o===2&&Dn(s,t,e,r,n,i):Bt(dt(s),t,e,r,n,i,1);break}}}function zn(s){const t=s.prev,e=s,r=s.next;if(W(t,e,r)>=0)return!1;const n=t.x,i=e.x,o=r.x,a=t.y,l=e.y,c=r.y,h=Math.min(n,i,o),u=Math.min(a,l,c),f=Math.max(n,i,o),d=Math.max(a,l,c);let p=r.next;for(;p!==t;){if(p.x>=h&&p.x<=f&&p.y>=u&&p.y<=d&&vt(n,a,i,l,o,c,p.x,p.y)&&W(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function An(s,t,e,r){const n=s.prev,i=s,o=s.next;if(W(n,i,o)>=0)return!1;const a=n.x,l=i.x,c=o.x,h=n.y,u=i.y,f=o.y,d=Math.min(a,l,c),p=Math.min(h,u,f),g=Math.max(a,l,c),m=Math.max(h,u,f),x=fe(d,p,t,e,r),y=fe(g,m,t,e,r);let b=s.prevZ,_=s.nextZ;for(;b&&b.z>=x&&_&&_.z<=y;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=m&&b!==n&&b!==o&&vt(a,h,l,u,c,f,b.x,b.y)&&W(b.prev,b,b.next)>=0||(b=b.prevZ,_.x>=d&&_.x<=g&&_.y>=p&&_.y<=m&&_!==n&&_!==o&&vt(a,h,l,u,c,f,_.x,_.y)&&W(_.prev,_,_.next)>=0))return!1;_=_.nextZ}for(;b&&b.z>=x;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=m&&b!==n&&b!==o&&vt(a,h,l,u,c,f,b.x,b.y)&&W(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;_&&_.z<=y;){if(_.x>=d&&_.x<=g&&_.y>=p&&_.y<=m&&_!==n&&_!==o&&vt(a,h,l,u,c,f,_.x,_.y)&&W(_.prev,_,_.next)>=0)return!1;_=_.nextZ}return!0}function Wn(s,t){let e=s;do{const r=e.prev,n=e.next.next;!mt(r,n)&&kr(r,e,e.next,n)&&Rt(r,n)&&Rt(n,r)&&(t.push(r.i,e.i,n.i),Gt(e),Gt(e.next),e=s=n),e=e.next}while(e!==s);return dt(e)}function Dn(s,t,e,r,n,i){let o=s;do{let a=o.next.next;for(;a!==o.prev;){if(o.i!==a.i&&$n(o,a)){let l=Fr(o,a);o=dt(o,o.next),l=dt(l,l.next),Bt(o,t,e,r,n,i,0),Bt(l,t,e,r,n,i,0);return}a=a.next}o=o.next}while(o!==s)}function In(s,t,e,r){const n=[];for(let i=0,o=t.length;i<o;i++){const a=t[i]*r,l=i<o-1?t[i+1]*r:s.length,c=Cr(s,a,l,r,!1);c===c.next&&(c.steiner=!0),n.push(jn(c))}n.sort(Ln);for(let i=0;i<n.length;i++)e=Hn(n[i],e);return e}function Ln(s,t){let e=s.x-t.x;if(e===0&&(e=s.y-t.y,e===0)){const r=(s.next.y-s.y)/(s.next.x-s.x),n=(t.next.y-t.y)/(t.next.x-t.x);e=r-n}return e}function Hn(s,t){const e=En(s,t);if(!e)return t;const r=Fr(e,s);return dt(r,r.next),dt(e,e.next)}function En(s,t){let e=t;const r=s.x,n=s.y;let i=-1/0,o;if(mt(s,e))return e;do{if(mt(s,e.next))return e.next;if(n<=e.y&&n>=e.next.y&&e.next.y!==e.y){const u=e.x+(n-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(u<=r&&u>i&&(i=u,o=e.x<e.next.x?e:e.next,u===r))return o}e=e.next}while(e!==t);if(!o)return null;const a=o,l=o.x,c=o.y;let h=1/0;e=o;do{if(r>=e.x&&e.x>=l&&r!==e.x&&Mr(n<c?r:i,n,l,c,n<c?i:r,n,e.x,e.y)){const u=Math.abs(n-e.y)/(r-e.x);Rt(e,s)&&(u<h||u===h&&(e.x>o.x||e.x===o.x&&Vn(o,e)))&&(o=e,h=u)}e=e.next}while(e!==a);return o}function Vn(s,t){return W(s.prev,s,t.prev)<0&&W(t.next,s,s.next)<0}function On(s,t,e,r){let n=s;do n.z===0&&(n.z=fe(n.x,n.y,t,e,r)),n.prevZ=n.prev,n.nextZ=n.next,n=n.next;while(n!==s);n.prevZ.nextZ=null,n.prevZ=null,Yn(n)}function Yn(s){let t,e=1;do{let r=s,n;s=null;let i=null;for(t=0;r;){t++;let o=r,a=0;for(let c=0;c<e&&(a++,o=o.nextZ,!!o);c++);let l=e;for(;a>0||l>0&&o;)a!==0&&(l===0||!o||r.z<=o.z)?(n=r,r=r.nextZ,a--):(n=o,o=o.nextZ,l--),i?i.nextZ=n:s=n,n.prevZ=i,i=n;r=o}i.nextZ=null,e*=2}while(t>1);return s}function fe(s,t,e,r,n){return s=(s-e)*n|0,t=(t-r)*n|0,s=(s|s<<8)&16711935,s=(s|s<<4)&252645135,s=(s|s<<2)&858993459,s=(s|s<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,s|t<<1}function jn(s){let t=s,e=s;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==s);return e}function Mr(s,t,e,r,n,i,o,a){return(n-o)*(t-a)>=(s-o)*(i-a)&&(s-o)*(r-a)>=(e-o)*(t-a)&&(e-o)*(i-a)>=(n-o)*(r-a)}function vt(s,t,e,r,n,i,o,a){return!(s===o&&t===a)&&Mr(s,t,e,r,n,i,o,a)}function $n(s,t){return s.next.i!==t.i&&s.prev.i!==t.i&&!Nn(s,t)&&(Rt(s,t)&&Rt(t,s)&&qn(s,t)&&(W(s.prev,s,t.prev)||W(s,t.prev,t))||mt(s,t)&&W(s.prev,s,s.next)>0&&W(t.prev,t,t.next)>0)}function W(s,t,e){return(t.y-s.y)*(e.x-t.x)-(t.x-s.x)*(e.y-t.y)}function mt(s,t){return s.x===t.x&&s.y===t.y}function kr(s,t,e,r){const n=Lt(W(s,t,e)),i=Lt(W(s,t,r)),o=Lt(W(e,r,s)),a=Lt(W(e,r,t));return!!(n!==i&&o!==a||n===0&&It(s,e,t)||i===0&&It(s,r,t)||o===0&&It(e,s,r)||a===0&&It(e,t,r))}function It(s,t,e){return t.x<=Math.max(s.x,e.x)&&t.x>=Math.min(s.x,e.x)&&t.y<=Math.max(s.y,e.y)&&t.y>=Math.min(s.y,e.y)}function Lt(s){return s>0?1:s<0?-1:0}function Nn(s,t){let e=s;do{if(e.i!==s.i&&e.next.i!==s.i&&e.i!==t.i&&e.next.i!==t.i&&kr(e,e.next,s,t))return!0;e=e.next}while(e!==s);return!1}function Rt(s,t){return W(s.prev,s,s.next)<0?W(s,t,s.next)>=0&&W(s,s.prev,t)>=0:W(s,t,s.prev)<0||W(s,s.next,t)<0}function qn(s,t){let e=s,r=!1;const n=(s.x+t.x)/2,i=(s.y+t.y)/2;do e.y>i!=e.next.y>i&&e.next.y!==e.y&&n<(e.next.x-e.x)*(i-e.y)/(e.next.y-e.y)+e.x&&(r=!r),e=e.next;while(e!==s);return r}function Fr(s,t){const e=pe(s.i,s.x,s.y),r=pe(t.i,t.x,t.y),n=s.next,i=t.prev;return s.next=t,t.prev=s,e.next=n,n.prev=e,r.next=e,e.prev=r,i.next=r,r.prev=i,r}function Oe(s,t,e,r){const n=pe(s,t,e);return r?(n.next=r.next,n.prev=r,r.next.prev=n,r.next=n):(n.prev=n,n.next=n),n}function Gt(s){s.next.prev=s.prev,s.prev.next=s.next,s.prevZ&&(s.prevZ.nextZ=s.nextZ),s.nextZ&&(s.nextZ.prevZ=s.prevZ)}function pe(s,t,e){return{i:s,x:t,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function Xn(s,t,e,r){let n=0;for(let i=t,o=e-r;i<e;i+=r)n+=(s[o]-s[i])*(s[i+1]+s[o+1]),o=i;return n}const Kn=Ve.default||Ve;class Br{static init(t){Object.defineProperty(this,"resizeTo",{set(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{this._resizeTo&&(this._cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this._cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this._cancelResize();let e,r;if(this._resizeTo===globalThis.window)e=globalThis.innerWidth,r=globalThis.innerHeight;else{const{clientWidth:n,clientHeight:i}=this._resizeTo;e=n,r=i}this.renderer.resize(e,r),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=t.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this._cancelResize(),this._cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}Br.extension=R.Application;class Rr{static init(t){t=Object.assign({autoStart:!0,sharedTicker:!1},t),Object.defineProperty(this,"ticker",{set(e){this._ticker&&this._ticker.remove(this.render,this),this._ticker=e,e&&e.add(this.render,this,gn.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=t.sharedTicker?He.shared:new He,t.autoStart&&this.start()}static destroy(){if(this._ticker){const t=this._ticker;this.ticker=null,t.destroy()}}}Rr.extension=R.Application;class Zn extends be{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100}get font(){return z(I,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return z(I,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return z(I,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return z(I,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return z(I,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(t=!1){var e;this.emit("destroy",this),this.removeAllListeners();for(const r in this.chars)(e=this.chars[r].texture)==null||e.destroy();this.chars=null,t&&(this.pages.forEach(r=>r.texture.destroy(!0)),this.pages=null)}}const Qn=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function jt(s){const t=typeof s.fontSize=="number"?`${s.fontSize}px`:s.fontSize;let e=s.fontFamily;Array.isArray(s.fontFamily)||(e=s.fontFamily.split(","));for(let r=e.length-1;r>=0;r--){let n=e[r].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&!Qn.includes(n)&&(n=`"${n}"`),e[r]=n}return`${s.fontStyle} ${s.fontVariant} ${s.fontWeight} ${t} ${e.join(",")}`}const ee={willReadFrequently:!0},Q=class C{static get experimentalLetterSpacingSupported(){let t=C._experimentalLetterSpacingSupported;if(t===void 0){const e=nt.get().getCanvasRenderingContext2D().prototype;t=C._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e}return t}constructor(t,e,r,n,i,o,a,l,c){this.text=t,this.style=e,this.width=r,this.height=n,this.lines=i,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=c}static measureText(t=" ",e,r=C._canvas,n=e.wordWrap){var x;const i=jt(e),o=C.measureFont(i);o.fontSize===0&&(o.fontSize=e.fontSize,o.ascent=e.fontSize);const a=C.__context;a.font=i;const c=(n?C._wordWrap(t,e,r):t).split(/(?:\r\n|\r|\n)/),h=new Array(c.length);let u=0;for(let y=0;y<c.length;y++){const b=C._measureText(c[y],e.letterSpacing,a);h[y]=b,u=Math.max(u,b)}const f=((x=e._stroke)==null?void 0:x.width)||0;let d=u+f;e.dropShadow&&(d+=e.dropShadow.distance);const p=e.lineHeight||o.fontSize;let g=Math.max(p,o.fontSize+f)+(c.length-1)*(p+e.leading);return e.dropShadow&&(g+=e.dropShadow.distance),new C(t,e,d,g,c,h,p+e.leading,u,o)}static _measureText(t,e,r){let n=!1;C.experimentalLetterSpacingSupported&&(C.experimentalLetterSpacing?(r.letterSpacing=`${e}px`,r.textLetterSpacing=`${e}px`,n=!0):(r.letterSpacing="0px",r.textLetterSpacing="0px"));const i=r.measureText(t);let o=i.width;const a=-i.actualBoundingBoxLeft;let c=i.actualBoundingBoxRight-a;if(o>0)if(n)o-=e,c-=e;else{const h=(C.graphemeSegmenter(t).length-1)*e;o+=h,c+=h}return Math.max(o,c)}static _wordWrap(t,e,r=C._canvas){const n=r.getContext("2d",ee);let i=0,o="",a="";const l=Object.create(null),{letterSpacing:c,whiteSpace:h}=e,u=C._collapseSpaces(h),f=C._collapseNewlines(h);let d=!u;const p=e.wordWrapWidth+c,g=C._tokenize(t);for(let m=0;m<g.length;m++){let x=g[m];if(C._isNewline(x)){if(!f){a+=C._addLine(o),d=!u,o="",i=0;continue}x=" "}if(u){const b=C.isBreakingSpace(x),_=C.isBreakingSpace(o[o.length-1]);if(b&&_)continue}const y=C._getFromCache(x,c,l,n);if(y>p)if(o!==""&&(a+=C._addLine(o),o="",i=0),C.canBreakWords(x,e.breakWords)){const b=C.wordWrapSplit(x);for(let _=0;_<b.length;_++){let T=b[_],w=T,S=1;for(;b[_+S];){const F=b[_+S];if(!C.canBreakChars(w,F,x,_,e.breakWords))T+=F;else break;w=F,S++}_+=S-1;const M=C._getFromCache(T,c,l,n);M+i>p&&(a+=C._addLine(o),d=!1,o="",i=0),o+=T,i+=M}}else{o.length>0&&(a+=C._addLine(o),o="",i=0);const b=m===g.length-1;a+=C._addLine(x,!b),d=!1,o="",i=0}else y+i>p&&(d=!1,a+=C._addLine(o),o="",i=0),(o.length>0||!C.isBreakingSpace(x)||d)&&(o+=x,i+=y)}return a+=C._addLine(o,!1),a}static _addLine(t,e=!0){return t=C._trimRight(t),t=e?`${t}
`:t,t}static _getFromCache(t,e,r,n){let i=r[t];return typeof i!="number"&&(i=C._measureText(t,e,n)+e,r[t]=i),i}static _collapseSpaces(t){return t==="normal"||t==="pre-line"}static _collapseNewlines(t){return t==="normal"}static _trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const r=t[e];if(!C.isBreakingSpace(r))break;t=t.slice(0,-1)}return t}static _isNewline(t){return typeof t!="string"?!1:C._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:C._breakingSpaces.includes(t.charCodeAt(0))}static _tokenize(t){const e=[];let r="";if(typeof t!="string")return e;for(let n=0;n<t.length;n++){const i=t[n],o=t[n+1];if(C.isBreakingSpace(i,o)||C._isNewline(i)){r!==""&&(e.push(r),r=""),i==="\r"&&o===`
`?(e.push(`\r
`),n++):e.push(i);continue}r+=i}return r!==""&&e.push(r),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,r,n,i){return!0}static wordWrapSplit(t){return C.graphemeSegmenter(t)}static measureFont(t){if(C._fonts[t])return C._fonts[t];const e=C._context;e.font=t;const r=e.measureText(C.METRICS_STRING+C.BASELINE_SYMBOL),n={ascent:r.actualBoundingBoxAscent,descent:r.actualBoundingBoxDescent,fontSize:r.actualBoundingBoxAscent+r.actualBoundingBoxDescent};return C._fonts[t]=n,n}static clearMetrics(t=""){t?delete C._fonts[t]:C._fonts={}}static get _canvas(){if(!C.__canvas){let t;try{const e=new OffscreenCanvas(0,0),r=e.getContext("2d",ee);if(r!=null&&r.measureText)return C.__canvas=e,e;t=nt.get().createCanvas()}catch{t=nt.get().createCanvas()}t.width=t.height=10,C.__canvas=t}return C.__canvas}static get _context(){return C.__context||(C.__context=C._canvas.getContext("2d",ee)),C.__context}};Q.METRICS_STRING="|ÉqÅ";Q.BASELINE_SYMBOL="M";Q.BASELINE_MULTIPLIER=1.4;Q.HEIGHT_MULTIPLIER=2;Q.graphemeSegmenter=(()=>{if(typeof(Intl==null?void 0:Intl.Segmenter)=="function"){const s=new Intl.Segmenter;return t=>{const e=s.segment(t),r=[];let n=0;for(const i of e)r[n++]=i.segment;return r}}return s=>[...s]})();Q.experimentalLetterSpacing=!1;Q._fonts={};Q._newlines=[10,13];Q._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];let K=Q;const Ye=[{offset:0,color:"white"},{offset:1,color:"black"}],ve=class ge{constructor(...t){this.uid=Xt("fillGradient"),this.type="linear",this.colorStops=[];let e=Jn(t);e={...e.type==="radial"?ge.defaultRadialOptions:ge.defaultLinearOptions,...xn(e)},this._textureSize=e.textureSize,this._wrapMode=e.wrapMode,e.type==="radial"?(this.center=e.center,this.outerCenter=e.outerCenter??this.center,this.innerRadius=e.innerRadius,this.outerRadius=e.outerRadius,this.scale=e.scale,this.rotation=e.rotation):(this.start=e.start,this.end=e.end),this.textureSpace=e.textureSpace,this.type=e.type,e.colorStops.forEach(n=>{this.addColorStop(n.offset,n.color)})}addColorStop(t,e){return this.colorStops.push({offset:t,color:E.shared.setValue(e).toHexa()}),this}buildLinearGradient(){if(this.texture)return;let{x:t,y:e}=this.start,{x:r,y:n}=this.end,i=r-t,o=n-e;const a=i<0||o<0;if(this._wrapMode==="clamp-to-edge"){if(i<0){const m=t;t=r,r=m,i*=-1}if(o<0){const m=e;e=n,n=m,o*=-1}}const l=this.colorStops.length?this.colorStops:Ye,c=this._textureSize,{canvas:h,context:u}=$e(c,1),f=a?u.createLinearGradient(this._textureSize,0,0,0):u.createLinearGradient(0,0,this._textureSize,0);je(f,l),u.fillStyle=f,u.fillRect(0,0,c,1),this.texture=new H({source:new ue({resource:h,addressMode:this._wrapMode})});const d=Math.sqrt(i*i+o*o),p=Math.atan2(o,i),g=new L;g.scale(d/c,1),g.rotate(p),g.translate(t,e),this.textureSpace==="local"&&g.scale(c,c),this.transform=g}buildGradient(){this.type==="linear"?this.buildLinearGradient():this.buildRadialGradient()}buildRadialGradient(){if(this.texture)return;const t=this.colorStops.length?this.colorStops:Ye,e=this._textureSize,{canvas:r,context:n}=$e(e,e),{x:i,y:o}=this.center,{x:a,y:l}=this.outerCenter,c=this.innerRadius,h=this.outerRadius,u=a-h,f=l-h,d=e/(h*2),p=(i-u)*d,g=(o-f)*d,m=n.createRadialGradient(p,g,c*d,(a-u)*d,(l-f)*d,h*d);je(m,t),n.fillStyle=t[t.length-1].color,n.fillRect(0,0,e,e),n.fillStyle=m,n.translate(p,g),n.rotate(this.rotation),n.scale(1,this.scale),n.translate(-p,-g),n.fillRect(0,0,e,e),this.texture=new H({source:new ue({resource:r,addressMode:this._wrapMode})});const x=new L;x.scale(1/d,1/d),x.translate(u,f),this.textureSpace==="local"&&x.scale(e,e),this.transform=x}get styleKey(){return this.uid}destroy(){var t;(t=this.texture)==null||t.destroy(!0),this.texture=null,this.transform=null,this.colorStops=[],this.start=null,this.end=null,this.center=null,this.outerCenter=null}};ve.defaultLinearOptions={start:{x:0,y:0},end:{x:0,y:1},colorStops:[],textureSpace:"local",type:"linear",textureSize:256,wrapMode:"clamp-to-edge"};ve.defaultRadialOptions={center:{x:.5,y:.5},innerRadius:0,outerRadius:.5,colorStops:[],scale:1,textureSpace:"local",type:"radial",textureSize:256,wrapMode:"clamp-to-edge"};let et=ve;function je(s,t){for(let e=0;e<t.length;e++){const r=t[e];s.addColorStop(r.offset,r.color)}}function $e(s,t){const e=nt.get().createCanvas(s,t),r=e.getContext("2d");return{canvas:e,context:r}}function Jn(s){let t=s[0]??{};return(typeof t=="number"||s[1])&&(z("8.5.2","use options object instead"),t={type:"linear",start:{x:s[0],y:s[1]},end:{x:s[2],y:s[3]},textureSpace:s[4],textureSize:s[5]??et.defaultLinearOptions.textureSize}),t}const Ne={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class Qt{constructor(t,e){this.uid=Xt("fillPattern"),this.transform=new L,this._styleKey=null,this.texture=t,this.transform.scale(1/t.frame.width,1/t.frame.height),e&&(t.source.style.addressModeU=Ne[e].addressModeU,t.source.style.addressModeV=Ne[e].addressModeV)}setTransform(t){const e=this.texture;this.transform.copyFrom(t),this.transform.invert(),this.transform.scale(1/e.frame.width,1/e.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}destroy(){this.texture.destroy(!0),this.texture=null,this._styleKey=null}}var ti=ri,re={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},ei=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function ri(s){var t=[];return s.replace(ei,function(e,r,n){var i=r.toLowerCase();for(n=ii(n),i=="m"&&n.length>2&&(t.push([r].concat(n.splice(0,2))),i="l",r=r=="m"?"l":"L");;){if(n.length==re[i])return n.unshift(r),t.push(n);if(n.length<re[i])throw new Error("malformed path data");t.push([r].concat(n.splice(0,re[i])))}}),t}var ni=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function ii(s){var t=s.match(ni);return t?t.map(Number):[]}const si=Un(ti);function oi(s,t){const e=si(s),r=[];let n=null,i=0,o=0;for(let a=0;a<e.length;a++){const l=e[a],c=l[0],h=l;switch(c){case"M":i=h[1],o=h[2],t.moveTo(i,o);break;case"m":i+=h[1],o+=h[2],t.moveTo(i,o);break;case"H":i=h[1],t.lineTo(i,o);break;case"h":i+=h[1],t.lineTo(i,o);break;case"V":o=h[1],t.lineTo(i,o);break;case"v":o+=h[1],t.lineTo(i,o);break;case"L":i=h[1],o=h[2],t.lineTo(i,o);break;case"l":i+=h[1],o+=h[2],t.lineTo(i,o);break;case"C":i=h[5],o=h[6],t.bezierCurveTo(h[1],h[2],h[3],h[4],i,o);break;case"c":t.bezierCurveTo(i+h[1],o+h[2],i+h[3],o+h[4],i+h[5],o+h[6]),i+=h[5],o+=h[6];break;case"S":i=h[3],o=h[4],t.bezierCurveToShort(h[1],h[2],i,o);break;case"s":t.bezierCurveToShort(i+h[1],o+h[2],i+h[3],o+h[4]),i+=h[3],o+=h[4];break;case"Q":i=h[3],o=h[4],t.quadraticCurveTo(h[1],h[2],i,o);break;case"q":t.quadraticCurveTo(i+h[1],o+h[2],i+h[3],o+h[4]),i+=h[3],o+=h[4];break;case"T":i=h[1],o=h[2],t.quadraticCurveToShort(i,o);break;case"t":i+=h[1],o+=h[2],t.quadraticCurveToShort(i,o);break;case"A":i=h[6],o=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],i,o);break;case"a":i+=h[6],o+=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],i,o);break;case"Z":case"z":t.closePath(),r.length>0&&(n=r.pop(),n?(i=n.startX,o=n.startY):(i=0,o=0)),n=null;break;default:Z(`Unknown SVG path command: ${c}`)}c!=="Z"&&c!=="z"&&n===null&&(n={startX:i,startY:o},r.push(n))}return t}class Ce{constructor(t=0,e=0,r=0){this.type="circle",this.x=t,this.y=e,this.radius=r}clone(){return new Ce(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const r=this.radius*this.radius;let n=this.x-t,i=this.y-e;return n*=n,i*=i,n+i<=r}strokeContains(t,e,r,n=.5){if(this.radius===0)return!1;const i=this.x-t,o=this.y-e,a=this.radius,l=(1-n)*r,c=Math.sqrt(i*i+o*o);return c<=a+l&&c>a-(r-l)}getBounds(t){return t||(t=new Y),t.x=this.x-this.radius,t.y=this.y-this.radius,t.width=this.radius*2,t.height=this.radius*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.radius=t.radius,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Me{constructor(t=0,e=0,r=0,n=0){this.type="ellipse",this.x=t,this.y=e,this.halfWidth=r,this.halfHeight=n}clone(){return new Me(this.x,this.y,this.halfWidth,this.halfHeight)}contains(t,e){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let r=(t-this.x)/this.halfWidth,n=(e-this.y)/this.halfHeight;return r*=r,n*=n,r+n<=1}strokeContains(t,e,r,n=.5){const{halfWidth:i,halfHeight:o}=this;if(i<=0||o<=0)return!1;const a=r*(1-n),l=r-a,c=i-l,h=o-l,u=i+a,f=o+a,d=t-this.x,p=e-this.y,g=d*d/(c*c)+p*p/(h*h),m=d*d/(u*u)+p*p/(f*f);return g>1&&m<=1}getBounds(t){return t||(t=new Y),t.x=this.x-this.halfWidth,t.y=this.y-this.halfHeight,t.width=this.halfWidth*2,t.height=this.halfHeight*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.halfWidth=t.halfWidth,this.halfHeight=t.halfHeight,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function ai(s,t,e,r,n,i){const o=s-e,a=t-r,l=n-e,c=i-r,h=o*l+a*c,u=l*l+c*c;let f=-1;u!==0&&(f=h/u);let d,p;f<0?(d=e,p=r):f>1?(d=n,p=i):(d=e+f*l,p=r+f*c);const g=s-d,m=t-p;return g*g+m*m}let li,hi;class Mt{constructor(...t){this.type="polygon";let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const r=[];for(let n=0,i=e.length;n<i;n++)r.push(e[n].x,e[n].y);e=r}this.points=e,this.closePath=!0}isClockwise(){let t=0;const e=this.points,r=e.length;for(let n=0;n<r;n+=2){const i=e[n],o=e[n+1],a=e[(n+2)%r],l=e[(n+3)%r];t+=(a-i)*(l+o)}return t<0}containsPolygon(t){const e=this.getBounds(li),r=t.getBounds(hi);if(!e.containsRect(r))return!1;const n=t.points;for(let i=0;i<n.length;i+=2){const o=n[i],a=n[i+1];if(!this.contains(o,a))return!1}return!0}clone(){const t=this.points.slice(),e=new Mt(t);return e.closePath=this.closePath,e}contains(t,e){let r=!1;const n=this.points.length/2;for(let i=0,o=n-1;i<n;o=i++){const a=this.points[i*2],l=this.points[i*2+1],c=this.points[o*2],h=this.points[o*2+1];l>e!=h>e&&t<(c-a)*((e-l)/(h-l))+a&&(r=!r)}return r}strokeContains(t,e,r,n=.5){const i=r*r,o=i*(1-n),a=i-o,{points:l}=this,c=l.length-(this.closePath?0:2);for(let h=0;h<c;h+=2){const u=l[h],f=l[h+1],d=l[(h+2)%l.length],p=l[(h+3)%l.length],g=ai(t,e,u,f,d,p),m=Math.sign((d-u)*(e-f)-(p-f)*(t-u));if(g<=(m<0?a:o))return!0}return!1}getBounds(t){t||(t=new Y);const e=this.points;let r=1/0,n=-1/0,i=1/0,o=-1/0;for(let a=0,l=e.length;a<l;a+=2){const c=e[a],h=e[a+1];r=c<r?c:r,n=c>n?c:n,i=h<i?h:i,o=h>o?h:o}return t.x=r,t.width=n-r,t.y=i,t.height=o-i,t}copyFrom(t){return this.points=t.points.slice(),this.closePath=t.closePath,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return z("8.11.0","Polygon.lastX is deprecated, please use Polygon.lastX instead."),this.points[this.points.length-2]}get y(){return z("8.11.0","Polygon.y is deprecated, please use Polygon.lastY instead."),this.points[this.points.length-1]}get startX(){return this.points[0]}get startY(){return this.points[1]}}const Ht=(s,t,e,r,n,i,o)=>{const a=s-e,l=t-r,c=Math.sqrt(a*a+l*l);return c>=n-i&&c<=n+o};class ke{constructor(t=0,e=0,r=0,n=0,i=20){this.type="roundedRectangle",this.x=t,this.y=e,this.width=r,this.height=n,this.radius=i}getBounds(t){return t||(t=new Y),t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}clone(){return new ke(this.x,this.y,this.width,this.height,this.radius)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const r=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+r&&e<=this.y+this.height-r||t>=this.x+r&&t<=this.x+this.width-r)return!0;let n=t-(this.x+r),i=e-(this.y+r);const o=r*r;if(n*n+i*i<=o||(n=t-(this.x+this.width-r),n*n+i*i<=o)||(i=e-(this.y+this.height-r),n*n+i*i<=o)||(n=t-(this.x+r),n*n+i*i<=o))return!0}return!1}strokeContains(t,e,r,n=.5){const{x:i,y:o,width:a,height:l,radius:c}=this,h=r*(1-n),u=r-h,f=i+c,d=o+c,p=a-c*2,g=l-c*2,m=i+a,x=o+l;return(t>=i-h&&t<=i+u||t>=m-u&&t<=m+h)&&e>=d&&e<=d+g||(e>=o-h&&e<=o+u||e>=x-u&&e<=x+h)&&t>=f&&t<=f+p?!0:t<f&&e<d&&Ht(t,e,f,d,c,u,h)||t>m-c&&e<d&&Ht(t,e,m-c,d,c,u,h)||t>m-c&&e>x-c&&Ht(t,e,m-c,x-c,c,u,h)||t<f&&e>x-c&&Ht(t,e,f,x-c,c,u,h)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}function ci(s,t,e,r,n,i,o,a=null){let l=0;e*=t,n*=i;const c=a.a,h=a.b,u=a.c,f=a.d,d=a.tx,p=a.ty;for(;l<o;){const g=s[e],m=s[e+1];r[n]=c*g+u*m+d,r[n+1]=h*g+f*m+p,n+=i,e+=t,l++}}function ui(s,t,e,r){let n=0;for(t*=e;n<r;)s[t]=0,s[t+1]=0,t+=e,n++}function Gr(s,t,e,r,n){const i=t.a,o=t.b,a=t.c,l=t.d,c=t.tx,h=t.ty;e||(e=0),r||(r=2),n||(n=s.length/r-e);let u=e*r;for(let f=0;f<n;f++){const d=s[u],p=s[u+1];s[u]=i*d+a*p+c,s[u+1]=o*d+l*p+h,u+=r}}const di=new L;class Fe{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.renderable&&this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const t=this.baseColor,e=t>>16|t&65280|(t&255)<<16,r=this.renderable;return r?mn(e,r.groupColor)+(this.alpha*r.groupAlpha*255<<24):e+(this.alpha*255<<24)}get transform(){var t;return((t=this.renderable)==null?void 0:t.groupTransform)||di}copyTo(t){t.indexOffset=this.indexOffset,t.indexSize=this.indexSize,t.attributeOffset=this.attributeOffset,t.attributeSize=this.attributeSize,t.baseColor=this.baseColor,t.alpha=this.alpha,t.texture=this.texture,t.geometryData=this.geometryData,t.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const Ut={extension:{type:R.ShapeBuilder,name:"circle"},build(s,t){let e,r,n,i,o,a;if(s.type==="circle"){const _=s;if(o=a=_.radius,o<=0)return!1;e=_.x,r=_.y,n=i=0}else if(s.type==="ellipse"){const _=s;if(o=_.halfWidth,a=_.halfHeight,o<=0||a<=0)return!1;e=_.x,r=_.y,n=i=0}else{const _=s,T=_.width/2,w=_.height/2;e=_.x+T,r=_.y+w,o=a=Math.max(0,Math.min(_.radius,Math.min(T,w))),n=T-o,i=w-a}if(n<0||i<0)return!1;const l=Math.ceil(2.3*Math.sqrt(o+a)),c=l*8+(n?4:0)+(i?4:0);if(c===0)return!1;if(l===0)return t[0]=t[6]=e+n,t[1]=t[3]=r+i,t[2]=t[4]=e-n,t[5]=t[7]=r-i,!0;let h=0,u=l*4+(n?2:0)+2,f=u,d=c,p=n+o,g=i,m=e+p,x=e-p,y=r+g;if(t[h++]=m,t[h++]=y,t[--u]=y,t[--u]=x,i){const _=r-g;t[f++]=x,t[f++]=_,t[--d]=_,t[--d]=m}for(let _=1;_<l;_++){const T=Math.PI/2*(_/l),w=n+Math.cos(T)*o,S=i+Math.sin(T)*a,M=e+w,F=e-w,v=r+S,P=r-S;t[h++]=M,t[h++]=v,t[--u]=v,t[--u]=F,t[f++]=F,t[f++]=P,t[--d]=P,t[--d]=M}p=n,g=i+a,m=e+p,x=e-p,y=r+g;const b=r-g;return t[h++]=m,t[h++]=y,t[--d]=b,t[--d]=m,n&&(t[h++]=x,t[h++]=y,t[--d]=b,t[--d]=x),!0},triangulate(s,t,e,r,n,i){if(s.length===0)return;let o=0,a=0;for(let h=0;h<s.length;h+=2)o+=s[h],a+=s[h+1];o/=s.length/2,a/=s.length/2;let l=r;t[l*e]=o,t[l*e+1]=a;const c=l++;for(let h=0;h<s.length;h+=2)t[l*e]=s[h],t[l*e+1]=s[h+1],h>0&&(n[i++]=l,n[i++]=c,n[i++]=l-1),l++;n[i++]=c+1,n[i++]=c,n[i++]=l-1}},fi={...Ut,extension:{...Ut.extension,name:"ellipse"}},pi={...Ut,extension:{...Ut.extension,name:"roundedRectangle"}},Ur=1e-4,qe=1e-4;function gi(s){const t=s.length;if(t<6)return 1;let e=0;for(let r=0,n=s[t-2],i=s[t-1];r<t;r+=2){const o=s[r],a=s[r+1];e+=(o-n)*(a+i),n=o,i=a}return e<0?-1:1}function Xe(s,t,e,r,n,i,o,a){const l=s-e*n,c=t-r*n,h=s+e*i,u=t+r*i;let f,d;o?(f=r,d=-e):(f=-r,d=e);const p=l+f,g=c+d,m=h+f,x=u+d;return a.push(p,g),a.push(m,x),2}function ht(s,t,e,r,n,i,o,a){const l=e-s,c=r-t;let h=Math.atan2(l,c),u=Math.atan2(n-s,i-t);a&&h<u?h+=Math.PI*2:!a&&h>u&&(u+=Math.PI*2);let f=h;const d=u-h,p=Math.abs(d),g=Math.sqrt(l*l+c*c),m=(15*p*Math.sqrt(g)/Math.PI>>0)+1,x=d/m;if(f+=x,a){o.push(s,t),o.push(e,r);for(let y=1,b=f;y<m;y++,b+=x)o.push(s,t),o.push(s+Math.sin(b)*g,t+Math.cos(b)*g);o.push(s,t),o.push(n,i)}else{o.push(e,r),o.push(s,t);for(let y=1,b=f;y<m;y++,b+=x)o.push(s+Math.sin(b)*g,t+Math.cos(b)*g),o.push(s,t);o.push(n,i),o.push(s,t)}return m*2}function xi(s,t,e,r,n,i){const o=Ur;if(s.length===0)return;const a=t;let l=a.alignment;if(t.alignment!==.5){let A=gi(s);l=(l-.5)*A+.5}const c=new pt(s[0],s[1]),h=new pt(s[s.length-2],s[s.length-1]),u=r,f=Math.abs(c.x-h.x)<o&&Math.abs(c.y-h.y)<o;if(u){s=s.slice(),f&&(s.pop(),s.pop(),h.set(s[s.length-2],s[s.length-1]));const A=(c.x+h.x)*.5,rt=(h.y+c.y)*.5;s.unshift(A,rt),s.push(A,rt)}const d=n,p=s.length/2;let g=s.length;const m=d.length/2,x=a.width/2,y=x*x,b=a.miterLimit*a.miterLimit;let _=s[0],T=s[1],w=s[2],S=s[3],M=0,F=0,v=-(T-S),P=_-w,U=0,G=0,O=Math.sqrt(v*v+P*P);v/=O,P/=O,v*=x,P*=x;const it=l,k=(1-it)*2,B=it*2;u||(a.cap==="round"?g+=ht(_-v*(k-B)*.5,T-P*(k-B)*.5,_-v*k,T-P*k,_+v*B,T+P*B,d,!0)+2:a.cap==="square"&&(g+=Xe(_,T,v,P,k,B,!0,d))),d.push(_-v*k,T-P*k),d.push(_+v*B,T+P*B);for(let A=1;A<p-1;++A){_=s[(A-1)*2],T=s[(A-1)*2+1],w=s[A*2],S=s[A*2+1],M=s[(A+1)*2],F=s[(A+1)*2+1],v=-(T-S),P=_-w,O=Math.sqrt(v*v+P*P),v/=O,P/=O,v*=x,P*=x,U=-(S-F),G=w-M,O=Math.sqrt(U*U+G*G),U/=O,G/=O,U*=x,G*=x;const rt=w-_,bt=T-S,St=w-M,wt=F-S,We=rt*St+bt*wt,zt=bt*St-wt*rt,Tt=zt<0;if(Math.abs(zt)<.001*Math.abs(We)){d.push(w-v*k,S-P*k),d.push(w+v*B,S+P*B),We>=0&&(a.join==="round"?g+=ht(w,S,w-v*k,S-P*k,w-U*k,S-G*k,d,!1)+4:g+=2,d.push(w-U*B,S-G*B),d.push(w+U*k,S+G*k));continue}const De=(-v+_)*(-P+S)-(-v+w)*(-P+T),Ie=(-U+M)*(-G+S)-(-U+w)*(-G+F),At=(rt*Ie-St*De)/zt,Wt=(wt*De-bt*Ie)/zt,te=(At-w)*(At-w)+(Wt-S)*(Wt-S),st=w+(At-w)*k,ot=S+(Wt-S)*k,at=w-(At-w)*B,lt=S-(Wt-S)*B,fn=Math.min(rt*rt+bt*bt,St*St+wt*wt),Le=Tt?k:B,pn=fn+Le*Le*y;te<=pn?a.join==="bevel"||te/y>b?(Tt?(d.push(st,ot),d.push(w+v*B,S+P*B),d.push(st,ot),d.push(w+U*B,S+G*B)):(d.push(w-v*k,S-P*k),d.push(at,lt),d.push(w-U*k,S-G*k),d.push(at,lt)),g+=2):a.join==="round"?Tt?(d.push(st,ot),d.push(w+v*B,S+P*B),g+=ht(w,S,w+v*B,S+P*B,w+U*B,S+G*B,d,!0)+4,d.push(st,ot),d.push(w+U*B,S+G*B)):(d.push(w-v*k,S-P*k),d.push(at,lt),g+=ht(w,S,w-v*k,S-P*k,w-U*k,S-G*k,d,!1)+4,d.push(w-U*k,S-G*k),d.push(at,lt)):(d.push(st,ot),d.push(at,lt)):(d.push(w-v*k,S-P*k),d.push(w+v*B,S+P*B),a.join==="round"?Tt?g+=ht(w,S,w+v*B,S+P*B,w+U*B,S+G*B,d,!0)+2:g+=ht(w,S,w-v*k,S-P*k,w-U*k,S-G*k,d,!1)+2:a.join==="miter"&&te/y<=b&&(Tt?(d.push(at,lt),d.push(at,lt)):(d.push(st,ot),d.push(st,ot)),g+=2),d.push(w-U*k,S-G*k),d.push(w+U*B,S+G*B),g+=2)}_=s[(p-2)*2],T=s[(p-2)*2+1],w=s[(p-1)*2],S=s[(p-1)*2+1],v=-(T-S),P=_-w,O=Math.sqrt(v*v+P*P),v/=O,P/=O,v*=x,P*=x,d.push(w-v*k,S-P*k),d.push(w+v*B,S+P*B),u||(a.cap==="round"?g+=ht(w-v*(k-B)*.5,S-P*(k-B)*.5,w-v*k,S-P*k,w+v*B,S+P*B,d,!1)+2:a.cap==="square"&&(g+=Xe(w,S,v,P,k,B,!1,d)));const dn=qe*qe;for(let A=m;A<g+m-2;++A)_=d[A*2],T=d[A*2+1],w=d[(A+1)*2],S=d[(A+1)*2+1],M=d[(A+2)*2],F=d[(A+2)*2+1],!(Math.abs(_*(S-F)+w*(F-T)+M*(T-S))<dn)&&i.push(A,A+1,A+2)}function mi(s,t,e,r){const n=Ur;if(s.length===0)return;const i=s[0],o=s[1],a=s[s.length-2],l=s[s.length-1],c=t||Math.abs(i-a)<n&&Math.abs(o-l)<n,h=e,u=s.length/2,f=h.length/2;for(let d=0;d<u;d++)h.push(s[d*2]),h.push(s[d*2+1]);for(let d=0;d<u-1;d++)r.push(f+d,f+d+1);c&&r.push(f+u-1,f)}function zr(s,t,e,r,n,i,o){const a=Kn(s,t,2);if(!a)return;for(let c=0;c<a.length;c+=3)i[o++]=a[c]+n,i[o++]=a[c+1]+n,i[o++]=a[c+2]+n;let l=n*r;for(let c=0;c<s.length;c+=2)e[l]=s[c],e[l+1]=s[c+1],l+=r}const yi=[],_i={extension:{type:R.ShapeBuilder,name:"polygon"},build(s,t){for(let e=0;e<s.points.length;e++)t[e]=s.points[e];return!0},triangulate(s,t,e,r,n,i){zr(s,yi,t,e,r,n,i)}},bi={extension:{type:R.ShapeBuilder,name:"rectangle"},build(s,t){const e=s,r=e.x,n=e.y,i=e.width,o=e.height;return i>0&&o>0?(t[0]=r,t[1]=n,t[2]=r+i,t[3]=n,t[4]=r+i,t[5]=n+o,t[6]=r,t[7]=n+o,!0):!1},triangulate(s,t,e,r,n,i){let o=0;r*=e,t[r+o]=s[0],t[r+o+1]=s[1],o+=e,t[r+o]=s[2],t[r+o+1]=s[3],o+=e,t[r+o]=s[6],t[r+o+1]=s[7],o+=e,t[r+o]=s[4],t[r+o+1]=s[5],o+=e;const a=r/e;n[i++]=a,n[i++]=a+1,n[i++]=a+2,n[i++]=a+1,n[i++]=a+3,n[i++]=a+2}},Si={extension:{type:R.ShapeBuilder,name:"triangle"},build(s,t){return t[0]=s.x,t[1]=s.y,t[2]=s.x2,t[3]=s.y2,t[4]=s.x3,t[5]=s.y3,!0},triangulate(s,t,e,r,n,i){let o=0;r*=e,t[r+o]=s[0],t[r+o+1]=s[1],o+=e,t[r+o]=s[2],t[r+o+1]=s[3],o+=e,t[r+o]=s[4],t[r+o+1]=s[5];const a=r/e;n[i++]=a,n[i++]=a+1,n[i++]=a+2}},wi=new L,Ti=new Y;function Pi(s,t,e,r){const n=t.matrix?s.copyFrom(t.matrix).invert():s.identity();if(t.textureSpace==="local"){const o=e.getBounds(Ti);t.width&&o.pad(t.width);const{x:a,y:l}=o,c=1/o.width,h=1/o.height,u=-a*c,f=-l*h,d=n.a,p=n.b,g=n.c,m=n.d;n.a*=c,n.b*=c,n.c*=h,n.d*=h,n.tx=u*d+f*g+n.tx,n.ty=u*p+f*m+n.ty}else n.translate(t.texture.frame.x,t.texture.frame.y),n.scale(1/t.texture.source.width,1/t.texture.source.height);const i=t.texture.source.style;return!(t.fill instanceof et)&&i.addressMode==="clamp-to-edge"&&(i.addressMode="repeat",i.update()),r&&n.append(wi.copyFrom(r).invert()),n}const Jt={};V.handleByMap(R.ShapeBuilder,Jt);V.add(bi,_i,Si,Ut,fi,pi);const vi=new Y,Ci=new L;function Mi(s,t){const{geometryData:e,batches:r}=t;r.length=0,e.indices.length=0,e.vertices.length=0,e.uvs.length=0;for(let n=0;n<s.instructions.length;n++){const i=s.instructions[n];if(i.action==="texture")ki(i.data,r,e);else if(i.action==="fill"||i.action==="stroke"){const o=i.action==="stroke",a=i.data.path.shapePath,l=i.data.style,c=i.data.hole;o&&c&&Ke(c.shapePath,l,!0,r,e),c&&(a.shapePrimitives[a.shapePrimitives.length-1].holes=c.shapePath.shapePrimitives),Ke(a,l,o,r,e)}}}function ki(s,t,e){const r=[],n=Jt.rectangle,i=vi;i.x=s.dx,i.y=s.dy,i.width=s.dw,i.height=s.dh;const o=s.transform;if(!n.build(i,r))return;const{vertices:a,uvs:l,indices:c}=e,h=c.length,u=a.length/2;o&&Gr(r,o),n.triangulate(r,a,2,u,c,h);const f=s.image,d=f.uvs;l.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const p=tt.get(Fe);p.indexOffset=h,p.indexSize=c.length-h,p.attributeOffset=u,p.attributeSize=a.length/2-u,p.baseColor=s.style,p.alpha=s.alpha,p.texture=f,p.geometryData=e,t.push(p)}function Ke(s,t,e,r,n){const{vertices:i,uvs:o,indices:a}=n;s.shapePrimitives.forEach(({shape:l,transform:c,holes:h})=>{const u=[],f=Jt[l.type];if(!f.build(l,u))return;const d=a.length,p=i.length/2;let g="triangle-list";if(c&&Gr(u,c),e){const b=l.closePath??!0,_=t;_.pixelLine?(mi(u,b,i,a),g="line-list"):xi(u,_,!1,b,i,a)}else if(h){const b=[],_=u.slice();Fi(h).forEach(w=>{b.push(_.length/2),_.push(...w)}),zr(_,b,i,2,p,a,d)}else f.triangulate(u,i,2,p,a,d);const m=o.length/2,x=t.texture;if(x!==H.WHITE){const b=Pi(Ci,t,l,c);ci(i,2,p,o,m,2,i.length/2-p,b)}else ui(o,m,2,i.length/2-p);const y=tt.get(Fe);y.indexOffset=d,y.indexSize=a.length-d,y.attributeOffset=p,y.attributeSize=i.length/2-p,y.baseColor=t.color,y.alpha=t.alpha,y.texture=x,y.geometryData=n,y.topology=g,r.push(y)})}function Fi(s){const t=[];for(let e=0;e<s.length;e++){const r=s[e].shape,n=[];Jt[r.type].build(r,n)&&t.push(n)}return t}class Bi{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class Ri{constructor(){this.instructions=new yn}init(t){this.batcher=new Tn({maxTextures:t}),this.instructions.reset()}get geometry(){return z(_n,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const Be=class xe{constructor(t){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),this._renderer=t,t.renderableGC.addManagedHash(this,"_gpuContextHash"),t.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(t){xe.defaultOptions.bezierSmoothness=(t==null?void 0:t.bezierSmoothness)??xe.defaultOptions.bezierSmoothness}getContextRenderData(t){return this._graphicsDataContextHash[t.uid]||this._initContextRenderData(t)}updateGpuContext(t){let e=this._gpuContextHash[t.uid]||this._initContext(t);if(t.dirty){e?this._cleanGraphicsContextData(t):e=this._initContext(t),Mi(t,e);const r=t.batchMode;t.customShader||r==="no-batch"?e.isBatchable=!1:r==="auto"?e.isBatchable=e.geometryData.vertices.length<400:e.isBatchable=!0,t.dirty=!1}return e}getGpuContext(t){return this._gpuContextHash[t.uid]||this._initContext(t)}_initContextRenderData(t){const e=tt.get(Ri,{maxTextures:this._renderer.limits.maxBatchableTextures}),{batches:r,geometryData:n}=this._gpuContextHash[t.uid],i=n.vertices.length,o=n.indices.length;for(let h=0;h<r.length;h++)r[h].applyTransform=!1;const a=e.batcher;a.ensureAttributeBuffer(i),a.ensureIndexBuffer(o),a.begin();for(let h=0;h<r.length;h++){const u=r[h];a.add(u)}a.finish(e.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const c=a.batches;for(let h=0;h<c.length;h++){const u=c[h];u.bindGroup=wn(u.textures.textures,u.textures.count,this._renderer.limits.maxBatchableTextures)}return this._graphicsDataContextHash[t.uid]=e,e}_initContext(t){const e=new Bi;return e.context=t,this._gpuContextHash[t.uid]=e,t.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]}onGraphicsContextDestroy(t){this._cleanGraphicsContextData(t),t.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]=null}_cleanGraphicsContextData(t){const e=this._gpuContextHash[t.uid];e.isBatchable||this._graphicsDataContextHash[t.uid]&&(tt.return(this.getContextRenderData(t)),this._graphicsDataContextHash[t.uid]=null),e.batches&&e.batches.forEach(r=>{tt.return(r)})}destroy(){for(const t in this._gpuContextHash)this._gpuContextHash[t]&&this.onGraphicsContextDestroy(this._gpuContextHash[t].context)}};Be.extension={type:[R.WebGLSystem,R.WebGPUSystem,R.CanvasSystem],name:"graphicsContext"};Be.defaultOptions={bezierSmoothness:.5};let Re=Be;const Gi=8,Et=11920929e-14,Ui=1;function Ar(s,t,e,r,n,i,o,a,l,c){const u=Math.min(.99,Math.max(0,c??Re.defaultOptions.bezierSmoothness));let f=(Ui-u)/1;return f*=f,zi(t,e,r,n,i,o,a,l,s,f),s}function zi(s,t,e,r,n,i,o,a,l,c){me(s,t,e,r,n,i,o,a,l,c,0),l.push(o,a)}function me(s,t,e,r,n,i,o,a,l,c,h){if(h>Gi)return;const u=(s+e)/2,f=(t+r)/2,d=(e+n)/2,p=(r+i)/2,g=(n+o)/2,m=(i+a)/2,x=(u+d)/2,y=(f+p)/2,b=(d+g)/2,_=(p+m)/2,T=(x+b)/2,w=(y+_)/2;if(h>0){let S=o-s,M=a-t;const F=Math.abs((e-o)*M-(r-a)*S),v=Math.abs((n-o)*M-(i-a)*S);if(F>Et&&v>Et){if((F+v)*(F+v)<=c*(S*S+M*M)){l.push(T,w);return}}else if(F>Et){if(F*F<=c*(S*S+M*M)){l.push(T,w);return}}else if(v>Et){if(v*v<=c*(S*S+M*M)){l.push(T,w);return}}else if(S=T-(s+o)/2,M=w-(t+a)/2,S*S+M*M<=c){l.push(T,w);return}}me(s,t,u,f,x,y,T,w,l,c,h+1),me(T,w,b,_,g,m,o,a,l,c,h+1)}const Ai=8,Wi=11920929e-14,Di=1;function Ii(s,t,e,r,n,i,o,a){const c=Math.min(.99,Math.max(0,a??Re.defaultOptions.bezierSmoothness));let h=(Di-c)/1;return h*=h,Li(t,e,r,n,i,o,s,h),s}function Li(s,t,e,r,n,i,o,a){ye(o,s,t,e,r,n,i,a,0),o.push(n,i)}function ye(s,t,e,r,n,i,o,a,l){if(l>Ai)return;const c=(t+r)/2,h=(e+n)/2,u=(r+i)/2,f=(n+o)/2,d=(c+u)/2,p=(h+f)/2;let g=i-t,m=o-e;const x=Math.abs((r-i)*m-(n-o)*g);if(x>Wi){if(x*x<=a*(g*g+m*m)){s.push(d,p);return}}else if(g=d-(t+i)/2,m=p-(e+o)/2,g*g+m*m<=a){s.push(d,p);return}ye(s,t,e,c,h,d,p,a,l+1),ye(s,d,p,u,f,i,o,a,l+1)}function Wr(s,t,e,r,n,i,o,a){let l=Math.abs(n-i);(!o&&n>i||o&&i>n)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(r,1/3)*(l/Math.PI)))),a=Math.max(a,3);let c=l/a,h=n;c*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(h),d=Math.sin(h),p=t+f*r,g=e+d*r;s.push(p,g),h+=c}}function Hi(s,t,e,r,n,i){const o=s[s.length-2],l=s[s.length-1]-e,c=o-t,h=n-e,u=r-t,f=Math.abs(l*u-c*h);if(f<1e-8||i===0){(s[s.length-2]!==t||s[s.length-1]!==e)&&s.push(t,e);return}const d=l*l+c*c,p=h*h+u*u,g=l*h+c*u,m=i*Math.sqrt(d)/f,x=i*Math.sqrt(p)/f,y=m*g/d,b=x*g/p,_=m*u+x*c,T=m*h+x*l,w=c*(x+y),S=l*(x+y),M=u*(m+b),F=h*(m+b),v=Math.atan2(S-T,w-_),P=Math.atan2(F-T,M-_);Wr(s,_+t,T+e,i,v,P,c*h>u*l)}const kt=Math.PI*2,ne={centerX:0,centerY:0,ang1:0,ang2:0},ie=({x:s,y:t},e,r,n,i,o,a,l)=>{s*=e,t*=r;const c=n*s-i*t,h=i*s+n*t;return l.x=c+o,l.y=h+a,l};function Ei(s,t){const e=t===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(t/4),r=t===1.5707963267948966?.551915024494:e,n=Math.cos(s),i=Math.sin(s),o=Math.cos(s+t),a=Math.sin(s+t);return[{x:n-i*r,y:i+n*r},{x:o+a*r,y:a-o*r},{x:o,y:a}]}const Ze=(s,t,e,r)=>{const n=s*r-t*e<0?-1:1;let i=s*e+t*r;return i>1&&(i=1),i<-1&&(i=-1),n*Math.acos(i)},Vi=(s,t,e,r,n,i,o,a,l,c,h,u,f)=>{const d=Math.pow(n,2),p=Math.pow(i,2),g=Math.pow(h,2),m=Math.pow(u,2);let x=d*p-d*m-p*g;x<0&&(x=0),x/=d*m+p*g,x=Math.sqrt(x)*(o===a?-1:1);const y=x*n/i*u,b=x*-i/n*h,_=c*y-l*b+(s+e)/2,T=l*y+c*b+(t+r)/2,w=(h-y)/n,S=(u-b)/i,M=(-h-y)/n,F=(-u-b)/i,v=Ze(1,0,w,S);let P=Ze(w,S,M,F);a===0&&P>0&&(P-=kt),a===1&&P<0&&(P+=kt),f.centerX=_,f.centerY=T,f.ang1=v,f.ang2=P};function Oi(s,t,e,r,n,i,o,a=0,l=0,c=0){if(i===0||o===0)return;const h=Math.sin(a*kt/360),u=Math.cos(a*kt/360),f=u*(t-r)/2+h*(e-n)/2,d=-h*(t-r)/2+u*(e-n)/2;if(f===0&&d===0)return;i=Math.abs(i),o=Math.abs(o);const p=Math.pow(f,2)/Math.pow(i,2)+Math.pow(d,2)/Math.pow(o,2);p>1&&(i*=Math.sqrt(p),o*=Math.sqrt(p)),Vi(t,e,r,n,i,o,l,c,h,u,f,d,ne);let{ang1:g,ang2:m}=ne;const{centerX:x,centerY:y}=ne;let b=Math.abs(m)/(kt/4);Math.abs(1-b)<1e-7&&(b=1);const _=Math.max(Math.ceil(b),1);m/=_;let T=s[s.length-2],w=s[s.length-1];const S={x:0,y:0};for(let M=0;M<_;M++){const F=Ei(g,m),{x:v,y:P}=ie(F[0],i,o,u,h,x,y,S),{x:U,y:G}=ie(F[1],i,o,u,h,x,y,S),{x:O,y:it}=ie(F[2],i,o,u,h,x,y,S);Ar(s,T,w,v,P,U,G,O,it),T=O,w=it,g+=m}}function Yi(s,t,e){const r=(o,a)=>{const l=a.x-o.x,c=a.y-o.y,h=Math.sqrt(l*l+c*c),u=l/h,f=c/h;return{len:h,nx:u,ny:f}},n=(o,a)=>{o===0?s.moveTo(a.x,a.y):s.lineTo(a.x,a.y)};let i=t[t.length-1];for(let o=0;o<t.length;o++){const a=t[o%t.length],l=a.radius??e;if(l<=0){n(o,a),i=a;continue}const c=t[(o+1)%t.length],h=r(a,i),u=r(a,c);if(h.len<1e-4||u.len<1e-4){n(o,a),i=a;continue}let f=Math.asin(h.nx*u.ny-h.ny*u.nx),d=1,p=!1;h.nx*u.nx-h.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,p=!0):f>0&&(d=-1,p=!0);const g=f/2;let m,x=Math.abs(Math.cos(g)*l/Math.sin(g));x>Math.min(h.len/2,u.len/2)?(x=Math.min(h.len/2,u.len/2),m=Math.abs(x*Math.sin(g)/Math.cos(g))):m=l;const y=a.x+u.nx*x+-u.ny*m*d,b=a.y+u.ny*x+u.nx*m*d,_=Math.atan2(h.ny,h.nx)+Math.PI/2*d,T=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&s.moveTo(y+Math.cos(_)*m,b+Math.sin(_)*m),s.arc(y,b,m,_,T,p),i=a}}function ji(s,t,e,r){const n=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),i=(a,l,c)=>({x:a.x+(l.x-a.x)*c,y:a.y+(l.y-a.y)*c}),o=t.length;for(let a=0;a<o;a++){const l=t[(a+1)%o],c=l.radius??e;if(c<=0){a===0?s.moveTo(l.x,l.y):s.lineTo(l.x,l.y);continue}const h=t[a],u=t[(a+2)%o],f=n(h,l);let d;if(f<1e-4)d=l;else{const m=Math.min(f/2,c);d=i(l,h,m/f)}const p=n(u,l);let g;if(p<1e-4)g=l;else{const m=Math.min(p/2,c);g=i(l,u,m/p)}a===0?s.moveTo(d.x,d.y):s.lineTo(d.x,d.y),s.quadraticCurveTo(l.x,l.y,g.x,g.y,r)}}const $i=new Y;class Ni{constructor(t){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new Kt,this._graphicsPath2D=t,this.signed=t.checkForHoles}moveTo(t,e){return this.startPoly(t,e),this}lineTo(t,e){this._ensurePoly();const r=this._currentPoly.points,n=r[r.length-2],i=r[r.length-1];return(n!==t||i!==e)&&r.push(t,e),this}arc(t,e,r,n,i,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Wr(a,t,e,r,n,i,o),this}arcTo(t,e,r,n,i){this._ensurePoly();const o=this._currentPoly.points;return Hi(o,t,e,r,n,i),this}arcToSvg(t,e,r,n,i,o,a){const l=this._currentPoly.points;return Oi(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,t,e,r,n,i),this}bezierCurveTo(t,e,r,n,i,o,a){this._ensurePoly();const l=this._currentPoly;return Ar(this._currentPoly.points,l.lastX,l.lastY,t,e,r,n,i,o,a),this}quadraticCurveTo(t,e,r,n,i){this._ensurePoly();const o=this._currentPoly;return Ii(this._currentPoly.points,o.lastX,o.lastY,t,e,r,n,i),this}closePath(){return this.endPoly(!0),this}addPath(t,e){this.endPoly(),e&&!e.isIdentity()&&(t=t.clone(!0),t.transform(e));const r=this.shapePrimitives,n=r.length;for(let i=0;i<t.instructions.length;i++){const o=t.instructions[i];this[o.action](...o.data)}if(t.checkForHoles&&r.length-n>1){let i=null;for(let o=n;o<r.length;o++){const a=r[o];if(a.shape.type==="polygon"){const l=a.shape,c=i==null?void 0:i.shape;c&&c.containsPolygon(l)?(i.holes||(i.holes=[]),i.holes.push(a),r.copyWithin(o,o+1),r.length--,o--):i=a}}}return this}finish(t=!1){this.endPoly(t)}rect(t,e,r,n,i){return this.drawShape(new Y(t,e,r,n),i),this}circle(t,e,r,n){return this.drawShape(new Ce(t,e,r),n),this}poly(t,e,r){const n=new Mt(t);return n.closePath=e,this.drawShape(n,r),this}regularPoly(t,e,r,n,i=0,o){n=Math.max(n|0,3);const a=-1*Math.PI/2+i,l=Math.PI*2/n,c=[];for(let h=0;h<n;h++){const u=a-h*l;c.push(t+r*Math.cos(u),e+r*Math.sin(u))}return this.poly(c,!0,o),this}roundPoly(t,e,r,n,i,o=0,a){if(n=Math.max(n|0,3),i<=0)return this.regularPoly(t,e,r,n,o);const l=r*Math.sin(Math.PI/n)-.001;i=Math.min(i,l);const c=-1*Math.PI/2+o,h=Math.PI*2/n,u=(n-2)*Math.PI/n/2;for(let f=0;f<n;f++){const d=f*h+c,p=t+r*Math.cos(d),g=e+r*Math.sin(d),m=d+Math.PI+u,x=d-Math.PI-u,y=p+i*Math.cos(m),b=g+i*Math.sin(m),_=p+i*Math.cos(x),T=g+i*Math.sin(x);f===0?this.moveTo(y,b):this.lineTo(y,b),this.quadraticCurveTo(p,g,_,T,a)}return this.closePath()}roundShape(t,e,r=!1,n){return t.length<3?this:(r?ji(this,t,e,n):Yi(this,t,e),this.closePath())}filletRect(t,e,r,n,i){if(i===0)return this.rect(t,e,r,n);const o=Math.min(r,n)/2,a=Math.min(o,Math.max(-o,i)),l=t+r,c=e+n,h=a<0?-a:0,u=Math.abs(a);return this.moveTo(t,e+u).arcTo(t+h,e+h,t+u,e,u).lineTo(l-u,e).arcTo(l-h,e+h,l,e+u,u).lineTo(l,c-u).arcTo(l-h,c-h,t+r-u,c,u).lineTo(t+u,c).arcTo(t+h,c-h,t,c-u,u).closePath()}chamferRect(t,e,r,n,i,o){if(i<=0)return this.rect(t,e,r,n);const a=Math.min(i,Math.min(r,n)/2),l=t+r,c=e+n,h=[t+a,e,l-a,e,l,e+a,l,c-a,l-a,c,t+a,c,t,c-a,t,e+a];for(let u=h.length-1;u>=2;u-=2)h[u]===h[u-2]&&h[u-1]===h[u-3]&&h.splice(u-1,2);return this.poly(h,!0,o)}ellipse(t,e,r,n,i){return this.drawShape(new Me(t,e,r,n),i),this}roundRect(t,e,r,n,i,o){return this.drawShape(new ke(t,e,r,n,i),o),this}drawShape(t,e){return this.endPoly(),this.shapePrimitives.push({shape:t,transform:e}),this}startPoly(t,e){let r=this._currentPoly;return r&&this.endPoly(),r=new Mt,r.points.push(t,e),this._currentPoly=r,this}endPoly(t=!1){const e=this._currentPoly;return e&&e.points.length>2&&(e.closePath=t,this.shapePrimitives.push({shape:e})),this._currentPoly=null,this}_ensurePoly(t=!0){if(!this._currentPoly&&(this._currentPoly=new Mt,t)){const e=this.shapePrimitives[this.shapePrimitives.length-1];if(e){let r=e.shape.x,n=e.shape.y;if(e.transform&&!e.transform.isIdentity()){const i=e.transform,o=r;r=i.a*r+i.c*n+i.tx,n=i.b*o+i.d*n+i.ty}this._currentPoly.points.push(r,n)}else this._currentPoly.points.push(0,0)}}buildPath(){const t=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let e=0;e<t.instructions.length;e++){const r=t.instructions[e];this[r.action](...r.data)}this.finish()}get bounds(){const t=this._bounds;t.clear();const e=this.shapePrimitives;for(let r=0;r<e.length;r++){const n=e[r],i=n.shape.getBounds($i);n.transform?t.addRect(i,n.transform):t.addRect(i)}return t}}class yt{constructor(t,e=!1){this.instructions=[],this.uid=Xt("graphicsPath"),this._dirty=!0,this.checkForHoles=e,typeof t=="string"?oi(t,this):this.instructions=(t==null?void 0:t.slice())??[]}get shapePath(){return this._shapePath||(this._shapePath=new Ni(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(t,e){return t=t.clone(),this.instructions.push({action:"addPath",data:[t,e]}),this._dirty=!0,this}arc(...t){return this.instructions.push({action:"arc",data:t}),this._dirty=!0,this}arcTo(...t){return this.instructions.push({action:"arcTo",data:t}),this._dirty=!0,this}arcToSvg(...t){return this.instructions.push({action:"arcToSvg",data:t}),this._dirty=!0,this}bezierCurveTo(...t){return this.instructions.push({action:"bezierCurveTo",data:t}),this._dirty=!0,this}bezierCurveToShort(t,e,r,n,i){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(pt.shared);let l=0,c=0;if(!o||o.action!=="bezierCurveTo")l=a.x,c=a.y;else{l=o.data[2],c=o.data[3];const h=a.x,u=a.y;l=h+(h-l),c=u+(u-c)}return this.instructions.push({action:"bezierCurveTo",data:[l,c,t,e,r,n,i]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...t){return this.instructions.push({action:"ellipse",data:t}),this._dirty=!0,this}lineTo(...t){return this.instructions.push({action:"lineTo",data:t}),this._dirty=!0,this}moveTo(...t){return this.instructions.push({action:"moveTo",data:t}),this}quadraticCurveTo(...t){return this.instructions.push({action:"quadraticCurveTo",data:t}),this._dirty=!0,this}quadraticCurveToShort(t,e,r){const n=this.instructions[this.instructions.length-1],i=this.getLastPoint(pt.shared);let o=0,a=0;if(!n||n.action!=="quadraticCurveTo")o=i.x,a=i.y;else{o=n.data[0],a=n.data[1];const l=i.x,c=i.y;o=l+(l-o),a=c+(c-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,t,e,r]}),this._dirty=!0,this}rect(t,e,r,n,i){return this.instructions.push({action:"rect",data:[t,e,r,n,i]}),this._dirty=!0,this}circle(t,e,r,n){return this.instructions.push({action:"circle",data:[t,e,r,n]}),this._dirty=!0,this}roundRect(...t){return this.instructions.push({action:"roundRect",data:t}),this._dirty=!0,this}poly(...t){return this.instructions.push({action:"poly",data:t}),this._dirty=!0,this}regularPoly(...t){return this.instructions.push({action:"regularPoly",data:t}),this._dirty=!0,this}roundPoly(...t){return this.instructions.push({action:"roundPoly",data:t}),this._dirty=!0,this}roundShape(...t){return this.instructions.push({action:"roundShape",data:t}),this._dirty=!0,this}filletRect(...t){return this.instructions.push({action:"filletRect",data:t}),this._dirty=!0,this}chamferRect(...t){return this.instructions.push({action:"chamferRect",data:t}),this._dirty=!0,this}star(t,e,r,n,i,o,a){i||(i=n/2);const l=-1*Math.PI/2+o,c=r*2,h=Math.PI*2/c,u=[];for(let f=0;f<c;f++){const d=f%2?i:n,p=f*h+l;u.push(t+d*Math.cos(p),e+d*Math.sin(p))}return this.poly(u,!0,a),this}clone(t=!1){const e=new yt;if(e.checkForHoles=this.checkForHoles,!t)e.instructions=this.instructions.slice();else for(let r=0;r<this.instructions.length;r++){const n=this.instructions[r];e.instructions.push({action:n.action,data:n.data.slice()})}return e}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(t){if(t.isIdentity())return this;const e=t.a,r=t.b,n=t.c,i=t.d,o=t.tx,a=t.ty;let l=0,c=0,h=0,u=0,f=0,d=0,p=0,g=0;for(let m=0;m<this.instructions.length;m++){const x=this.instructions[m],y=x.data;switch(x.action){case"moveTo":case"lineTo":l=y[0],c=y[1],y[0]=e*l+n*c+o,y[1]=r*l+i*c+a;break;case"bezierCurveTo":h=y[0],u=y[1],f=y[2],d=y[3],l=y[4],c=y[5],y[0]=e*h+n*u+o,y[1]=r*h+i*u+a,y[2]=e*f+n*d+o,y[3]=r*f+i*d+a,y[4]=e*l+n*c+o,y[5]=r*l+i*c+a;break;case"quadraticCurveTo":h=y[0],u=y[1],l=y[2],c=y[3],y[0]=e*h+n*u+o,y[1]=r*h+i*u+a,y[2]=e*l+n*c+o,y[3]=r*l+i*c+a;break;case"arcToSvg":l=y[5],c=y[6],p=y[0],g=y[1],y[0]=e*p+n*g,y[1]=r*p+i*g,y[5]=e*l+n*c+o,y[6]=r*l+i*c+a;break;case"circle":y[4]=Pt(y[3],t);break;case"rect":y[4]=Pt(y[4],t);break;case"ellipse":y[8]=Pt(y[8],t);break;case"roundRect":y[5]=Pt(y[5],t);break;case"addPath":y[0].transform(t);break;case"poly":y[2]=Pt(y[2],t);break;default:Z("unknown transform action",x.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(t){let e=this.instructions.length-1,r=this.instructions[e];if(!r)return t.x=0,t.y=0,t;for(;r.action==="closePath";){if(e--,e<0)return t.x=0,t.y=0,t;r=this.instructions[e]}switch(r.action){case"moveTo":case"lineTo":t.x=r.data[0],t.y=r.data[1];break;case"quadraticCurveTo":t.x=r.data[2],t.y=r.data[3];break;case"bezierCurveTo":t.x=r.data[4],t.y=r.data[5];break;case"arc":case"arcToSvg":t.x=r.data[5],t.y=r.data[6];break;case"addPath":r.data[0].getLastPoint(t);break}return t}}function Pt(s,t){return s?s.prepend(t):t.clone()}function D(s,t,e){const r=s.getAttribute(t);return r?Number(r):e}function qi(s,t){const e=s.querySelectorAll("defs");for(let r=0;r<e.length;r++){const n=e[r];for(let i=0;i<n.children.length;i++){const o=n.children[i];switch(o.nodeName.toLowerCase()){case"lineargradient":t.defs[o.id]=Xi(o);break;case"radialgradient":t.defs[o.id]=Ki();break}}}}function Xi(s){const t=D(s,"x1",0),e=D(s,"y1",0),r=D(s,"x2",1),n=D(s,"y2",0),i=s.getAttribute("gradientUnits")||"objectBoundingBox",o=new et(t,e,r,n,i==="objectBoundingBox"?"local":"global");for(let a=0;a<s.children.length;a++){const l=s.children[a],c=D(l,"offset",0),h=E.shared.setValue(l.getAttribute("stop-color")).toNumber();o.addColorStop(c,h)}return o}function Ki(s){return Z("[SVG Parser] Radial gradients are not yet supported"),new et(0,0,1,0)}function Qe(s){const t=s.match(/url\s*\(\s*['"]?\s*#([^'"\s)]+)\s*['"]?\s*\)/i);return t?t[1]:""}const Je={fill:{type:"paint",default:0},"fill-opacity":{type:"number",default:1},stroke:{type:"paint",default:0},"stroke-width":{type:"number",default:1},"stroke-opacity":{type:"number",default:1},"stroke-linecap":{type:"string",default:"butt"},"stroke-linejoin":{type:"string",default:"miter"},"stroke-miterlimit":{type:"number",default:10},"stroke-dasharray":{type:"string",default:"none"},"stroke-dashoffset":{type:"number",default:0},opacity:{type:"number",default:1}};function Dr(s,t){const e=s.getAttribute("style"),r={},n={},i={strokeStyle:r,fillStyle:n,useFill:!1,useStroke:!1};for(const o in Je){const a=s.getAttribute(o);a&&tr(t,i,o,a.trim())}if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const l=o[a].trim(),[c,h]=l.split(":");Je[c]&&tr(t,i,c,h.trim())}}return{strokeStyle:i.useStroke?r:null,fillStyle:i.useFill?n:null,useFill:i.useFill,useStroke:i.useStroke}}function tr(s,t,e,r){switch(e){case"stroke":if(r!=="none"){if(r.startsWith("url(")){const n=Qe(r);t.strokeStyle.fill=s.defs[n]}else t.strokeStyle.color=E.shared.setValue(r).toNumber();t.useStroke=!0}break;case"stroke-width":t.strokeStyle.width=Number(r);break;case"fill":if(r!=="none"){if(r.startsWith("url(")){const n=Qe(r);t.fillStyle.fill=s.defs[n]}else t.fillStyle.color=E.shared.setValue(r).toNumber();t.useFill=!0}break;case"fill-opacity":t.fillStyle.alpha=Number(r);break;case"stroke-opacity":t.strokeStyle.alpha=Number(r);break;case"opacity":t.fillStyle.alpha=Number(r),t.strokeStyle.alpha=Number(r);break}}function Zi(s,t){if(typeof s=="string"){const o=document.createElement("div");o.innerHTML=s.trim(),s=o.querySelector("svg")}const e={context:t,defs:{},path:new yt};qi(s,e);const r=s.children,{fillStyle:n,strokeStyle:i}=Dr(s,e);for(let o=0;o<r.length;o++){const a=r[o];a.nodeName.toLowerCase()!=="defs"&&Ir(a,e,n,i)}return t}function Ir(s,t,e,r){const n=s.children,{fillStyle:i,strokeStyle:o}=Dr(s,t);i&&e?e={...e,...i}:i&&(e=i),o&&r?r={...r,...o}:o&&(r=o);const a=!e&&!r;a&&(e={color:0});let l,c,h,u,f,d,p,g,m,x,y,b,_,T,w,S,M;switch(s.nodeName.toLowerCase()){case"path":T=s.getAttribute("d"),s.getAttribute("fill-rule")==="evenodd"&&Z("SVG Evenodd fill rule not supported, your svg may render incorrectly"),w=new yt(T,!0),t.context.path(w),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"circle":p=D(s,"cx",0),g=D(s,"cy",0),m=D(s,"r",0),t.context.ellipse(p,g,m,m),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"rect":l=D(s,"x",0),c=D(s,"y",0),S=D(s,"width",0),M=D(s,"height",0),x=D(s,"rx",0),y=D(s,"ry",0),x||y?t.context.roundRect(l,c,S,M,x||y):t.context.rect(l,c,S,M),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"ellipse":p=D(s,"cx",0),g=D(s,"cy",0),x=D(s,"rx",0),y=D(s,"ry",0),t.context.beginPath(),t.context.ellipse(p,g,x,y),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"line":h=D(s,"x1",0),u=D(s,"y1",0),f=D(s,"x2",0),d=D(s,"y2",0),t.context.beginPath(),t.context.moveTo(h,u),t.context.lineTo(f,d),r&&t.context.stroke(r);break;case"polygon":_=s.getAttribute("points"),b=_.match(/\d+/g).map(F=>parseInt(F,10)),t.context.poly(b,!0),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"polyline":_=s.getAttribute("points"),b=_.match(/\d+/g).map(F=>parseInt(F,10)),t.context.poly(b,!1),r&&t.context.stroke(r);break;case"g":case"svg":break;default:{Z(`[SVG parser] <${s.nodeName}> elements unsupported`);break}}a&&(e=null);for(let F=0;F<n.length;F++)Ir(n[F],t,e,r)}function Qi(s){return E.isColorLike(s)}function er(s){return s instanceof Qt}function rr(s){return s instanceof et}function Ji(s){return s instanceof H}function ts(s,t,e){const r=E.shared.setValue(t??0);return s.color=r.toNumber(),s.alpha=r.alpha===1?e.alpha:r.alpha,s.texture=H.WHITE,{...e,...s}}function es(s,t,e){return s.texture=t,{...e,...s}}function nr(s,t,e){return s.fill=t,s.color=16777215,s.texture=t.texture,s.matrix=t.transform,{...e,...s}}function ir(s,t,e){return t.buildGradient(),s.fill=t,s.color=16777215,s.texture=t.texture,s.matrix=t.transform,s.textureSpace=t.textureSpace,{...e,...s}}function rs(s,t){const e={...t,...s},r=E.shared.setValue(e.color);return e.alpha*=r.alpha,e.color=r.toNumber(),e}function ut(s,t){if(s==null)return null;const e={},r=s;return Qi(s)?ts(e,s,t):Ji(s)?es(e,s,t):er(s)?nr(e,s,t):rr(s)?ir(e,s,t):r.fill&&er(r.fill)?nr(r,r.fill,t):r.fill&&rr(r.fill)?ir(r,r.fill,t):rs(r,t)}function $t(s,t){const{width:e,alignment:r,miterLimit:n,cap:i,join:o,pixelLine:a,...l}=t,c=ut(s,l);return c?{width:e,alignment:r,miterLimit:n,cap:i,join:o,pixelLine:a,...c}:null}const ns=new pt,sr=new L,Ge=class $ extends be{constructor(){super(...arguments),this.uid=Xt("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new yt,this._transform=new L,this._fillStyle={...$.defaultFillStyle},this._strokeStyle={...$.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new Kt,this._boundsDirty=!0}clone(){const t=new $;return t.batchMode=this.batchMode,t.instructions=this.instructions.slice(),t._activePath=this._activePath.clone(),t._transform=this._transform.clone(),t._fillStyle={...this._fillStyle},t._strokeStyle={...this._strokeStyle},t._stateStack=this._stateStack.slice(),t._bounds=this._bounds.clone(),t._boundsDirty=!0,t}get fillStyle(){return this._fillStyle}set fillStyle(t){this._fillStyle=ut(t,$.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(t){this._strokeStyle=$t(t,$.defaultStrokeStyle)}setFillStyle(t){return this._fillStyle=ut(t,$.defaultFillStyle),this}setStrokeStyle(t){return this._strokeStyle=ut(t,$.defaultStrokeStyle),this}texture(t,e,r,n,i,o){return this.instructions.push({action:"texture",data:{image:t,dx:r||0,dy:n||0,dw:i||t.frame.width,dh:o||t.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:e?E.shared.setValue(e).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new yt,this}fill(t,e){let r;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="stroke"?r=n.data.path:r=this._activePath.clone(),r?(t!=null&&(e!==void 0&&typeof t=="number"&&(z(I,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),t={color:t,alpha:e}),this._fillStyle=ut(t,$.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:r}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:t,y:e}=this._activePath.getLastPoint(pt.shared);this._activePath.clear(),this._activePath.moveTo(t,e)}stroke(t){let e;const r=this.instructions[this.instructions.length-1];return this._tick===0&&r&&r.action==="fill"?e=r.data.path:e=this._activePath.clone(),e?(t!=null&&(this._strokeStyle=$t(t,$.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:e}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let t=0;t<2;t++){const e=this.instructions[this.instructions.length-1-t],r=this._activePath.clone();if(e&&(e.action==="stroke"||e.action==="fill"))if(e.data.hole)e.data.hole.addPath(r);else{e.data.hole=r;break}}return this._initNextPathLocation(),this}arc(t,e,r,n,i,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*t+a.c*e+a.tx,a.b*t+a.d*e+a.ty,r,n,i,o),this}arcTo(t,e,r,n,i){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*r+o.c*n+o.tx,o.b*r+o.d*n+o.ty,i),this}arcToSvg(t,e,r,n,i,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(t,e,r,n,i,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(t,e,r,n,i,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*t+l.c*e+l.tx,l.b*t+l.d*e+l.ty,l.a*r+l.c*n+l.tx,l.b*r+l.d*n+l.ty,l.a*i+l.c*o+l.tx,l.b*i+l.d*o+l.ty,a),this}closePath(){var t;return this._tick++,(t=this._activePath)==null||t.closePath(),this}ellipse(t,e,r,n){return this._tick++,this._activePath.ellipse(t,e,r,n,this._transform.clone()),this}circle(t,e,r){return this._tick++,this._activePath.circle(t,e,r,this._transform.clone()),this}path(t){return this._tick++,this._activePath.addPath(t,this._transform.clone()),this}lineTo(t,e){this._tick++;const r=this._transform;return this._activePath.lineTo(r.a*t+r.c*e+r.tx,r.b*t+r.d*e+r.ty),this}moveTo(t,e){this._tick++;const r=this._transform,n=this._activePath.instructions,i=r.a*t+r.c*e+r.tx,o=r.b*t+r.d*e+r.ty;return n.length===1&&n[0].action==="moveTo"?(n[0].data[0]=i,n[0].data[1]=o,this):(this._activePath.moveTo(i,o),this)}quadraticCurveTo(t,e,r,n,i){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*r+o.c*n+o.tx,o.b*r+o.d*n+o.ty,i),this}rect(t,e,r,n){return this._tick++,this._activePath.rect(t,e,r,n,this._transform.clone()),this}roundRect(t,e,r,n,i){return this._tick++,this._activePath.roundRect(t,e,r,n,i,this._transform.clone()),this}poly(t,e){return this._tick++,this._activePath.poly(t,e,this._transform.clone()),this}regularPoly(t,e,r,n,i=0,o){return this._tick++,this._activePath.regularPoly(t,e,r,n,i,o),this}roundPoly(t,e,r,n,i,o){return this._tick++,this._activePath.roundPoly(t,e,r,n,i,o),this}roundShape(t,e,r,n){return this._tick++,this._activePath.roundShape(t,e,r,n),this}filletRect(t,e,r,n,i){return this._tick++,this._activePath.filletRect(t,e,r,n,i),this}chamferRect(t,e,r,n,i,o){return this._tick++,this._activePath.chamferRect(t,e,r,n,i,o),this}star(t,e,r,n,i=0,o=0){return this._tick++,this._activePath.star(t,e,r,n,i,o,this._transform.clone()),this}svg(t){return this._tick++,Zi(t,this),this}restore(){const t=this._stateStack.pop();return t&&(this._transform=t.transform,this._fillStyle=t.fillStyle,this._strokeStyle=t.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(t){return this._transform.rotate(t),this}scale(t,e=t){return this._transform.scale(t,e),this}setTransform(t,e,r,n,i,o){return t instanceof L?(this._transform.set(t.a,t.b,t.c,t.d,t.tx,t.ty),this):(this._transform.set(t,e,r,n,i,o),this)}transform(t,e,r,n,i,o){return t instanceof L?(this._transform.append(t),this):(sr.set(t,e,r,n,i,o),this._transform.append(sr),this)}translate(t,e=t){return this._transform.translate(t,e),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const t=this._bounds;t.clear();for(let e=0;e<this.instructions.length;e++){const r=this.instructions[e],n=r.action;if(n==="fill"){const i=r.data;t.addBounds(i.path.bounds)}else if(n==="texture"){const i=r.data;t.addFrame(i.dx,i.dy,i.dx+i.dw,i.dy+i.dh,i.transform)}if(n==="stroke"){const i=r.data,o=i.style.alignment,a=i.style.width*(1-o),l=i.path.bounds;t.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a)}}return t}containsPoint(t){var n;if(!this.bounds.containsPoint(t.x,t.y))return!1;const e=this.instructions;let r=!1;for(let i=0;i<e.length;i++){const o=e[i],a=o.data,l=a.path;if(!o.action||!l)continue;const c=a.style,h=l.shapePath.shapePrimitives;for(let u=0;u<h.length;u++){const f=h[u].shape;if(!c||!f)continue;const d=h[u].transform,p=d?d.applyInverse(t,ns):t;if(o.action==="fill")r=f.contains(p.x,p.y);else{const m=c;r=f.strokeContains(p.x,p.y,m.width,m.alignment)}const g=a.hole;if(g){const m=(n=g.shapePath)==null?void 0:n.shapePrimitives;if(m)for(let x=0;x<m.length;x++)m[x].shape.contains(p.x,p.y)&&(r=!1)}if(r)return!0}}return r}destroy(t=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const r=typeof t=="boolean"?t:t==null?void 0:t.textureSource;this._fillStyle.texture&&(this._fillStyle.fill&&"uid"in this._fillStyle.fill?this._fillStyle.fill.destroy():this._fillStyle.texture.destroy(r)),this._strokeStyle.texture&&(this._strokeStyle.fill&&"uid"in this._strokeStyle.fill?this._strokeStyle.fill.destroy():this._strokeStyle.texture.destroy(r))}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};Ge.defaultFillStyle={color:16777215,alpha:1,texture:H.WHITE,matrix:null,fill:null,textureSpace:"local"};Ge.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:H.WHITE,matrix:null,fill:null,textureSpace:"local",pixelLine:!1};let j=Ge;const or=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function is(s){const t=[];let e=0;for(let r=0;r<or.length;r++){const n=`_${or[r]}`;t[e++]=s[n]}return e=Lr(s._fill,t,e),e=os(s._stroke,t,e),e=as(s.dropShadow,t,e),e=ss(s.filters,t,e),t.join("-")}function ss(s,t,e){if(!s)return e;for(const r of s)t[e++]=r.uid;return e}function Lr(s,t,e){var r;return s&&(t[e++]=s.color,t[e++]=s.alpha,t[e++]=(r=s.fill)==null?void 0:r.styleKey),e}function os(s,t,e){return s&&(e=Lr(s,t,e),t[e++]=s.width,t[e++]=s.alignment,t[e++]=s.cap,t[e++]=s.join,t[e++]=s.miterLimit),e}function as(s,t,e){return s&&(t[e++]=s.alpha,t[e++]=s.angle,t[e++]=s.blur,t[e++]=s.distance,t[e++]=E.shared.setValue(s.color).toNumber()),e}const Ue=class ft extends be{constructor(t={}){super(),ls(t);const e={...ft.defaultTextStyle,...t};for(const r in e){const n=r;this[n]=e[r]}this.update()}get align(){return this._align}set align(t){this._align=t,this.update()}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords=t,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(t){t!==null&&typeof t=="object"?this._dropShadow=this._createProxy({...ft.defaultDropShadow,...t}):this._dropShadow=t?this._createProxy({...ft.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(t){this._fontFamily=t,this.update()}get fontSize(){return this._fontSize}set fontSize(t){typeof t=="string"?this._fontSize=parseInt(t,10):this._fontSize=t,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle=t.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant=t,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight=t,this.update()}get leading(){return this._leading}set leading(t){this._leading=t,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing=t,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight=t,this.update()}get padding(){return this._padding}set padding(t){this._padding=t,this.update()}get filters(){return this._filters}set filters(t){this._filters=t,this.update()}get trim(){return this._trim}set trim(t){this._trim=t,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline=t,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace=t,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap=t,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth=t,this.update()}get fill(){return this._originalFill}set fill(t){t!==this._originalFill&&(this._originalFill=t,this._isFillStyle(t)&&(this._originalFill=this._createProxy({...j.defaultFillStyle,...t},()=>{this._fill=ut({...this._originalFill},j.defaultFillStyle)})),this._fill=ut(t===0?"black":t,j.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(t){t!==this._originalStroke&&(this._originalStroke=t,this._isFillStyle(t)&&(this._originalStroke=this._createProxy({...j.defaultStrokeStyle,...t},()=>{this._stroke=$t({...this._originalStroke},j.defaultStrokeStyle)})),this._stroke=$t(t,j.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=is(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const t=ft.defaultTextStyle;for(const e in t)this[e]=t[e]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new ft({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,filters:this._filters?[...this._filters]:void 0})}_getFinalPadding(){let t=0;if(this._filters)for(let e=0;e<this._filters.length;e++)t+=this._filters[e].padding;return Math.max(this._padding,t)}destroy(t=!1){var r,n,i,o;if(this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const a=typeof t=="boolean"?t:t==null?void 0:t.textureSource;(r=this._fill)!=null&&r.texture&&this._fill.texture.destroy(a),(n=this._originalFill)!=null&&n.texture&&this._originalFill.texture.destroy(a),(i=this._stroke)!=null&&i.texture&&this._stroke.texture.destroy(a),(o=this._originalStroke)!=null&&o.texture&&this._originalStroke.texture.destroy(a)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(t,e){return new Proxy(t,{set:(r,n,i)=>(r[n]=i,e==null||e(n,i),this.update(),!0)})}_isFillStyle(t){return(t??null)!==null&&!(E.isColorLike(t)||t instanceof et||t instanceof Qt)}};Ue.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};Ue.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let _t=Ue;function ls(s){const t=s;if(typeof t.dropShadow=="boolean"&&t.dropShadow){const e=_t.defaultDropShadow;s.dropShadow={alpha:t.dropShadowAlpha??e.alpha,angle:t.dropShadowAngle??e.angle,blur:t.dropShadowBlur??e.blur,color:t.dropShadowColor??e.color,distance:t.dropShadowDistance??e.distance}}if(t.strokeThickness!==void 0){z(I,"strokeThickness is now a part of stroke");const e=t.stroke;let r={};if(E.isColorLike(e))r.color=e;else if(e instanceof et||e instanceof Qt)r.fill=e;else if(Object.hasOwnProperty.call(e,"color")||Object.hasOwnProperty.call(e,"fill"))r=e;else throw new Error("Invalid stroke value.");s.stroke={...r,width:t.strokeThickness}}if(Array.isArray(t.fillGradientStops)){if(z(I,"gradient fill is now a fill pattern: `new FillGradient(...)`"),!Array.isArray(t.fill)||t.fill.length===0)throw new Error("Invalid fill value. Expected an array of colors for gradient fill.");t.fill.length!==t.fillGradientStops.length&&Z("The number of fill colors must match the number of fill gradient stops.");const e=new et({start:{x:0,y:0},end:{x:0,y:1},textureSpace:"local"}),r=t.fillGradientStops.slice(),n=t.fill.map(i=>E.shared.setValue(i).toNumber());r.forEach((i,o)=>{e.addColorStop(i,n[o])}),s.fill={fill:e}}}const ar=1e5;function Nt(s,t,e,r=0){if(s.texture===H.WHITE&&!s.fill)return E.shared.setValue(s.color).setAlpha(s.alpha??1).toHexa();if(s.fill){if(s.fill instanceof Qt){const n=s.fill,i=t.createPattern(n.texture.source.resource,"repeat"),o=n.transform.copyTo(L.shared);return o.scale(n.texture.frame.width,n.texture.frame.height),i.setTransform(o),i}else if(s.fill instanceof et){const n=s.fill,i=n.type==="linear",o=n.textureSpace==="local";let a=1,l=1;o&&e&&(a=e.width+r,l=e.height+r);let c,h=!1;if(i){const{start:u,end:f}=n;c=t.createLinearGradient(u.x*a,u.y*l,f.x*a,f.y*l),h=Math.abs(f.x-u.x)<Math.abs((f.y-u.y)*.1)}else{const{center:u,innerRadius:f,outerCenter:d,outerRadius:p}=n;c=t.createRadialGradient(u.x*a,u.y*l,f*a,d.x*a,d.y*l,p*a)}if(h&&o&&e){const u=e.lineHeight/l;for(let f=0;f<e.lines.length;f++){const d=(f*e.lineHeight+r/2)/l;n.colorStops.forEach(p=>{const g=d+p.offset*u;c.addColorStop(Math.floor(g*ar)/ar,E.shared.setValue(p.color).toHex())})}}else n.colorStops.forEach(u=>{c.addColorStop(u.offset,E.shared.setValue(u.color).toHex())});return c}}else{const n=t.createPattern(s.texture.source.resource,"repeat"),i=s.matrix.copyTo(L.shared);return i.scale(s.texture.frame.width,s.texture.frame.height),n.setTransform(i),n}return Z("FillStyle not recognised",s),"red"}const Hr=class Er extends Zn{constructor(t){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentMaxCharHeight=0,this._currentPageIndex=-1,this._skipKerning=!1;const e={...Er.defaultOptions,...t};this._textureSize=e.textureSize,this._mipmap=e.mipmap;const r=e.style.clone();e.overrideFill&&(r._fill.color=16777215,r._fill.alpha=1,r._fill.texture=H.WHITE,r._fill.fill=null),this.applyFillAsTint=e.overrideFill;const n=r.fontSize;r.fontSize=this.baseMeasurementFontSize;const i=jt(r);e.overrideSize?r._stroke&&(r._stroke.width*=this.baseRenderedFontSize/n):r.fontSize=this.baseRenderedFontSize=n,this._style=r,this._skipKerning=e.skipKerning??!1,this.resolution=e.resolution??1,this._padding=e.padding??4,e.textureStyle&&(this._textureStyle=e.textureStyle instanceof Ft?e.textureStyle:new Ft(e.textureStyle)),this.fontMetrics=K.measureFont(i),this.lineHeight=r.lineHeight||this.fontMetrics.fontSize||r.fontSize}ensureCharacters(t){var m,x;const e=K.graphemeSegmenter(t).filter(y=>!this._currentChars.includes(y)).filter((y,b,_)=>_.indexOf(y)===b);if(!e.length)return;this._currentChars=[...this._currentChars,...e];let r;this._currentPageIndex===-1?r=this._nextPage():r=this.pages[this._currentPageIndex];let{canvas:n,context:i}=r.canvasAndContext,o=r.texture.source;const a=this._style;let l=this._currentX,c=this._currentY,h=this._currentMaxCharHeight;const u=this.baseRenderedFontSize/this.baseMeasurementFontSize,f=this._padding*u;let d=!1;const p=n.width/this.resolution,g=n.height/this.resolution;for(let y=0;y<e.length;y++){const b=e[y],_=K.measureText(b,a,n,!1);_.lineHeight=_.height;const T=_.width*u,w=Math.ceil((a.fontStyle==="italic"?2:1)*T),S=_.height*u,M=w+f*2,F=S+f*2;if(d=!1,b!==`
`&&b!=="\r"&&b!=="	"&&b!==" "&&(d=!0,h=Math.ceil(Math.max(F,h))),l+M>p&&(c+=h,h=F,l=0,c+h>g)){o.update();const P=this._nextPage();n=P.canvasAndContext.canvas,i=P.canvasAndContext.context,o=P.texture.source,l=0,c=0,h=0}const v=T/u-(((m=a.dropShadow)==null?void 0:m.distance)??0)-(((x=a._stroke)==null?void 0:x.width)??0);if(this.chars[b]={id:b.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:v,kerning:{}},d){this._drawGlyph(i,_,l+f,c+f,u,a);const P=o.width*u,U=o.height*u,G=new Y(l/P*o.width,c/U*o.height,M/P*o.width,F/U*o.height);this.chars[b].texture=new H({source:o,frame:G}),l+=Math.ceil(M)}}o.update(),this._currentX=l,this._currentY=c,this._currentMaxCharHeight=h,this._skipKerning&&this._applyKerning(e,i)}get pageTextures(){return z(I,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(t,e){const r=this._measureCache;for(let n=0;n<t.length;n++){const i=t[n];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let l=r[i];l||(l=r[i]=e.measureText(i).width);let c=r[a];c||(c=r[a]=e.measureText(a).width);let h=e.measureText(i+a).width,u=h-(l+c);u&&(this.chars[i].kerning[a]=u),h=e.measureText(i+a).width,u=h-(l+c),u&&(this.chars[a].kerning[i]=u)}}}_nextPage(){this._currentPageIndex++;const t=this.resolution,e=gt.getOptimalCanvasAndContext(this._textureSize,this._textureSize,t);this._setupContext(e.context,this._style,t);const r=t*(this.baseRenderedFontSize/this.baseMeasurementFontSize),n=new H({source:new ue({resource:e.canvas,resolution:r,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})});this._textureStyle&&(n.source.style=this._textureStyle);const i={canvasAndContext:e,texture:n};return this.pages[this._currentPageIndex]=i,i}_setupContext(t,e,r){e.fontSize=this.baseRenderedFontSize,t.scale(r,r),t.font=jt(e),e.fontSize=this.baseMeasurementFontSize,t.textBaseline=e.textBaseline;const n=e._stroke,i=(n==null?void 0:n.width)??0;if(n&&(t.lineWidth=i,t.lineJoin=n.join,t.miterLimit=n.miterLimit,t.strokeStyle=Nt(n,t)),e._fill&&(t.fillStyle=Nt(e._fill,t)),e.dropShadow){const o=e.dropShadow,a=E.shared.setValue(o.color).toArray(),l=o.blur*r,c=o.distance*r;t.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,t.shadowBlur=l,t.shadowOffsetX=Math.cos(o.angle)*c,t.shadowOffsetY=Math.sin(o.angle)*c}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0}_drawGlyph(t,e,r,n,i,o){const a=e.text,l=e.fontProperties,c=o._stroke,h=((c==null?void 0:c.width)??0)*i,u=r+h/2,f=n-h/2,d=l.descent*i,p=e.lineHeight*i;let g=!1;o.stroke&&h&&(g=!0,t.strokeText(a,u,f+p-d));const{shadowBlur:m,shadowOffsetX:x,shadowOffsetY:y}=t;o._fill&&(g&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.fillText(a,u,f+p-d)),g&&(t.shadowBlur=m,t.shadowOffsetX=x,t.shadowOffsetY=y)}destroy(){super.destroy();for(let t=0;t<this.pages.length;t++){const{canvasAndContext:e,texture:r}=this.pages[t];gt.returnCanvasAndContext(e),r.destroy(!0)}this.pages=null}};Hr.defaultOptions={textureSize:512,style:new _t,mipmap:!0};let lr=Hr;function Vr(s,t,e,r){const n={width:0,height:0,offsetY:0,scale:t.fontSize/e.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};n.offsetY=e.baseLineOffset;let i=n.lines[0],o=null,a=!0;const l={width:0,start:0,index:0,positions:[],chars:[]},c=e.baseMeasurementFontSize/t.fontSize,h=t.letterSpacing*c,u=t.wordWrapWidth*c,f=t.lineHeight?t.lineHeight*c:e.lineHeight,d=t.wordWrap&&t.breakWords,p=x=>{const y=i.width;for(let b=0;b<l.index;b++){const _=x.positions[b];i.chars.push(x.chars[b]),i.charPositions.push(_+y)}i.width+=x.width,a=!1,l.width=0,l.index=0,l.chars.length=0},g=()=>{let x=i.chars.length-1;if(r){let y=i.chars[x];for(;y===" ";)i.width-=e.chars[y].xAdvance,y=i.chars[--x]}n.width=Math.max(n.width,i.width),i={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,n.lines.push(i),n.height+=f},m=x=>x-h>u;for(let x=0;x<s.length+1;x++){let y;const b=x===s.length;b||(y=s[x]);const _=e.chars[y]||e.chars[" "];if(/(?:\s)/.test(y)||y==="\r"||y===`
`||b){if(!a&&t.wordWrap&&m(i.width+l.width)?(g(),p(l),b||i.charPositions.push(0)):(l.start=i.width,p(l),b||i.charPositions.push(0)),y==="\r"||y===`
`)i.width!==0&&g();else if(!b){const M=_.xAdvance+(_.kerning[o]||0)+h;i.width+=M,i.spaceWidth=M,i.spacesIndex.push(i.charPositions.length),i.chars.push(y)}}else{const S=_.kerning[o]||0,M=_.xAdvance+S+h;d&&m(i.width+l.width+M)&&(p(l),g()),l.positions[l.index++]=l.width+S,l.chars.push(y),l.width+=M}o=y}return g(),t.align==="center"?hs(n):t.align==="right"?cs(n):t.align==="justify"&&us(n),n}function hs(s){for(let t=0;t<s.lines.length;t++){const e=s.lines[t],r=s.width/2-e.width/2;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=r}}function cs(s){for(let t=0;t<s.lines.length;t++){const e=s.lines[t],r=s.width-e.width;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=r}}function us(s){const t=s.width;for(let e=0;e<s.lines.length;e++){const r=s.lines[e];let n=0,i=r.spacesIndex[n++],o=0;const a=r.spacesIndex.length,c=(t-r.width)/a;for(let h=0;h<r.charPositions.length;h++)h===i&&(i=r.spacesIndex[n++],o+=c),r.charPositions[h]+=o}}function ds(s){if(s==="")return[];typeof s=="string"&&(s=[s]);const t=[];for(let e=0,r=s.length;e<r;e++){const n=s[e];if(Array.isArray(n)){if(n.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${n.length}.`);if(n[0].length===0||n[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const i=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<i)throw new Error("[BitmapFont]: Invalid character range.");for(let a=i,l=o;a<=l;a++)t.push(String.fromCharCode(a))}else t.push(...Array.from(n))}if(t.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}let Vt=0;class fs{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1,textureStyle:null}}getFont(t,e){var o;let r=`${e.fontFamily}-bitmap`,n=!0;if(e._fill.fill&&!e._stroke)r+=e._fill.fill.styleKey,n=!1;else if(e._stroke||e.dropShadow){let a=e.styleKey;a=a.substring(0,a.lastIndexOf("-")),r=`${a}-bitmap`,n=!1}if(!N.has(r)){const a=Object.create(e);a.lineHeight=0;const l=new lr({style:a,overrideFill:n,overrideSize:!0,...this.defaultOptions});Vt++,Vt>50&&Z("BitmapText",`You have dynamically created ${Vt} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),l.once("destroy",()=>{Vt--,N.remove(r)}),N.set(r,l)}const i=N.get(r);return(o=i.ensureCharacters)==null||o.call(i,t),i}getLayout(t,e,r=!0){const n=this.getFont(t,e),i=K.graphemeSegmenter(t);return Vr(i,e,n,r)}measureText(t,e,r=!0){return this.getLayout(t,e,r)}install(...t){var c,h,u,f;let e=t[0];typeof e=="string"&&(e={name:e,style:t[1],chars:(c=t[2])==null?void 0:c.chars,resolution:(h=t[2])==null?void 0:h.resolution,padding:(u=t[2])==null?void 0:u.padding,skipKerning:(f=t[2])==null?void 0:f.skipKerning},z(I,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const r=e==null?void 0:e.name;if(!r)throw new Error("[BitmapFontManager] Property `name` is required.");e={...this.defaultOptions,...e};const n=e.style,i=n instanceof _t?n:new _t(n),o=e.dynamicFill??this._canUseTintForStyle(i),a=new lr({style:i,overrideFill:o,skipKerning:e.skipKerning,padding:e.padding,resolution:e.resolution,overrideSize:!1,textureStyle:e.textureStyle}),l=ds(e.chars);return a.ensureCharacters(l.join("")),N.set(`${r}-bitmap`,a),a.once("destroy",()=>N.remove(`${r}-bitmap`)),a}uninstall(t){const e=`${t}-bitmap`,r=N.get(e);r&&r.destroy()}_canUseTintForStyle(t){return!t._stroke&&(!t.dropShadow||t.dropShadow.color===0)&&!t._fill.fill&&t._fill.color===16777215}}const ps=new fs;class Or{constructor(t){this._renderer=t}push(t,e,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",canBundle:!1,action:"pushFilter",container:e,filterEffect:t})}pop(t,e,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",action:"popFilter",canBundle:!1})}execute(t){t.action==="pushFilter"?this._renderer.filter.push(t):t.action==="popFilter"&&this._renderer.filter.pop()}destroy(){this._renderer=null}}Or.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"filter"};function gs(s,t){t.clear();const e=t.matrix;for(let r=0;r<s.length;r++){const n=s[r];n.globalDisplayStatus<7||(t.matrix=n.worldTransform,t.addBounds(n.bounds))}return t.matrix=e,t}const xs=new Se({attributes:{aPosition:{buffer:new Float32Array([0,0,1,0,1,1,0,1]),format:"float32x2",stride:2*4,offset:0}},indexBuffer:new Uint32Array([0,1,2,0,2,3])});class ms{constructor(){this.skip=!1,this.inputTexture=null,this.backTexture=null,this.filters=null,this.bounds=new Kt,this.container=null,this.blendRequired=!1,this.outputRenderSurface=null,this.globalFrame={x:0,y:0,width:0,height:0}}}class Yr{constructor(t){this._filterStackIndex=0,this._filterStack=[],this._filterGlobalUniforms=new xt({uInputSize:{value:new Float32Array(4),type:"vec4<f32>"},uInputPixel:{value:new Float32Array(4),type:"vec4<f32>"},uInputClamp:{value:new Float32Array(4),type:"vec4<f32>"},uOutputFrame:{value:new Float32Array(4),type:"vec4<f32>"},uGlobalFrame:{value:new Float32Array(4),type:"vec4<f32>"},uOutputTexture:{value:new Float32Array(4),type:"vec4<f32>"}}),this._globalFilterBindGroup=new br({}),this.renderer=t}get activeBackTexture(){var t;return(t=this._activeFilterData)==null?void 0:t.backTexture}push(t){const e=this.renderer,r=t.filterEffect.filters,n=this._pushFilterData();n.skip=!1,n.filters=r,n.container=t.container,n.outputRenderSurface=e.renderTarget.renderSurface;const i=e.renderTarget.renderTarget.colorTexture.source,o=i.resolution,a=i.antialias;if(r.length===0){n.skip=!0;return}const l=n.bounds;if(this._calculateFilterArea(t,l),this._calculateFilterBounds(n,e.renderTarget.rootViewPort,a,o,1),n.skip)return;const c=this._getPreviousFilterData(),h=this._findFilterResolution(o);let u=0,f=0;c&&(u=c.bounds.minX,f=c.bounds.minY),this._calculateGlobalFrame(n,u,f,h,i.width,i.height),this._setupFilterTextures(n,l,e,c)}generateFilteredTexture({texture:t,filters:e}){const r=this._pushFilterData();this._activeFilterData=r,r.skip=!1,r.filters=e;const n=t.source,i=n.resolution,o=n.antialias;if(e.length===0)return r.skip=!0,t;const a=r.bounds;if(a.addRect(t.frame),this._calculateFilterBounds(r,a.rectangle,o,i,0),r.skip)return t;const l=i;this._calculateGlobalFrame(r,0,0,l,n.width,n.height),r.outputRenderSurface=q.getOptimalTexture(a.width,a.height,r.resolution,r.antialias),r.backTexture=H.EMPTY,r.inputTexture=t,this.renderer.renderTarget.finishRenderPass(),this._applyFiltersToTexture(r,!0);const f=r.outputRenderSurface;return f.source.alphaMode="premultiplied-alpha",f}pop(){const t=this.renderer,e=this._popFilterData();e.skip||(t.globalUniforms.pop(),t.renderTarget.finishRenderPass(),this._activeFilterData=e,this._applyFiltersToTexture(e,!1),e.blendRequired&&q.returnTexture(e.backTexture),q.returnTexture(e.inputTexture))}getBackTexture(t,e,r){const n=t.colorTexture.source._resolution,i=q.getOptimalTexture(e.width,e.height,n,!1);let o=e.minX,a=e.minY;r&&(o-=r.minX,a-=r.minY),o=Math.floor(o*n),a=Math.floor(a*n);const l=Math.ceil(e.width*n),c=Math.ceil(e.height*n);return this.renderer.renderTarget.copyToTexture(t,i,{x:o,y:a},{width:l,height:c},{x:0,y:0}),i}applyFilter(t,e,r,n){const i=this.renderer,o=this._activeFilterData,l=o.outputRenderSurface===r,c=i.renderTarget.rootRenderTarget.colorTexture.source._resolution,h=this._findFilterResolution(c);let u=0,f=0;if(l){const d=this._findPreviousFilterOffset();u=d.x,f=d.y}this._updateFilterUniforms(e,r,o,u,f,h,l,n),this._setupBindGroupsAndRender(t,e,i)}calculateSpriteMatrix(t,e){const r=this._activeFilterData,n=t.set(r.inputTexture._source.width,0,0,r.inputTexture._source.height,r.bounds.minX,r.bounds.minY),i=e.worldTransform.copyTo(L.shared),o=e.renderGroup||e.parentRenderGroup;return o&&o.cacheToLocalTransform&&i.prepend(o.cacheToLocalTransform),i.invert(),n.prepend(i),n.scale(1/e.texture.orig.width,1/e.texture.orig.height),n.translate(e.anchor.x,e.anchor.y),n}destroy(){}_setupBindGroupsAndRender(t,e,r){if(r.renderPipes.uniformBatch){const n=r.renderPipes.uniformBatch.getUboResource(this._filterGlobalUniforms);this._globalFilterBindGroup.setResource(n,0)}else this._globalFilterBindGroup.setResource(this._filterGlobalUniforms,0);this._globalFilterBindGroup.setResource(e.source,1),this._globalFilterBindGroup.setResource(e.source.style,2),t.groups[0]=this._globalFilterBindGroup,r.encoder.draw({geometry:xs,shader:t,state:t._state,topology:"triangle-list"}),r.type===we.WEBGL&&r.renderTarget.finishRenderPass()}_setupFilterTextures(t,e,r,n){if(t.backTexture=H.EMPTY,t.blendRequired){r.renderTarget.finishRenderPass();const i=r.renderTarget.getRenderTarget(t.outputRenderSurface);t.backTexture=this.getBackTexture(i,e,n==null?void 0:n.bounds)}t.inputTexture=q.getOptimalTexture(e.width,e.height,t.resolution,t.antialias),r.renderTarget.bind(t.inputTexture,!0),r.globalUniforms.push({offset:e})}_calculateGlobalFrame(t,e,r,n,i,o){const a=t.globalFrame;a.x=e*n,a.y=r*n,a.width=i*n,a.height=o*n}_updateFilterUniforms(t,e,r,n,i,o,a,l){const c=this._filterGlobalUniforms.uniforms,h=c.uOutputFrame,u=c.uInputSize,f=c.uInputPixel,d=c.uInputClamp,p=c.uGlobalFrame,g=c.uOutputTexture;a?(h[0]=r.bounds.minX-n,h[1]=r.bounds.minY-i):(h[0]=0,h[1]=0),h[2]=t.frame.width,h[3]=t.frame.height,u[0]=t.source.width,u[1]=t.source.height,u[2]=1/u[0],u[3]=1/u[1],f[0]=t.source.pixelWidth,f[1]=t.source.pixelHeight,f[2]=1/f[0],f[3]=1/f[1],d[0]=.5*f[2],d[1]=.5*f[3],d[2]=t.frame.width*u[2]-.5*f[2],d[3]=t.frame.height*u[3]-.5*f[3];const m=this.renderer.renderTarget.rootRenderTarget.colorTexture;p[0]=n*o,p[1]=i*o,p[2]=m.source.width*o,p[3]=m.source.height*o,e instanceof H&&(e.source.resource=null);const x=this.renderer.renderTarget.getRenderTarget(e);this.renderer.renderTarget.bind(e,!!l),e instanceof H?(g[0]=e.frame.width,g[1]=e.frame.height):(g[0]=x.width,g[1]=x.height),g[2]=x.isRoot?-1:1,this._filterGlobalUniforms.update()}_findFilterResolution(t){let e=this._filterStackIndex-1;for(;e>0&&this._filterStack[e].skip;)--e;return e>0&&this._filterStack[e].inputTexture?this._filterStack[e].inputTexture.source._resolution:t}_findPreviousFilterOffset(){let t=0,e=0,r=this._filterStackIndex;for(;r>0;){r--;const n=this._filterStack[r];if(!n.skip){t=n.bounds.minX,e=n.bounds.minY;break}}return{x:t,y:e}}_calculateFilterArea(t,e){if(t.renderables?gs(t.renderables,e):t.filterEffect.filterArea?(e.clear(),e.addRect(t.filterEffect.filterArea),e.applyMatrix(t.container.worldTransform)):t.container.getFastGlobalBounds(!0,e),t.container){const n=(t.container.renderGroup||t.container.parentRenderGroup).cacheToLocalTransform;n&&e.applyMatrix(n)}}_applyFiltersToTexture(t,e){const r=t.inputTexture,n=t.bounds,i=t.filters;if(this._globalFilterBindGroup.setResource(r.source.style,2),this._globalFilterBindGroup.setResource(t.backTexture.source,3),i.length===1)i[0].apply(this,r,t.outputRenderSurface,e);else{let o=t.inputTexture;const a=q.getOptimalTexture(n.width,n.height,o.source._resolution,!1);let l=a,c=0;for(c=0;c<i.length-1;++c){i[c].apply(this,o,l,!0);const u=o;o=l,l=u}i[c].apply(this,o,t.outputRenderSurface,e),q.returnTexture(a)}}_calculateFilterBounds(t,e,r,n,i){var g;const o=this.renderer,a=t.bounds,l=t.filters;let c=1/0,h=0,u=!0,f=!1,d=!1,p=!0;for(let m=0;m<l.length;m++){const x=l[m];if(c=Math.min(c,x.resolution==="inherit"?n:x.resolution),h+=x.padding,x.antialias==="off"?u=!1:x.antialias==="inherit"&&u&&(u=r),x.clipToViewport||(p=!1),!!!(x.compatibleRenderers&o.type)){d=!1;break}if(x.blendRequired&&!(((g=o.backBuffer)==null?void 0:g.useBackBuffer)??!0)){Z("Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options."),d=!1;break}d=x.enabled||d,f||(f=x.blendRequired)}if(!d){t.skip=!0;return}if(p&&a.fitBounds(0,e.width/n,0,e.height/n),a.scale(c).ceil().scale(1/c).pad((h|0)*i),!a.isPositive){t.skip=!0;return}t.antialias=u,t.resolution=c,t.blendRequired=f}_popFilterData(){return this._filterStackIndex--,this._filterStack[this._filterStackIndex]}_getPreviousFilterData(){let t,e=this._filterStackIndex-1;for(;e>1&&(e--,t=this._filterStack[e],!!t.skip););return t}_pushFilterData(){let t=this._filterStack[this._filterStackIndex];return t||(t=this._filterStack[this._filterStackIndex]=new ms),this._filterStackIndex++,t}}Yr.extension={type:[R.WebGLSystem,R.WebGPUSystem],name:"filter"};class qt extends bn{constructor(t){t instanceof j&&(t={context:t});const{context:e,roundPixels:r,...n}=t||{};super({label:"Graphics",...n}),this.renderPipeId="graphics",e?this._context=e:this._context=this._ownedContext=new j,this._context.on("update",this.onViewUpdate,this),this.didViewUpdate=!0,this.allowChildren=!1,this.roundPixels=r??!1}set context(t){t!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=t,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(t){return this._context.containsPoint(t)}destroy(t){this._ownedContext&&!t?this._ownedContext.destroy(t):(t===!0||(t==null?void 0:t.context)===!0)&&this._context.destroy(t),this._ownedContext=null,this._context=null,super.destroy(t)}_callContextMethod(t,e){return this.context[t](...e),this}setFillStyle(...t){return this._callContextMethod("setFillStyle",t)}setStrokeStyle(...t){return this._callContextMethod("setStrokeStyle",t)}fill(...t){return this._callContextMethod("fill",t)}stroke(...t){return this._callContextMethod("stroke",t)}texture(...t){return this._callContextMethod("texture",t)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...t){return this._callContextMethod("arc",t)}arcTo(...t){return this._callContextMethod("arcTo",t)}arcToSvg(...t){return this._callContextMethod("arcToSvg",t)}bezierCurveTo(...t){return this._callContextMethod("bezierCurveTo",t)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...t){return this._callContextMethod("ellipse",t)}circle(...t){return this._callContextMethod("circle",t)}path(...t){return this._callContextMethod("path",t)}lineTo(...t){return this._callContextMethod("lineTo",t)}moveTo(...t){return this._callContextMethod("moveTo",t)}quadraticCurveTo(...t){return this._callContextMethod("quadraticCurveTo",t)}rect(...t){return this._callContextMethod("rect",t)}roundRect(...t){return this._callContextMethod("roundRect",t)}poly(...t){return this._callContextMethod("poly",t)}regularPoly(...t){return this._callContextMethod("regularPoly",t)}roundPoly(...t){return this._callContextMethod("roundPoly",t)}roundShape(...t){return this._callContextMethod("roundShape",t)}filletRect(...t){return this._callContextMethod("filletRect",t)}chamferRect(...t){return this._callContextMethod("chamferRect",t)}star(...t){return this._callContextMethod("star",t)}svg(...t){return this._callContextMethod("svg",t)}restore(...t){return this._callContextMethod("restore",t)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...t){return this._callContextMethod("rotate",t)}scaleTransform(...t){return this._callContextMethod("scale",t)}setTransform(...t){return this._callContextMethod("setTransform",t)}transform(...t){return this._callContextMethod("transform",t)}translateTransform(...t){return this._callContextMethod("translate",t)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._context.fillStyle=t}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._context.strokeStyle=t}clone(t=!1){return t?new qt(this._context.clone()):(this._ownedContext=null,new qt(this._context))}lineStyle(t,e,r){z(I,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const n={};return t&&(n.width=t),e&&(n.color=e),r&&(n.alpha=r),this.context.strokeStyle=n,this}beginFill(t,e){z(I,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const r={};return t!==void 0&&(r.color=t),e!==void 0&&(r.alpha=e),this.context.fillStyle=r,this}endFill(){z(I,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const t=this.context.strokeStyle;return(t.width!==j.defaultStrokeStyle.width||t.color!==j.defaultStrokeStyle.color||t.alpha!==j.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...t){return z(I,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",t)}drawEllipse(...t){return z(I,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",t)}drawPolygon(...t){return z(I,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",t)}drawRect(...t){return z(I,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",t)}drawRoundedRect(...t){return z(I,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",t)}drawStar(...t){return z(I,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",t)}}const jr=class $r extends Se{constructor(...t){let e=t[0]??{};e instanceof Float32Array&&(z(I,"use new MeshGeometry({ positions, uvs, indices }) instead"),e={positions:e,uvs:t[1],indices:t[2]}),e={...$r.defaultOptions,...e};const r=e.positions||new Float32Array([0,0,1,0,1,1,0,1]);let n=e.uvs;n||(e.positions?n=new Float32Array(r.length):n=new Float32Array([0,0,1,0,1,1,0,1]));const i=e.indices||new Uint32Array([0,1,2,0,2,3]),o=e.shrinkBuffersToFit,a=new Ct({data:r,label:"attribute-mesh-positions",shrinkToFit:o,usage:X.VERTEX|X.COPY_DST}),l=new Ct({data:n,label:"attribute-mesh-uvs",shrinkToFit:o,usage:X.VERTEX|X.COPY_DST}),c=new Ct({data:i,label:"index-mesh-buffer",shrinkToFit:o,usage:X.INDEX|X.COPY_DST});super({attributes:{aPosition:{buffer:a,format:"float32x2",stride:2*4,offset:0},aUV:{buffer:l,format:"float32x2",stride:2*4,offset:0}},indexBuffer:c,topology:e.topology}),this.batchMode="auto"}get positions(){return this.attributes.aPosition.buffer.data}set positions(t){this.attributes.aPosition.buffer.data=t}get uvs(){return this.attributes.aUV.buffer.data}set uvs(t){this.attributes.aUV.buffer.data=t}get indices(){return this.indexBuffer.data}set indices(t){this.indexBuffer.data=t}};jr.defaultOptions={topology:"triangle-list",shrinkBuffersToFit:!1};let ze=jr,ct=null,J=null;function ys(s,t){ct||(ct=nt.get().createCanvas(256,128),J=ct.getContext("2d",{willReadFrequently:!0}),J.globalCompositeOperation="copy",J.globalAlpha=1),(ct.width<s||ct.height<t)&&(ct.width=Ee(s),ct.height=Ee(t))}function hr(s,t,e){for(let r=0,n=4*e*t;r<t;++r,n+=4)if(s[n+3]!==0)return!1;return!0}function cr(s,t,e,r,n){const i=4*t;for(let o=r,a=r*i+4*e;o<=n;++o,a+=i)if(s[a+3]!==0)return!1;return!0}function _s(...s){let t=s[0];t.canvas||(t={canvas:s[0],resolution:s[1]});const{canvas:e}=t,r=Math.min(t.resolution??1,1),n=t.width??e.width,i=t.height??e.height;let o=t.output;if(ys(n,i),!J)throw new TypeError("Failed to get canvas 2D context");J.drawImage(e,0,0,n,i,0,0,n*r,i*r);const l=J.getImageData(0,0,n,i).data;let c=0,h=0,u=n-1,f=i-1;for(;h<i&&hr(l,n,h);)++h;if(h===i)return Y.EMPTY;for(;hr(l,n,f);)--f;for(;cr(l,n,c,h,f);)++c;for(;cr(l,n,u,h,f);)--u;return++u,++f,J.globalCompositeOperation="source-over",J.strokeRect(c,h,u-c,f-h),J.globalCompositeOperation="copy",o??(o=new Y),o.set(c/r,h/r,(u-c)/r,(f-h)/r),o}const ur=new Y;class bs{getCanvasAndContext(t){const{text:e,style:r,resolution:n=1}=t,i=r._getFinalPadding(),o=K.measureText(e||" ",r),a=Math.ceil(Math.ceil(Math.max(1,o.width)+i*2)*n),l=Math.ceil(Math.ceil(Math.max(1,o.height)+i*2)*n),c=gt.getOptimalCanvasAndContext(a,l);this._renderTextToCanvas(e,r,i,n,c);const h=r.trim?_s({canvas:c.canvas,width:a,height:l,resolution:1,output:ur}):ur.set(0,0,a,l);return{canvasAndContext:c,frame:h}}returnCanvasAndContext(t){gt.returnCanvasAndContext(t)}_renderTextToCanvas(t,e,r,n,i){var b,_,T,w;const{canvas:o,context:a}=i,l=jt(e),c=K.measureText(t||" ",e),h=c.lines,u=c.lineHeight,f=c.lineWidths,d=c.maxLineWidth,p=c.fontProperties,g=o.height;if(a.resetTransform(),a.scale(n,n),a.textBaseline=e.textBaseline,(b=e._stroke)!=null&&b.width){const S=e._stroke;a.lineWidth=S.width,a.miterLimit=S.miterLimit,a.lineJoin=S.join,a.lineCap=S.cap}a.font=l;let m,x;const y=e.dropShadow?2:1;for(let S=0;S<y;++S){const M=e.dropShadow&&S===0,F=M?Math.ceil(Math.max(1,g)+r*2):0,v=F*n;if(M){a.fillStyle="black",a.strokeStyle="black";const G=e.dropShadow,O=G.color,it=G.alpha;a.shadowColor=E.shared.setValue(O).setAlpha(it).toRgbaString();const k=G.blur*n,B=G.distance*n;a.shadowBlur=k,a.shadowOffsetX=Math.cos(G.angle)*B,a.shadowOffsetY=Math.sin(G.angle)*B+v}else{if(a.fillStyle=e._fill?Nt(e._fill,a,c,r*2):null,(_=e._stroke)!=null&&_.width){const G=e._stroke.width*.5+r*2;a.strokeStyle=Nt(e._stroke,a,c,G)}a.shadowColor="black"}let P=(u-p.fontSize)/2;u-p.fontSize<0&&(P=0);const U=((T=e._stroke)==null?void 0:T.width)??0;for(let G=0;G<h.length;G++)m=U/2,x=U/2+G*u+p.ascent+P,e.align==="right"?m+=d-f[G]:e.align==="center"&&(m+=(d-f[G])/2),(w=e._stroke)!=null&&w.width&&this._drawLetterSpacing(h[G],e,i,m+r,x+r-F,!0),e._fill!==void 0&&this._drawLetterSpacing(h[G],e,i,m+r,x+r-F)}}_drawLetterSpacing(t,e,r,n,i,o=!1){const{context:a}=r,l=e.letterSpacing;let c=!1;if(K.experimentalLetterSpacingSupported&&(K.experimentalLetterSpacing?(a.letterSpacing=`${l}px`,a.textLetterSpacing=`${l}px`,c=!0):(a.letterSpacing="0px",a.textLetterSpacing="0px")),l===0||c){o?a.strokeText(t,n,i):a.fillText(t,n,i);return}let h=n;const u=K.graphemeSegmenter(t);let f=a.measureText(t).width,d=0;for(let p=0;p<u.length;++p){const g=u[p];o?a.strokeText(g,h,i):a.fillText(g,h,i);let m="";for(let x=p+1;x<u.length;++x)m+=u[x];d=a.measureText(m).width,h+=f-d+l,f=d}}}const se=new bs,dr="http://www.w3.org/2000/svg",fr="http://www.w3.org/1999/xhtml";class Nr{constructor(){this.svgRoot=document.createElementNS(dr,"svg"),this.foreignObject=document.createElementNS(dr,"foreignObject"),this.domElement=document.createElementNS(fr,"div"),this.styleElement=document.createElementNS(fr,"style");const{foreignObject:t,svgRoot:e,styleElement:r,domElement:n}=this;t.setAttribute("width","10000"),t.setAttribute("height","10000"),t.style.overflow="hidden",e.appendChild(t),t.appendChild(r),t.appendChild(n),this.image=nt.get().createImage()}}let pr;function Ss(s,t,e,r){r||(r=pr||(pr=new Nr));const{domElement:n,styleElement:i,svgRoot:o}=r;n.innerHTML=`<style>${t.cssStyle};</style><div style='padding:0'>${s}</div>`,n.setAttribute("style","transform-origin: top left; display: inline-block"),e&&(i.textContent=e),document.body.appendChild(o);const a=n.getBoundingClientRect();o.remove();const l=t.padding*2;return{width:a.width-l,height:a.height-l}}class ws{constructor(){this.batches=[],this.batched=!1}destroy(){this.batches.forEach(t=>{tt.return(t)}),this.batches.length=0}}class qr{constructor(t,e){this.state=Yt.for2d(),this.renderer=t,this._adaptor=e,this.renderer.runners.contextChange.add(this)}contextChange(){this._adaptor.contextChange(this.renderer)}validateRenderable(t){const e=t.context,r=!!t._gpuData,n=this.renderer.graphicsContext.updateGpuContext(e);return!!(n.isBatchable||r!==n.isBatchable)}addRenderable(t,e){const r=this.renderer.graphicsContext.updateGpuContext(t.context);t.didViewUpdate&&this._rebuild(t),r.isBatchable?this._addToBatcher(t,e):(this.renderer.renderPipes.batch.break(e),e.add(t))}updateRenderable(t){const r=this._getGpuDataForRenderable(t).batches;for(let n=0;n<r.length;n++){const i=r[n];i._batcher.updateElement(i)}}execute(t){if(!t.isRenderable)return;const e=this.renderer,r=t.context;if(!e.graphicsContext.getGpuContext(r).batches.length)return;const i=r.customShader||this._adaptor.shader;this.state.blendMode=t.groupBlendMode;const o=i.resources.localUniforms.uniforms;o.uTransformMatrix=t.groupTransform,o.uRound=e._roundPixels|t._roundPixels,Zt(t.groupColorAlpha,o.uColor,0),this._adaptor.execute(this,t)}_rebuild(t){const e=this._getGpuDataForRenderable(t),r=this.renderer.graphicsContext.updateGpuContext(t.context);e.destroy(),r.isBatchable&&this._updateBatchesForRenderable(t,e)}_addToBatcher(t,e){const r=this.renderer.renderPipes.batch,n=this._getGpuDataForRenderable(t).batches;for(let i=0;i<n.length;i++){const o=n[i];r.addToBatch(o,e)}}_getGpuDataForRenderable(t){return t._gpuData[this.renderer.uid]||this._initGpuDataForRenderable(t)}_initGpuDataForRenderable(t){const e=new ws;return t._gpuData[this.renderer.uid]=e,e}_updateBatchesForRenderable(t,e){const r=t.context,n=this.renderer.graphicsContext.getGpuContext(r),i=this.renderer._roundPixels|t._roundPixels;e.batches=n.batches.map(o=>{const a=tt.get(Fe);return o.copyTo(a),a.renderable=t,a.roundPixels=i,a})}destroy(){this.renderer=null,this._adaptor.destroy(),this._adaptor=null,this.state=null}}qr.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"graphics"};const Xr=class Kr extends ze{constructor(...t){super({});let e=t[0]??{};typeof e=="number"&&(z(I,"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead"),e={width:e,height:t[1],verticesX:t[2],verticesY:t[3]}),this.build(e)}build(t){t={...Kr.defaultOptions,...t},this.verticesX=this.verticesX??t.verticesX,this.verticesY=this.verticesY??t.verticesY,this.width=this.width??t.width,this.height=this.height??t.height;const e=this.verticesX*this.verticesY,r=[],n=[],i=[],o=this.verticesX-1,a=this.verticesY-1,l=this.width/o,c=this.height/a;for(let u=0;u<e;u++){const f=u%this.verticesX,d=u/this.verticesX|0;r.push(f*l,d*c),n.push(f/o,d/a)}const h=o*a;for(let u=0;u<h;u++){const f=u%o,d=u/o|0,p=d*this.verticesX+f,g=d*this.verticesX+f+1,m=(d+1)*this.verticesX+f,x=(d+1)*this.verticesX+f+1;i.push(p,g,m,g,x,m)}this.buffers[0].data=new Float32Array(r),this.buffers[1].data=new Float32Array(n),this.indexBuffer.data=new Uint32Array(i),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}};Xr.defaultOptions={width:100,height:100,verticesX:10,verticesY:10};let Ts=Xr;class Ae{constructor(){this.batcherName="default",this.packAsQuad=!1,this.indexOffset=0,this.attributeOffset=0,this.roundPixels=0,this._batcher=null,this._batch=null,this._textureMatrixUpdateId=-1,this._uvUpdateId=-1}get blendMode(){return this.renderable.groupBlendMode}get topology(){return this._topology||this.geometry.topology}set topology(t){this._topology=t}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.geometry=null,this._uvUpdateId=-1,this._textureMatrixUpdateId=-1}setTexture(t){this.texture!==t&&(this.texture=t,this._textureMatrixUpdateId=-1)}get uvs(){const e=this.geometry.getBuffer("aUV"),r=e.data;let n=r;const i=this.texture.textureMatrix;return i.isSimple||(n=this._transformedUvs,(this._textureMatrixUpdateId!==i._updateID||this._uvUpdateId!==e._updateID)&&((!n||n.length<r.length)&&(n=this._transformedUvs=new Float32Array(r.length)),this._textureMatrixUpdateId=i._updateID,this._uvUpdateId=e._updateID,i.multiplyUvs(r,n))),n}get positions(){return this.geometry.positions}get indices(){return this.geometry.indices}get color(){return this.renderable.groupColorAlpha}get groupTransform(){return this.renderable.groupTransform}get attributeSize(){return this.geometry.positions.length/2}get indexSize(){return this.geometry.indices.length}}class gr{destroy(){}}class Zr{constructor(t,e){this.localUniforms=new xt({uTransformMatrix:{value:new L,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),this.localUniformsBindGroup=new br({0:this.localUniforms}),this.renderer=t,this._adaptor=e,this._adaptor.init()}validateRenderable(t){const e=this._getMeshData(t),r=e.batched,n=t.batched;if(e.batched=n,r!==n)return!0;if(n){const i=t._geometry;if(i.indices.length!==e.indexSize||i.positions.length!==e.vertexSize)return e.indexSize=i.indices.length,e.vertexSize=i.positions.length,!0;const o=this._getBatchableMesh(t);return o.texture.uid!==t._texture.uid&&(o._textureMatrixUpdateId=-1),!o._batcher.checkAndUpdateTexture(o,t._texture)}return!1}addRenderable(t,e){var i,o;const r=this.renderer.renderPipes.batch,n=this._getMeshData(t);if(t.didViewUpdate&&(n.indexSize=(i=t._geometry.indices)==null?void 0:i.length,n.vertexSize=(o=t._geometry.positions)==null?void 0:o.length),n.batched){const a=this._getBatchableMesh(t);a.setTexture(t._texture),a.geometry=t._geometry,r.addToBatch(a,e)}else r.break(e),e.add(t)}updateRenderable(t){if(t.batched){const e=this._getBatchableMesh(t);e.setTexture(t._texture),e.geometry=t._geometry,e._batcher.updateElement(e)}}execute(t){if(!t.isRenderable)return;t.state.blendMode=Te(t.groupBlendMode,t.texture._source);const e=this.localUniforms;e.uniforms.uTransformMatrix=t.groupTransform,e.uniforms.uRound=this.renderer._roundPixels|t._roundPixels,e.update(),Zt(t.groupColorAlpha,e.uniforms.uColor,0),this._adaptor.execute(this,t)}_getMeshData(t){var e,r;return(e=t._gpuData)[r=this.renderer.uid]||(e[r]=new gr),t._gpuData[this.renderer.uid].meshData||this._initMeshData(t)}_initMeshData(t){return t._gpuData[this.renderer.uid].meshData={batched:t.batched,indexSize:0,vertexSize:0},t._gpuData[this.renderer.uid].meshData}_getBatchableMesh(t){var e,r;return(e=t._gpuData)[r=this.renderer.uid]||(e[r]=new gr),t._gpuData[this.renderer.uid].batchableMesh||this._initBatchableMesh(t)}_initBatchableMesh(t){const e=new Ae;return e.renderable=t,e.setTexture(t._texture),e.transform=t.groupTransform,e.roundPixels=this.renderer._roundPixels|t._roundPixels,t._gpuData[this.renderer.uid].batchableMesh=e,e}destroy(){this.localUniforms=null,this.localUniformsBindGroup=null,this._adaptor.destroy(),this._adaptor=null,this.renderer=null}}Zr.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"mesh"};class Ps{execute(t,e){const r=t.state,n=t.renderer,i=e.shader||t.defaultShader;i.resources.uTexture=e.texture._source,i.resources.uniforms=t.localUniforms;const o=n.gl,a=t.getBuffers(e);n.shader.bind(i),n.state.set(r),n.geometry.bind(a.geometry,i.glProgram);const c=a.geometry.indexBuffer.data.BYTES_PER_ELEMENT===2?o.UNSIGNED_SHORT:o.UNSIGNED_INT;o.drawElements(o.TRIANGLES,e.particleChildren.length*6,c,0)}}class vs{execute(t,e){const r=t.renderer,n=e.shader||t.defaultShader;n.groups[0]=r.renderPipes.uniformBatch.getUniformBindGroup(t.localUniforms,!0),n.groups[1]=r.texture.getTextureBindGroup(e.texture);const i=t.state,o=t.getBuffers(e);r.encoder.draw({geometry:o.geometry,shader:e.shader||t.defaultShader,state:i,size:e.particleChildren.length*6})}}function xr(s,t=null){const e=s*6;if(e>65535?t||(t=new Uint32Array(e)):t||(t=new Uint16Array(e)),t.length!==e)throw new Error(`Out buffer length is incorrect, got ${t.length} and expected ${e}`);for(let r=0,n=0;r<e;r+=6,n+=4)t[r+0]=n+0,t[r+1]=n+1,t[r+2]=n+2,t[r+3]=n+0,t[r+4]=n+2,t[r+5]=n+3;return t}function Cs(s){return{dynamicUpdate:mr(s,!0),staticUpdate:mr(s,!1)}}function mr(s,t){const e=[];e.push(`

        var index = 0;

        for (let i = 0; i < ps.length; ++i)
        {
            const p = ps[i];

            `);let r=0;for(const i in s){const o=s[i];if(t!==o.dynamic)continue;e.push(`offset = index + ${r}`),e.push(o.code);const a=de(o.format);r+=a.stride/4}e.push(`
            index += stride * 4;
        }
    `),e.unshift(`
        var stride = ${r};
    `);const n=e.join(`
`);return new Function("ps","f32v","u32v",n)}class Ms{constructor(t){this._size=0,this._generateParticleUpdateCache={};const e=this._size=t.size??1e3,r=t.properties;let n=0,i=0;for(const h in r){const u=r[h],f=de(u.format);u.dynamic?i+=f.stride:n+=f.stride}this._dynamicStride=i/4,this._staticStride=n/4,this.staticAttributeBuffer=new Dt(e*4*n),this.dynamicAttributeBuffer=new Dt(e*4*i),this.indexBuffer=xr(e);const o=new Se;let a=0,l=0;this._staticBuffer=new Ct({data:new Float32Array(1),label:"static-particle-buffer",shrinkToFit:!1,usage:X.VERTEX|X.COPY_DST}),this._dynamicBuffer=new Ct({data:new Float32Array(1),label:"dynamic-particle-buffer",shrinkToFit:!1,usage:X.VERTEX|X.COPY_DST});for(const h in r){const u=r[h],f=de(u.format);u.dynamic?(o.addAttribute(u.attributeName,{buffer:this._dynamicBuffer,stride:this._dynamicStride*4,offset:a*4,format:u.format}),a+=f.size):(o.addAttribute(u.attributeName,{buffer:this._staticBuffer,stride:this._staticStride*4,offset:l*4,format:u.format}),l+=f.size)}o.addIndex(this.indexBuffer);const c=this.getParticleUpdate(r);this._dynamicUpload=c.dynamicUpdate,this._staticUpload=c.staticUpdate,this.geometry=o}getParticleUpdate(t){const e=ks(t);return this._generateParticleUpdateCache[e]?this._generateParticleUpdateCache[e]:(this._generateParticleUpdateCache[e]=this.generateParticleUpdate(t),this._generateParticleUpdateCache[e])}generateParticleUpdate(t){return Cs(t)}update(t,e){t.length>this._size&&(e=!0,this._size=Math.max(t.length,this._size*1.5|0),this.staticAttributeBuffer=new Dt(this._size*this._staticStride*4*4),this.dynamicAttributeBuffer=new Dt(this._size*this._dynamicStride*4*4),this.indexBuffer=xr(this._size),this.geometry.indexBuffer.setDataWithSize(this.indexBuffer,this.indexBuffer.byteLength,!0));const r=this.dynamicAttributeBuffer;if(this._dynamicUpload(t,r.float32View,r.uint32View),this._dynamicBuffer.setDataWithSize(this.dynamicAttributeBuffer.float32View,t.length*this._dynamicStride*4,!0),e){const n=this.staticAttributeBuffer;this._staticUpload(t,n.float32View,n.uint32View),this._staticBuffer.setDataWithSize(n.float32View,t.length*this._staticStride*4,!0)}}destroy(){this._staticBuffer.destroy(),this._dynamicBuffer.destroy(),this.geometry.destroy()}}function ks(s){const t=[];for(const e in s){const r=s[e];t.push(e,r.code,r.dynamic?"d":"s")}return t.join("_")}var Fs=`varying vec2 vUV;
varying vec4 vColor;

uniform sampler2D uTexture;

void main(void){
    vec4 color = texture2D(uTexture, vUV) * vColor;
    gl_FragColor = color;
}`,Bs=`attribute vec2 aVertex;
attribute vec2 aUV;
attribute vec4 aColor;

attribute vec2 aPosition;
attribute float aRotation;

uniform mat3 uTranslationMatrix;
uniform float uRound;
uniform vec2 uResolution;
uniform vec4 uColor;

varying vec2 vUV;
varying vec4 vColor;

vec2 roundPixels(vec2 position, vec2 targetSize)
{       
    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
}

void main(void){
    float cosRotation = cos(aRotation);
    float sinRotation = sin(aRotation);
    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;
    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;

    vec2 v = vec2(x, y);
    v = v + aPosition;

    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    if(uRound == 1.0)
    {
        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
    }

    vUV = aUV;
    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;
}
`,yr=`
struct ParticleUniforms {
  uProjectionMatrix:mat3x3<f32>,
  uColor:vec4<f32>,
  uResolution:vec2<f32>,
  uRoundPixels:f32,
};

@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;

@group(1) @binding(0) var uTexture: texture_2d<f32>;
@group(1) @binding(1) var uSampler : sampler;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>,
    @location(1) color : vec4<f32>,
  };
@vertex
fn mainVertex(
  @location(0) aVertex: vec2<f32>,
  @location(1) aPosition: vec2<f32>,
  @location(2) aUV: vec2<f32>,
  @location(3) aColor: vec4<f32>,
  @location(4) aRotation: f32,
) -> VSOutput {
  
   let v = vec2(
       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),
       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)
   ) + aPosition;

   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;

  return VSOutput(
   position,
   aUV,
   vColor,
  );
}

@fragment
fn mainFragment(
  @location(0) uv: vec2<f32>,
  @location(1) color: vec4<f32>,
  @builtin(position) position: vec4<f32>,
) -> @location(0) vec4<f32> {

    var sample = textureSample(uTexture, uSampler, uv) * color;
   
    return sample;
}`;class Rs extends Pe{constructor(){const t=Pn.from({vertex:Bs,fragment:Fs}),e=vn.from({fragment:{source:yr,entryPoint:"mainFragment"},vertex:{source:yr,entryPoint:"mainVertex"}});super({glProgram:t,gpuProgram:e,resources:{uTexture:H.WHITE.source,uSampler:new Ft({}),uniforms:{uTranslationMatrix:{value:new L,type:"mat3x3<f32>"},uColor:{value:new E(16777215),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}}})}}class Qr{constructor(t,e){this.state=Yt.for2d(),this.localUniforms=new xt({uTranslationMatrix:{value:new L,type:"mat3x3<f32>"},uColor:{value:new Float32Array(4),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}),this.renderer=t,this.adaptor=e,this.defaultShader=new Rs,this.state=Yt.for2d()}validateRenderable(t){return!1}addRenderable(t,e){this.renderer.renderPipes.batch.break(e),e.add(t)}getBuffers(t){return t._gpuData[this.renderer.uid]||this._initBuffer(t)}_initBuffer(t){return t._gpuData[this.renderer.uid]=new Ms({size:t.particleChildren.length,properties:t._properties}),t._gpuData[this.renderer.uid]}updateRenderable(t){}execute(t){const e=t.particleChildren;if(e.length===0)return;const r=this.renderer,n=this.getBuffers(t);t.texture||(t.texture=e[0].texture);const i=this.state;n.update(e,t._childrenDirty),t._childrenDirty=!1,i.blendMode=Te(t.blendMode,t.texture._source);const o=this.localUniforms.uniforms,a=o.uTranslationMatrix;t.worldTransform.copyTo(a),a.prepend(r.globalUniforms.globalUniformData.projectionMatrix),o.uResolution=r.globalUniforms.globalUniformData.resolution,o.uRound=r._roundPixels|t._roundPixels,Zt(t.groupColorAlpha,o.uColor,0),this.adaptor.execute(this,t)}destroy(){this.defaultShader&&(this.defaultShader.destroy(),this.defaultShader=null)}}class Jr extends Qr{constructor(t){super(t,new Ps)}}Jr.extension={type:[R.WebGLPipes],name:"particle"};class tn extends Qr{constructor(t){super(t,new vs)}}tn.extension={type:[R.WebGPUPipes],name:"particle"};const en=class rn extends Ts{constructor(t={}){t={...rn.defaultOptions,...t},super({width:t.width,height:t.height,verticesX:4,verticesY:4}),this.update(t)}update(t){var e,r;this.width=t.width??this.width,this.height=t.height??this.height,this._originalWidth=t.originalWidth??this._originalWidth,this._originalHeight=t.originalHeight??this._originalHeight,this._leftWidth=t.leftWidth??this._leftWidth,this._rightWidth=t.rightWidth??this._rightWidth,this._topHeight=t.topHeight??this._topHeight,this._bottomHeight=t.bottomHeight??this._bottomHeight,this._anchorX=(e=t.anchor)==null?void 0:e.x,this._anchorY=(r=t.anchor)==null?void 0:r.y,this.updateUvs(),this.updatePositions()}updatePositions(){const t=this.positions,{width:e,height:r,_leftWidth:n,_rightWidth:i,_topHeight:o,_bottomHeight:a,_anchorX:l,_anchorY:c}=this,h=n+i,u=e>h?1:e/h,f=o+a,d=r>f?1:r/f,p=Math.min(u,d),g=l*e,m=c*r;t[0]=t[8]=t[16]=t[24]=-g,t[2]=t[10]=t[18]=t[26]=n*p-g,t[4]=t[12]=t[20]=t[28]=e-i*p-g,t[6]=t[14]=t[22]=t[30]=e-g,t[1]=t[3]=t[5]=t[7]=-m,t[9]=t[11]=t[13]=t[15]=o*p-m,t[17]=t[19]=t[21]=t[23]=r-a*p-m,t[25]=t[27]=t[29]=t[31]=r-m,this.getBuffer("aPosition").update()}updateUvs(){const t=this.uvs;t[0]=t[8]=t[16]=t[24]=0,t[1]=t[3]=t[5]=t[7]=0,t[6]=t[14]=t[22]=t[30]=1,t[25]=t[27]=t[29]=t[31]=1;const e=1/this._originalWidth,r=1/this._originalHeight;t[2]=t[10]=t[18]=t[26]=e*this._leftWidth,t[9]=t[11]=t[13]=t[15]=r*this._topHeight,t[4]=t[12]=t[20]=t[28]=1-e*this._rightWidth,t[17]=t[19]=t[21]=t[23]=1-r*this._bottomHeight,this.getBuffer("aUV").update()}};en.defaultOptions={width:100,height:100,leftWidth:10,topHeight:10,rightWidth:10,bottomHeight:10,originalWidth:100,originalHeight:100};let Gs=en;class Us extends Ae{constructor(){super(),this.geometry=new Gs}destroy(){this.geometry.destroy()}}class nn{constructor(t){this._renderer=t}addRenderable(t,e){const r=this._getGpuSprite(t);t.didViewUpdate&&this._updateBatchableSprite(t,r),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuSprite(t);t.didViewUpdate&&this._updateBatchableSprite(t,e),e._batcher.updateElement(e)}validateRenderable(t){const e=this._getGpuSprite(t);return!e._batcher.checkAndUpdateTexture(e,t._texture)}_updateBatchableSprite(t,e){e.geometry.update(t),e.setTexture(t._texture)}_getGpuSprite(t){return t._gpuData[this._renderer.uid]||this._initGPUSprite(t)}_initGPUSprite(t){const e=t._gpuData[this._renderer.uid]=new Us,r=e;return r.renderable=t,r.transform=t.groupTransform,r.texture=t._texture,r.roundPixels=this._renderer._roundPixels|t._roundPixels,t.didViewUpdate||this._updateBatchableSprite(t,r),e}destroy(){this._renderer=null}}nn.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"nineSliceSprite"};const zs={name:"tiling-bit",vertex:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`
            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;

            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;
        `},fragment:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`

            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);
            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;
            var unclamped = coord;
            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);

            var bias = 0.;

            if(unclamped.x == coord.x && unclamped.y == coord.y)
            {
                bias = -32.;
            }

            outColor = textureSampleBias(uTexture, uSampler, coord, bias);
        `}},As={name:"tiling-bit",vertex:{header:`
            uniform mat3 uTextureTransform;
            uniform vec4 uSizeAnchor;

        `,main:`
            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;

            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;
        `},fragment:{header:`
            uniform sampler2D uTexture;
            uniform mat3 uMapCoord;
            uniform vec4 uClampFrame;
            uniform vec2 uClampOffset;
        `,main:`

        vec2 coord = vUV + ceil(uClampOffset - vUV);
        coord = (uMapCoord * vec3(coord, 1.0)).xy;
        vec2 unclamped = coord;
        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0

        `}};let oe,ae;class Ws extends Pe{constructor(){oe??(oe=Sr({name:"tiling-sprite-shader",bits:[Cn,zs,wr]})),ae??(ae=Tr({name:"tiling-sprite-shader",bits:[Mn,As,Pr]}));const t=new xt({uMapCoord:{value:new L,type:"mat3x3<f32>"},uClampFrame:{value:new Float32Array([0,0,1,1]),type:"vec4<f32>"},uClampOffset:{value:new Float32Array([0,0]),type:"vec2<f32>"},uTextureTransform:{value:new L,type:"mat3x3<f32>"},uSizeAnchor:{value:new Float32Array([100,100,.5,.5]),type:"vec4<f32>"}});super({glProgram:ae,gpuProgram:oe,resources:{localUniforms:new xt({uTransformMatrix:{value:new L,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),tilingUniforms:t,uTexture:H.EMPTY.source,uSampler:H.EMPTY.source.style}})}updateUniforms(t,e,r,n,i,o){const a=this.resources.tilingUniforms,l=o.width,c=o.height,h=o.textureMatrix,u=a.uniforms.uTextureTransform;u.set(r.a*l/t,r.b*l/e,r.c*c/t,r.d*c/e,r.tx/t,r.ty/e),u.invert(),a.uniforms.uMapCoord=h.mapCoord,a.uniforms.uClampFrame=h.uClampFrame,a.uniforms.uClampOffset=h.uClampOffset,a.uniforms.uTextureTransform=u,a.uniforms.uSizeAnchor[0]=t,a.uniforms.uSizeAnchor[1]=e,a.uniforms.uSizeAnchor[2]=n,a.uniforms.uSizeAnchor[3]=i,o&&(this.resources.uTexture=o.source,this.resources.uSampler=o.source.style)}}class Ds extends ze{constructor(){super({positions:new Float32Array([0,0,1,0,1,1,0,1]),uvs:new Float32Array([0,0,1,0,1,1,0,1]),indices:new Uint32Array([0,1,2,0,2,3])})}}function Is(s,t){const e=s.anchor.x,r=s.anchor.y;t[0]=-e*s.width,t[1]=-r*s.height,t[2]=(1-e)*s.width,t[3]=-r*s.height,t[4]=(1-e)*s.width,t[5]=(1-r)*s.height,t[6]=-e*s.width,t[7]=(1-r)*s.height}function Ls(s,t,e,r){let n=0;const i=s.length/t,o=r.a,a=r.b,l=r.c,c=r.d,h=r.tx,u=r.ty;for(e*=t;n<i;){const f=s[e],d=s[e+1];s[e]=o*f+l*d+h,s[e+1]=a*f+c*d+u,e+=t,n++}}function Hs(s,t){const e=s.texture,r=e.frame.width,n=e.frame.height;let i=0,o=0;s.applyAnchorToTexture&&(i=s.anchor.x,o=s.anchor.y),t[0]=t[6]=-i,t[2]=t[4]=1-i,t[1]=t[3]=-o,t[5]=t[7]=1-o;const a=L.shared;a.copyFrom(s._tileTransform.matrix),a.tx/=s.width,a.ty/=s.height,a.invert(),a.scale(s.width/r,s.height/n),Ls(t,2,0,a)}const Ot=new Ds;class Es{constructor(){this.canBatch=!0,this.geometry=new ze({indices:Ot.indices.slice(),positions:Ot.positions.slice(),uvs:Ot.uvs.slice()})}destroy(){var t;this.geometry.destroy(),(t=this.shader)==null||t.destroy()}}class sn{constructor(t){this._state=Yt.default2d,this._renderer=t}validateRenderable(t){const e=this._getTilingSpriteData(t),r=e.canBatch;this._updateCanBatch(t);const n=e.canBatch;if(n&&n===r){const{batchableMesh:i}=e;return!i._batcher.checkAndUpdateTexture(i,t.texture)}return r!==n}addRenderable(t,e){const r=this._renderer.renderPipes.batch;this._updateCanBatch(t);const n=this._getTilingSpriteData(t),{geometry:i,canBatch:o}=n;if(o){n.batchableMesh||(n.batchableMesh=new Ae);const a=n.batchableMesh;t.didViewUpdate&&(this._updateBatchableMesh(t),a.geometry=i,a.renderable=t,a.transform=t.groupTransform,a.setTexture(t._texture)),a.roundPixels=this._renderer._roundPixels|t._roundPixels,r.addToBatch(a,e)}else r.break(e),n.shader||(n.shader=new Ws),this.updateRenderable(t),e.add(t)}execute(t){const{shader:e}=this._getTilingSpriteData(t);e.groups[0]=this._renderer.globalUniforms.bindGroup;const r=e.resources.localUniforms.uniforms;r.uTransformMatrix=t.groupTransform,r.uRound=this._renderer._roundPixels|t._roundPixels,Zt(t.groupColorAlpha,r.uColor,0),this._state.blendMode=Te(t.groupBlendMode,t.texture._source),this._renderer.encoder.draw({geometry:Ot,shader:e,state:this._state})}updateRenderable(t){const e=this._getTilingSpriteData(t),{canBatch:r}=e;if(r){const{batchableMesh:n}=e;t.didViewUpdate&&this._updateBatchableMesh(t),n._batcher.updateElement(n)}else if(t.didViewUpdate){const{shader:n}=e;n.updateUniforms(t.width,t.height,t._tileTransform.matrix,t.anchor.x,t.anchor.y,t.texture)}}_getTilingSpriteData(t){return t._gpuData[this._renderer.uid]||this._initTilingSpriteData(t)}_initTilingSpriteData(t){const e=new Es;return e.renderable=t,t._gpuData[this._renderer.uid]=e,e}_updateBatchableMesh(t){const e=this._getTilingSpriteData(t),{geometry:r}=e,n=t.texture.source.style;n.addressMode!=="repeat"&&(n.addressMode="repeat",n.update()),Hs(t,r.uvs),Is(t,r.positions)}destroy(){this._renderer=null}_updateCanBatch(t){const e=this._getTilingSpriteData(t),r=t.texture;let n=!0;return this._renderer.type===we.WEBGL&&(n=this._renderer.context.supports.nonPowOf2wrapping),e.canBatch=r.textureMatrix.isSimple&&(n||r.source.isPowerOfTwo),e.canBatch}}sn.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"tilingSprite"};const Vs={name:"local-uniform-msdf-bit",vertex:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32,
                uRound:f32,
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
        `,main:`
            vColor *= localUniforms.uColor;
            modelMatrix *= localUniforms.uTransformMatrix;
        `,end:`
            if(localUniforms.uRound == 1)
            {
                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
            }
        `},fragment:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
         `,main:`
            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));
        `}},Os={name:"local-uniform-msdf-bit",vertex:{header:`
            uniform mat3 uTransformMatrix;
            uniform vec4 uColor;
            uniform float uRound;
        `,main:`
            vColor *= uColor;
            modelMatrix *= uTransformMatrix;
        `,end:`
            if(uRound == 1.)
            {
                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
            }
        `},fragment:{header:`
            uniform float uDistance;
         `,main:`
            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));
        `}},Ys={name:"msdf-bit",fragment:{header:`
            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {

                // MSDF
                var median = msdfColor.r + msdfColor.g + msdfColor.b -
                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                var screenPxDistance = distance * (median - 0.5);
                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));
                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);
                var coverage: f32 = pow(shapeColor.a * alpha, gamma);

                return coverage;

            }
        `}},js={name:"msdf-bit",fragment:{header:`
            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {

                // MSDF
                float median = msdfColor.r + msdfColor.g + msdfColor.b -
                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                float screenPxDistance = distance * (median - 0.5);
                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);

                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));
                float gamma = mix(1.0, 1.0 / 2.2, luma);
                float coverage = pow(shapeColor.a * alpha, gamma);

                return coverage;
            }
        `}};let le,he;class $s extends Pe{constructor(t){const e=new xt({uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uTransformMatrix:{value:new L,type:"mat3x3<f32>"},uDistance:{value:4,type:"f32"},uRound:{value:0,type:"f32"}});le??(le=Sr({name:"sdf-shader",bits:[kn,Fn(t),Vs,Ys,wr]})),he??(he=Tr({name:"sdf-shader",bits:[Bn,Rn(t),Os,js,Pr]})),super({glProgram:he,gpuProgram:le,resources:{localUniforms:e,batchSamplers:Gn(t)}})}}class Ns extends qt{destroy(){this.context.customShader&&this.context.customShader.destroy(),super.destroy()}}class on{constructor(t){this._renderer=t,this._renderer.renderableGC.addManagedHash(this,"_gpuBitmapText")}validateRenderable(t){const e=this._getGpuBitmapText(t);return this._renderer.renderPipes.graphics.validateRenderable(e)}addRenderable(t,e){const r=this._getGpuBitmapText(t);_r(t,r),t._didTextUpdate&&(t._didTextUpdate=!1,this._updateContext(t,r)),this._renderer.renderPipes.graphics.addRenderable(r,e),r.context.customShader&&this._updateDistanceField(t)}updateRenderable(t){const e=this._getGpuBitmapText(t);_r(t,e),this._renderer.renderPipes.graphics.updateRenderable(e),e.context.customShader&&this._updateDistanceField(t)}_updateContext(t,e){const{context:r}=e,n=ps.getFont(t.text,t._style);r.clear(),n.distanceField.type!=="none"&&(r.customShader||(r.customShader=new $s(this._renderer.limits.maxBatchableTextures)));const i=K.graphemeSegmenter(t.text),o=t._style;let a=n.baseLineOffset;const l=Vr(i,o,n,!0),c=o.padding,h=l.scale;let u=l.width,f=l.height+l.offsetY;o._stroke&&(u+=o._stroke.width/h,f+=o._stroke.width/h),r.translate(-t._anchor._x*u-c,-t._anchor._y*f-c).scale(h,h);const d=n.applyFillAsTint?o._fill.color:16777215;let p=n.fontMetrics.fontSize,g=n.lineHeight;o.lineHeight&&(p=o.fontSize/h,g=o.lineHeight/h);let m=(g-p)/2;m-n.baseLineOffset<0&&(m=0);for(let x=0;x<l.lines.length;x++){const y=l.lines[x];for(let b=0;b<y.charPositions.length;b++){const _=y.chars[b],T=n.chars[_];if(T!=null&&T.texture){const w=T.texture;r.texture(w,d||"black",Math.round(y.charPositions[b]+T.xOffset),Math.round(a+T.yOffset+m),w.orig.width,w.orig.height)}}a+=g}}_getGpuBitmapText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new Ns;return t._gpuData[this._renderer.uid]=e,this._updateContext(t,e),e}_updateDistanceField(t){const e=this._getGpuBitmapText(t).context,r=t._style.fontFamily,n=N.get(`${r}-bitmap`),{a:i,b:o,c:a,d:l}=t.groupTransform,c=Math.sqrt(i*i+o*o),h=Math.sqrt(a*a+l*l),u=(Math.abs(c)+Math.abs(h))/2,f=n.baseRenderedFontSize/t._style.fontSize,d=u*n.distanceField.range*(1/f);e.customShader.resources.localUniforms.uniforms.uDistance=d}destroy(){this._renderer=null}}on.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"bitmapText"};function _r(s,t){t.groupTransform=s.groupTransform,t.groupColorAlpha=s.groupColorAlpha,t.groupColor=s.groupColor,t.groupBlendMode=s.groupBlendMode,t.globalDisplayStatus=s.globalDisplayStatus,t.groupTransform=s.groupTransform,t.localDisplayStatus=s.localDisplayStatus,t.groupAlpha=s.groupAlpha,t._roundPixels=s._roundPixels}class qs extends vr{constructor(t){super(),this.generatingTexture=!1,this._renderer=t,t.runners.resolutionChange.add(this)}resolutionChange(){const t=this.renderable;t._autoResolution&&t.onViewUpdate()}destroy(){this._renderer.htmlText.returnTexturePromise(this.texturePromise),this.texturePromise=null,this._renderer=null}}function _e(s,t){const{texture:e,bounds:r}=s,n=t._style._getFinalPadding();Sn(r,t._anchor,e);const i=t._anchor._x*n*2,o=t._anchor._y*n*2;r.minX-=n-i,r.minY-=n-o,r.maxX-=n-i,r.maxY-=n-o}class an{constructor(t){this._renderer=t}validateRenderable(t){return t._didTextUpdate}addRenderable(t,e){const r=this._getGpuText(t);t._didTextUpdate&&(this._updateGpuText(t).catch(n=>{console.error(n)}),t._didTextUpdate=!1,_e(r,t)),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuText(t);e._batcher.updateElement(e)}async _updateGpuText(t){t._didTextUpdate=!1;const e=this._getGpuText(t);if(e.generatingTexture)return;e.texturePromise&&(this._renderer.htmlText.returnTexturePromise(e.texturePromise),e.texturePromise=null),e.generatingTexture=!0,t._resolution=t._autoResolution?this._renderer.resolution:t.resolution;const r=this._renderer.htmlText.getTexturePromise(t);e.texturePromise=r,e.texture=await r;const n=t.renderGroup||t.parentRenderGroup;n&&(n.structureDidChange=!0),e.generatingTexture=!1,_e(e,t)}_getGpuText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new qs(this._renderer);return e.renderable=t,e.transform=t.groupTransform,e.texture=H.EMPTY,e.bounds={minX:0,maxX:1,minY:0,maxY:0},e.roundPixels=this._renderer._roundPixels|t._roundPixels,t._resolution=t._autoResolution?this._renderer.resolution:t.resolution,t._gpuData[this._renderer.uid]=e,e}destroy(){this._renderer=null}}an.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"htmlText"};function Xs(){const{userAgent:s}=nt.get().getNavigator();return/^((?!chrome|android).)*safari/i.test(s)}const Ks=new Kt;function ln(s,t,e,r){const n=Ks;n.minX=0,n.minY=0,n.maxX=s.width/r|0,n.maxY=s.height/r|0;const i=q.getOptimalTexture(n.width,n.height,r,!1);return i.source.uploadMethodId="image",i.source.resource=s,i.source.alphaMode="premultiply-alpha-on-upload",i.frame.width=t/r,i.frame.height=e/r,i.source.emit("update",i.source),i.updateUvs(),i}function Zs(s,t){const e=t.fontFamily,r=[],n={},i=/font-family:([^;"\s]+)/g,o=s.match(i);function a(l){n[l]||(r.push(l),n[l]=!0)}if(Array.isArray(e))for(let l=0;l<e.length;l++)a(e[l]);else a(e);o&&o.forEach(l=>{const c=l.split(":")[1].trim();a(c)});for(const l in t.tagStyles){const c=t.tagStyles[l].fontFamily;a(c)}return r}async function Qs(s){const e=await(await nt.get().fetch(s)).blob(),r=new FileReader;return await new Promise((i,o)=>{r.onloadend=()=>i(r.result),r.onerror=o,r.readAsDataURL(e)})}async function Js(s,t){const e=await Qs(t);return`@font-face {
        font-family: "${s.fontFamily}";
        font-weight: ${s.fontWeight};
        font-style: ${s.fontStyle};
        src: url('${e}');
    }`}const ce=new Map;async function to(s){const t=s.filter(e=>N.has(`${e}-and-url`)).map(e=>{if(!ce.has(e)){const{entries:r}=N.get(`${e}-and-url`),n=[];r.forEach(i=>{const o=i.url,l=i.faces.map(c=>({weight:c.weight,style:c.style}));n.push(...l.map(c=>Js({fontWeight:c.weight,fontStyle:c.style,fontFamily:e},o)))}),ce.set(e,Promise.all(n).then(i=>i.join(`
`)))}return ce.get(e)});return(await Promise.all(t)).join(`
`)}function eo(s,t,e,r,n){const{domElement:i,styleElement:o,svgRoot:a}=n;i.innerHTML=`<style>${t.cssStyle}</style><div style='padding:0;'>${s}</div>`,i.setAttribute("style",`transform: scale(${e});transform-origin: top left; display: inline-block`),o.textContent=r;const{width:l,height:c}=n.image;return a.setAttribute("width",l.toString()),a.setAttribute("height",c.toString()),new XMLSerializer().serializeToString(a)}function ro(s,t){const e=gt.getOptimalCanvasAndContext(s.width,s.height,t),{context:r}=e;return r.clearRect(0,0,s.width,s.height),r.drawImage(s,0,0),e}function no(s,t,e){return new Promise(async r=>{e&&await new Promise(n=>setTimeout(n,100)),s.onload=()=>{r()},s.src=`data:image/svg+xml;charset=utf8,${encodeURIComponent(t)}`,s.crossOrigin="anonymous"})}class hn{constructor(t){this._renderer=t,this._createCanvas=t.type===we.WEBGPU}getTexture(t){return this.getTexturePromise(t)}getTexturePromise(t){return this._buildTexturePromise(t)}async _buildTexturePromise(t){const{text:e,style:r,resolution:n,textureStyle:i}=t,o=tt.get(Nr),a=Zs(e,r),l=await to(a),c=Ss(e,r,l,o),h=Math.ceil(Math.ceil(Math.max(1,c.width)+r.padding*2)*n),u=Math.ceil(Math.ceil(Math.max(1,c.height)+r.padding*2)*n),f=o.image,d=2;f.width=(h|0)+d,f.height=(u|0)+d;const p=eo(e,r,n,l,o);await no(f,p,Xs()&&a.length>0);const g=f;let m;this._createCanvas&&(m=ro(f,n));const x=ln(m?m.canvas:g,f.width-d,f.height-d,n);return i&&(x.source.style=i),this._createCanvas&&(this._renderer.texture.initSource(x.source),gt.returnCanvasAndContext(m)),tt.return(o),x}returnTexturePromise(t){t.then(e=>{this._cleanUp(e)}).catch(()=>{Z("HTMLTextSystem: Failed to clean texture")})}_cleanUp(t){q.returnTexture(t,!0),t.source.resource=null,t.source.uploadMethodId="unknown"}destroy(){this._renderer=null}}hn.extension={type:[R.WebGLSystem,R.WebGPUSystem,R.CanvasSystem],name:"htmlText"};class io extends vr{constructor(t){super(),this._renderer=t,t.runners.resolutionChange.add(this)}resolutionChange(){const t=this.renderable;t._autoResolution&&t.onViewUpdate()}destroy(){this._renderer.canvasText.returnTexture(this.texture),this._renderer=null}}class cn{constructor(t){this._renderer=t}validateRenderable(t){return t._didTextUpdate}addRenderable(t,e){const r=this._getGpuText(t);t._didTextUpdate&&(this._updateGpuText(t),t._didTextUpdate=!1),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuText(t);e._batcher.updateElement(e)}_updateGpuText(t){const e=this._getGpuText(t);e.texture&&this._renderer.canvasText.returnTexture(e.texture),t._resolution=t._autoResolution?this._renderer.resolution:t.resolution,e.texture=this._renderer.canvasText.getTexture(t),_e(e,t)}_getGpuText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new io(this._renderer);return e.renderable=t,e.transform=t.groupTransform,e.bounds={minX:0,maxX:1,minY:0,maxY:0},e.roundPixels=this._renderer._roundPixels|t._roundPixels,t._gpuData[this._renderer.uid]=e,e}destroy(){this._renderer=null}}cn.extension={type:[R.WebGLPipes,R.WebGPUPipes,R.CanvasPipes],name:"text"};class un{constructor(t){this._renderer=t}getTexture(t,e,r,n){typeof t=="string"&&(z("8.0.0","CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments"),t={text:t,style:r,resolution:e}),t.style instanceof _t||(t.style=new _t(t.style)),t.textureStyle instanceof Ft||(t.textureStyle=new Ft(t.textureStyle)),typeof t.text!="string"&&(t.text=t.text.toString());const{text:i,style:o,textureStyle:a}=t,l=t.resolution??this._renderer.resolution,{frame:c,canvasAndContext:h}=se.getCanvasAndContext({text:i,style:o,resolution:l}),u=ln(h.canvas,c.width,c.height,l);if(a&&(u.source.style=a),o.trim&&(c.pad(o.padding),u.frame.copyFrom(c),u.frame.scale(1/l),u.updateUvs()),o.filters){const f=this._applyFilters(u,o.filters);return this.returnTexture(u),se.returnCanvasAndContext(h),f}return this._renderer.texture.initSource(u._source),se.returnCanvasAndContext(h),u}returnTexture(t){const e=t.source;e.resource=null,e.uploadMethodId="unknown",e.alphaMode="no-premultiply-alpha",q.returnTexture(t,!0)}renderTextToCanvas(){z("8.10.0","CanvasTextSystem.renderTextToCanvas: no longer supported, use CanvasTextSystem.getTexture instead")}_applyFilters(t,e){const r=this._renderer.renderTarget.renderTarget,n=this._renderer.filter.generateFilteredTexture({texture:t,filters:e});return this._renderer.renderTarget.bind(r,!1),n}destroy(){this._renderer=null}}un.extension={type:[R.WebGLSystem,R.WebGPUSystem,R.CanvasSystem],name:"canvasText"};V.add(Br);V.add(Rr);V.add(qr);V.add(Re);V.add(Zr);V.add(Jr);V.add(tn);V.add(un);V.add(cn);V.add(on);V.add(hn);V.add(an);V.add(sn);V.add(nn);V.add(Yr);V.add(Or);
