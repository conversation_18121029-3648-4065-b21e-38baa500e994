package com.slhc.hcms.module.infra.es.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.slhc.hcms.module.infra.es.service.IElasticsearchService;

import org.jeecg.common.es.JeecgElasticsearchTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ElasticsearchServiceImpl implements IElasticsearchService {

    @Resource
    private JeecgElasticsearchTemplate esTemplate;

    @Override
    public boolean createIndex(String indexName) {
        return esTemplate.createIndex(indexName, null);
    }

    @Override
    public boolean createIndex(String indexName, JSONObject mapping) {
        if (esTemplate.indexExists(indexName)) {
            return true;
        }
        return esTemplate.createIndex(indexName,mapping);
    }


    @Override
    public boolean deleteIndex(String indexName) {
        return esTemplate.removeIndex(indexName);
    }

    @Override
    public boolean updateData(String indexName, String typeName, String dataId, JSONObject data) {
        // 可加入预处理、空值过滤等逻辑
        return esTemplate.update(indexName, typeName, dataId, data);
    }

    @Override
    public boolean deleteData(String indexName, String typeName, String dataId) {
        return esTemplate.delete(indexName, typeName, dataId);
    }

    @Override
    public boolean saveData(String indexName, String typeName, String dataId, JSONObject data) {
        // 可加入预处理、空值过滤等逻辑
        return esTemplate.saveOrUpdate(indexName, typeName, dataId, data);
    }

    @Override
    public JSONObject searchData(String indexName, String typeName, JSONObject query) {
        return esTemplate.search(indexName, typeName, query);
    }
}