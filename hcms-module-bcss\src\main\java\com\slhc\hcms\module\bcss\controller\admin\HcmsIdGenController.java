package com.slhc.hcms.module.bcss.controller.admin;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.slhc.hcms.module.bcss.entity.HcmsIdGen;
import com.slhc.hcms.module.bcss.service.IHcmsIdGenService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 序号表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Tag(name="序号表")
@RestController
@RequestMapping("/admin/hcmsIdGen")
@Slf4j
public class HcmsIdGenController extends JeecgController<HcmsIdGen, IHcmsIdGenService> {
	@Autowired
	private IHcmsIdGenService hcmsIdGenService;
	
	/**
	 * 分页列表查询
	 *
	 * @param hcmsIdGen
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "序号表-分页列表查询")
	@Operation(summary="序号表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<HcmsIdGen>> queryPageList(HcmsIdGen hcmsIdGen,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<HcmsIdGen> queryWrapper = QueryGenerator.initQueryWrapper(hcmsIdGen, req.getParameterMap());
		Page<HcmsIdGen> page = new Page<HcmsIdGen>(pageNo, pageSize);
		IPage<HcmsIdGen> pageList = hcmsIdGenService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param hcmsIdGen
	 * @return
	 */
	@AutoLog(value = "序号表-添加")
	@Operation(summary="序号表-添加")
	@RequiresPermissions("admin:hcms_id_gen:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody HcmsIdGen hcmsIdGen) {
		hcmsIdGenService.save(hcmsIdGen);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param hcmsIdGen
	 * @return
	 */
	@AutoLog(value = "序号表-编辑")
	@Operation(summary="序号表-编辑")
	@RequiresPermissions("admin:hcms_id_gen:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody HcmsIdGen hcmsIdGen) {
		hcmsIdGenService.updateById(hcmsIdGen);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "序号表-通过id删除")
	@Operation(summary="序号表-通过id删除")
	@RequiresPermissions("admin:hcms_id_gen:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		hcmsIdGenService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "序号表-批量删除")
	@Operation(summary="序号表-批量删除")
	@RequiresPermissions("admin:hcms_id_gen:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.hcmsIdGenService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "序号表-通过id查询")
	@Operation(summary="序号表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<HcmsIdGen> queryById(@RequestParam(name="id",required=true) String id) {
		HcmsIdGen hcmsIdGen = hcmsIdGenService.getById(id);
		if(hcmsIdGen==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(hcmsIdGen);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param hcmsIdGen
    */
    @RequiresPermissions("admin:hcms_id_gen:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HcmsIdGen hcmsIdGen) {
        return super.exportXls(request, hcmsIdGen, HcmsIdGen.class, "序号表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:hcms_id_gen:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, HcmsIdGen.class);
    }

}
