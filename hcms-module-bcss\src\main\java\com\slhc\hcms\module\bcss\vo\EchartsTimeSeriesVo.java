package com.slhc.hcms.module.bcss.vo;

import com.slhc.hcms.module.bcss.enums.TimeDimensionEnum;
import lombok.Data;

import java.util.List;

@Data
public class EchartsTimeSeriesVo {
    // 当前时间维度类型
    private TimeDimensionEnum dimensionType;
    // 时间轴标签 ["00:00",...,"23:00"] 或 ["01","02",...,"30"] 等
    private List<String> timeAxis;
    // 数据系列
    private List<SeriesData> series;
    // 总任务数
    private int totalTaskCount;
    // 总接收任务数
    private int totalReceivedTaskCount;


    /**
     * SeriesData类用于封装图表系列数据信息
     * 包含系列名称、图表类型、数据列表和堆叠分组等属性
     */
    @Data
    public static class SeriesData {
        private String name;             // 系列名称
        private String type = "line";    // 图表类型
        private List<Integer> data;      // 时间序列数据
        private String stack;            // 堆叠分组（可选）
    }
}
