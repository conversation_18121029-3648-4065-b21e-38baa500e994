package org.jeecg.common.util.encryption;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class RsaEncryptUtil {

    // PKCS#8格式私钥
    private static final String PRIVATE_KEY = """
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            """;

    private static final String ENCRYPTION_HEADER = "RSA_ENC:";

    public static String rsaDecrypt(String encryptedText) throws Exception {
        // 1. 检查并去除加密头部
        if (!encryptedText.startsWith(ENCRYPTION_HEADER)) {
            throw new IllegalArgumentException("无效的加密数据格式：缺少RSA_ENC:前缀");
        }
        String base64CipherText = encryptedText.substring(ENCRYPTION_HEADER.length());

        // 2. 准备私钥
        String pemKey = PRIVATE_KEY
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");  // 去除所有空白字符

        byte[] decodedKey = Base64.getDecoder().decode(pemKey);

        // 3. 生成私钥对象
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        PrivateKey privateKey = KeyFactory.getInstance("RSA").generatePrivate(keySpec);

        // 4. 初始化解密器
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        // 5. 执行解密
        byte[] encryptedData = Base64.getDecoder().decode(base64CipherText);
        byte[] decryptedBytes = cipher.doFinal(encryptedData);

        return new String(decryptedBytes);
    }

//    // 测试方法
//    public static void main(String[] args) {
//        try {
//            // 此处填写实际的前端加密输出
//            String encryptedPassword = "RSA_ENC:UZwdYi4/I5Z2gWdrvEld8/kbmQpHaLQcNLG7jsz1rBZO/fGj/z+OEg1Gr6kIbPa4wr8Fq5hTl+L/vaQAaSoBGA==";
//
//            String decrypted = rsaDecrypt(encryptedPassword);
//            System.out.println("解密结果: " + decrypted);
//        } catch (Exception e) {
//            System.err.println("解密过程中发生错误:");
//            e.printStackTrace();
//        }
//    }
}
/// 以下是在前端的代码
///*
// * @description: 前端非对称加密工具
// * @author: Jyuntou
// */
//import JSEncrypt from 'jsencrypt'
//
//// 硬编码的公钥 (RSA 公钥)
//        const PUBLIC_KEY = `
//        -----BEGIN PUBLIC KEY-----
//MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJ+oVQt93wQfQJtEilMpfXbg5bbaJTd5
//Y0acKG65/QsIDUdeLf02WUOOXUyigCwCDGJqmhBjNorJZnvJ7tLDbGECAwEAAQ==
//        -----END PUBLIC KEY-----
//        `;
//// 定义加密头部常量
//        const ENCRYPTION_HEADER = 'RSA_ENC:';
///**
// * 对密码进行加密
// * @param {string} str - 加密前的密码
// * @returns {string} - 加密后的密码
// */
//export function rsaEncrypt(data) {
//  const encryptor = new JSEncrypt()
//    encryptor.setPublicKey(PUBLIC_KEY)
//  const encryptedData = encryptor.encrypt(data)
//    return ENCRYPTION_HEADER + encryptedData
//}
///**
// * 判断字符串是否经过 RSA 加密（是否包含加密头部）
// * @param {string} str - 要检查的字符串
// * @returns {boolean} - 是否经过加密
// */
//export function isRsaEncrypted(str) {
//    return typeof str === 'string' && str.startsWith(ENCRYPTION_HEADER)
//}