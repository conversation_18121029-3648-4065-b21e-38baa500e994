## Basic reverse proxy server ##
upstream backend  {
    server 127.0.0.1:8090;
}

server {
    listen       80;
    # server_name  localhost;

    #charset koi8-r;

    #access_log  logs/host.access.log  main;
    root   /fe;
    location / {
        root   /fe/hcms/;
        try_files $uri $uri/ /index.html;
    }
    location ^~ /report/ib  {
        alias /fe/report/ib;
        try_files $uri $uri/ /index.html;
    }
    location ^~ /report/gv  {
        alias /fe/report/gv;
        try_files $uri $uri/ /index.html;
    }
    location ^~ /scada {
        alias /fe/hcms-scada/;
        try_files $uri $uri/ /scada/index.html;
    }
    location ~* \.json$ {
            add_header Content-Type application/json;
            # add_header Access-Control-Allow-Origin *; # 如需跨域则取消注释
    }
    location ^~ /hcms/websocket/ {
        # 将请求代理到后端
        proxy_pass http://backend;

        # !!! 以下是WebSocket必须的核心配置 !!!
        proxy_http_version 1.1; # 必须使用1.1版本
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # !!! 关键：为WebSocket设置超长的超时时间（注：解决连接成功后，即可断开问题） !!!
        proxy_read_timeout 3600s; # 连接保持1小时
        proxy_send_timeout 3600s; # 发送超时也设置长一些

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 可选：禁用缓冲，以实现更实时的通信
        proxy_buffering off;
    }
    location ^~ /hcms {
        # ^~/api 表示匹配前缀为api的请求
        # 注：proxy_pass的结尾有/， -> 效果：会在请求时将/api/*后面的路径直接拼接到后面
        proxy_pass  http://backend;
        proxy_redirect     off;
        proxy_set_header   Host             $host;        # 传递域名
        proxy_set_header   X-Real-IP        $remote_addr; # 传递ip
        proxy_set_header   X-Scheme         $scheme;      # 传递协议
        proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;

        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_max_temp_file_size    0;
        proxy_connect_timeout       30;
        proxy_send_timeout          90;
        proxy_read_timeout          90;
        proxy_buffer_size           4k;
        proxy_buffers               4 32k;
        proxy_busy_buffers_size     64k;
        proxy_temp_file_write_size  64k;
    }
    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
