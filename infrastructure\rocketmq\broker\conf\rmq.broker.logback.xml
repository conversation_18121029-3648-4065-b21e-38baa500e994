<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<configuration scan="true" scanPeriod="30 seconds">

    <appender name="DefaultSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="DefaultAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}broker_default.log</file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}broker_default.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="DefaultSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="DefaultSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqBrokerSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqBrokerAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}broker.log</file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}broker.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>20</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>128MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqBrokerSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqBrokerSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqProtectionSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqProtectionAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}protection.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}protection.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqProtectionSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqProtectionSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqWaterMarkSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqWaterMarkAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}watermark.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}watermark.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqWaterMarkSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqWaterMarkSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqRocksDBSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqStoreAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}rocksdb.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}rocksdb.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>128MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqRocksDBSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqRocksDBSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqStoreSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqStoreAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}store.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}store.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>128MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqStoreSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqStoreSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqTieredStoreSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqTieredStoreAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}tiered_store.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}tiered_store.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>128MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqTieredStoreSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqTieredStoreSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqTrafficSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqTrafficAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}${file.separator}logs${file.separator}rocketmqlogs${file.separator}broker_traffic.log</file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${user.home}${file.separator}logs${file.separator}rocketmqlogs${file.separator}otherdays${file.separator}broker_traffic.%i.log.gz</fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqTrafficSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqTrafficSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqRemotingSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqRemotingAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}remoting.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}remoting.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqRemotingSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqRemotingSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqStoreErrorSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqStoreErrorAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}storeerror.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}storeerror.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqStoreErrorSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqStoreErrorSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqTransactionSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqTransactionAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}transaction.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}transaction.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqTransactionSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqTransactionSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqRebalanceLockSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqRebalanceLockAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}lock.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}lock.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>5</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqRebalanceLockSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqRebalanceLockSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqFilterSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqFilterAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}filter.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}filter.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqRebalanceLockSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqFilterSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqStatsSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqStatsAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}stats.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}stats.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>5</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqStatsSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqStatsSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqCommercialSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqCommercialAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}commercial.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}commercial.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>500MB</maxFileSize>
                </triggeringPolicy>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqCommercialSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqCommercialSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqPopSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqPopAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}${file.separator}logs${file.separator}rocketmqlogs${file.separator}pop.log</file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${user.home}${file.separator}logs${file.separator}rocketmqlogs${file.separator}otherdays${file.separator}pop.%i.log
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>20</maxIndex>
                </rollingPolicy>
                <triggeringPolicy
                        class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>128MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqPopSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqPopSiftingAppender_inner"/>
    </appender>

    <appender name="RocketmqColdCtrSiftingAppender" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqColdCtrAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${user.home}/logs/rocketmqlogs/coldctr.log</file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>${user.home}/logs/rocketmqlogs/otherdays/coldctr.%i.log
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>10</maxIndex>
                </rollingPolicy>
                <triggeringPolicy
                        class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>100MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <appender name="RocketmqBrokerMetricsSiftingAppender_inner" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>brokerContainerLogDir</key>
            <defaultValue>${file.separator}</defaultValue>
        </discriminator>
        <sift>
            <appender name="RocketmqBrokerMetricsAppender"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>
                    ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}broker_metrics.log
                </file>
                <append>true</append>
                <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                    <fileNamePattern>
                        ${user.home}${file.separator}logs${file.separator}rocketmqlogs${brokerLogDir:-${file.separator}}${brokerContainerLogDir}${file.separator}otherdays${file.separator}broker_metrics.%i.log.gz
                    </fileNamePattern>
                    <minIndex>1</minIndex>
                    <maxIndex>3</maxIndex>
                </rollingPolicy>
                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                    <maxFileSize>512MB</maxFileSize>
                </triggeringPolicy>
                <encoder>
                    <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
                    <charset class="java.nio.charset.Charset">UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
    <appender name="RocketmqBrokerMetricsSiftingAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RocketmqBrokerMetricsSiftingAppender_inner"/>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p %t - %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <logger name="RocketmqBroker" additivity="false" level="INFO">
        <appender-ref ref="RocketmqBrokerSiftingAppender"/>
    </logger>

    <logger name="RocketmqProtection" additivity="false" level="INFO">
        <appender-ref ref="RocketmqProtectionSiftingAppender"/>
    </logger>

    <logger name="RocketmqWaterMark" additivity="false" level="INFO">
        <appender-ref ref="RocketmqWaterMarkSiftingAppender"/>
    </logger>

    <logger name="RocketmqCommon" additivity="false" level="INFO">
        <appender-ref ref="RocketmqBrokerSiftingAppender"/>
    </logger>

    <logger name="RocketmqRocksDB" additivity="false" level="INFO">
        <appender-ref ref="RocketmqRocksDBSiftingAppender"/>
    </logger>

    <logger name="RocketmqStore" additivity="false" level="INFO">
        <appender-ref ref="RocketmqStoreSiftingAppender"/>
    </logger>

    <logger name="RocketmqStoreError" additivity="false" level="INFO">
        <appender-ref ref="RocketmqStoreErrorSiftingAppender"/>
    </logger>

    <logger name="RocketmqTieredStore" additivity="false" level="INFO">
        <appender-ref ref="RocketmqTieredStoreSiftingAppender"/>
    </logger>

    <logger name="RocketmqTransaction" additivity="false" level="INFO">
        <appender-ref ref="RocketmqTransactionSiftingAppender"/>
    </logger>

    <logger name="RocketmqRebalanceLock" additivity="false" level="INFO">
        <appender-ref ref="RocketmqRebalanceLockSiftingAppender"/>
    </logger>

    <logger name="RocketmqRemoting" additivity="false" level="INFO">
        <appender-ref ref="RocketmqRemotingSiftingAppender"/>
    </logger>

    <logger name="RocketmqStats" additivity="false" level="INFO">
        <appender-ref ref="RocketmqStatsSiftingAppender"/>
    </logger>

    <logger name="RocketmqCommercial" additivity="false" level="INFO">
        <appender-ref ref="RocketmqCommercialSiftingAppender"/>
    </logger>

    <logger name="RocketmqFilter" additivity="false" level="INFO">
        <appender-ref ref="RocketmqFilterSiftingAppender"/>
    </logger>

    <logger name="RocketmqConsole" additivity="false" level="INFO">
        <appender-ref ref="STDOUT"/>
    </logger>

    <logger name="RocketmqPop" additivity="false" level="INFO">
        <appender-ref ref="RocketmqPopSiftingAppender"/>
    </logger>

    <logger name="RocketmqColdCtr" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="RocketmqColdCtrSiftingAppender"/>
    </logger>

    <logger name="RocketmqTraffic" additivity="false" level="INFO">
        <appender-ref ref="RocketmqTrafficSiftingAppender"/>
    </logger>

    <!-- Use json formatter to log metrics -->
    <logger name="io.opentelemetry.exporter.logging.otlp.OtlpJsonLoggingMetricExporter" additivity="false" level="INFO">
        <appender-ref ref="RocketmqBrokerMetricsSiftingAppender"/>
    </logger>

    <logger name="io.opentelemetry.exporter.logging.LoggingMetricExporter" additivity="false" level="INFO">
        <appender-ref ref="RocketmqBrokerMetricsSiftingAppender"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="DefaultSiftingAppender"/>
    </root>
</configuration>
