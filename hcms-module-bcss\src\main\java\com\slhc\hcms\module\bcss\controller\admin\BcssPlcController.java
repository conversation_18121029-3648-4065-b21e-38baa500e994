package com.slhc.hcms.module.bcss.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.slhc.hcms.module.bcss.common.FieldNameUtils;
import com.slhc.hcms.module.bcss.entity.BcssPlc;
import com.slhc.hcms.module.bcss.service.IBcssPlcService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.exception.JeecgBootBizTipException;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;

 /**
 * @Description: PLC表
 * @Author: jeecg-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Tag(name="PLC表")
@RestController
@RequestMapping("/admin/bcssPlc")
@Slf4j
public class BcssPlcController extends JeecgController<BcssPlc, IBcssPlcService> {
	@Autowired
	private IBcssPlcService bcssPlcService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bcssPlc
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "PLC表-分页列表查询")
	@Operation(summary="PLC表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BcssPlc>> queryPageList(BcssPlc bcssPlc,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<BcssPlc> queryWrapper = QueryGenerator.initQueryWrapper(bcssPlc, req.getParameterMap());
		Page<BcssPlc> page = new Page<BcssPlc>(pageNo, pageSize);
		IPage<BcssPlc> pageList = bcssPlcService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  *  校验PLC
	  *
	  * @param id
	  * @return
	  */
//	 @AutoLog(value = "plc-校验PLC")
//	 @Operation(summary="plc-校验PLC")
////	 @RequiresPermissions("admin:bcss_plc:edit")
////	 @RequestMapping(value = "/enable", method = {RequestMethod.PUT,RequestMethod.POST})
//	 public Result<String> doJudge(@RequestParam(name="id",required=true) String id) {
//		 bcssPlcService.enablePlc(id);
//		 return Result.OK("激活成功!");
//	 }

	 /**
	  *  激活站点
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "plc-启用PLC")
	 @Operation(summary="plc-启用PLC")
	 @RequiresPermissions("admin:bcss_plc:edit")
	 @RequestMapping(value = "/enable", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> enable(@RequestParam(name="id",required=true) String id) {
		 bcssPlcService.enablePlc(id);
		 return Result.OK("启用成功!");
	 }

	 /**
	  *  停用PLC
	  *
	  * @param id
	  * @return
	  */
	 @AutoLog(value = "plc-停用PLC")
	 @Operation(summary="plc-停用PLC")
	 @RequiresPermissions("admin:bcss_plc:edit")
	 @RequestMapping(value = "/disable", method = {RequestMethod.PUT,RequestMethod.POST})
	 public Result<String> disable(@RequestParam(name="id",required=true) String id) {
		 bcssPlcService.disablePlc(id);
		 return Result.OK("停用成功!");
	 }

	 /**
	 *   添加
	 *
	 * @param bcssPlc
	 * @return
	 */
	@AutoLog(value = "PLC表-添加")
	@Operation(summary="PLC表-添加")
	@RequiresPermissions("admin:bcss_plc:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BcssPlc bcssPlc) {
		if (bcssPlc == null) {
			throw new JeecgBootBizTipException("参数异常！");
		}
		//用户名不能为空
		if (StringUtils.isBlank(bcssPlc.getName())) {
			throw new JeecgBootBizTipException("PLC名称不能为空格！");
		}
		bcssPlcService.doJudge(bcssPlc);
		boolean save = bcssPlcService.save(bcssPlc);
		if (save) {
			bcssPlcService.updateById(bcssPlc);
			return Result.OK("添加成功！");
		}
		return Result.error("添加失败");

	}
	
	/**
	 *  编辑
	 *
	 * @param bcssPlc
	 * @return
	 */
	@AutoLog(value = "PLC表-编辑")
	@Operation(summary="PLC表-编辑")
	@RequiresPermissions("admin:bcss_plc:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BcssPlc bcssPlc) {
		bcssPlcService.doJudge(bcssPlc);
		boolean updateById = bcssPlcService.updateById(bcssPlc);
		if (updateById)
			return Result.OK("编辑成功!");
		return Result.error("编辑失败");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "PLC表-通过id删除")
	@Operation(summary="PLC表-通过id删除")
	@RequiresPermissions("admin:bcss_plc:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bcssPlcService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "PLC表-批量删除")
	@Operation(summary="PLC表-批量删除")
	@RequiresPermissions("admin:bcss_plc:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bcssPlcService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "PLC表-通过id查询")
	@Operation(summary="PLC表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BcssPlc> queryById(@RequestParam(name="id",required=true) String id) {
		BcssPlc bcssPlc = bcssPlcService.getById(id);
		if(bcssPlc==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bcssPlc);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bcssPlc
    */
    @RequiresPermissions("admin:bcss_plc:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BcssPlc bcssPlc) {
        return super.exportXls(request, bcssPlc, BcssPlc.class, "PLC表", FieldNameUtils.getFieldNameString(BcssPlc.class,"delFlag,tenantId"));
    }
	 /**
	  * 下载空白模板
	  *
	  */
	 @RequiresPermissions("admin:bcss_plc:downloadTemplate")
	 @GetMapping(value = "/downloadTemplate")
	 public ModelAndView downloadTemplate() {
		 return super.downloadTemplate("data/ImportTemplate/PLC模板.xlsx");
	 }
    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:bcss_plc:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        // 调用父类的带创建时间导入方法
        return super.importExcelWithCreateTime(request, response, BcssPlc.class);
    }

	 /**
	  * 导入校验
	  * @param list
	  */
	 public void checkExcelList(List<BcssPlc> list) {
		 List<BcssPlc> bcssPlcListIp= new ArrayList<BcssPlc>();
		 List<BcssPlc> bcssPlcListName= new ArrayList<BcssPlc>();

		// 检查Excel内部空值
		 bcssPlcListIp = list.stream().filter(i -> StringUtils.isEmpty(i.getIp())).toList();
		 if (!bcssPlcListIp.isEmpty())
			 throw new JeecgBootBizTipException("导入表格中存在IP地址空值");
		 bcssPlcListName = list.stream().filter(i -> StringUtils.isEmpty(i.getName())).toList();
		 if (!bcssPlcListName.isEmpty())
			 throw new JeecgBootBizTipException("导入表格中存在PLC名称空值");
		 
		// 检查Excel内部IP重复
		 Set<String> uniqueIps = new HashSet<>();
		 boolean hasDuplicateIp = list.stream().anyMatch(plc -> !uniqueIps.add(plc.getIp()));
		 if (hasDuplicateIp) {
			 throw new JeecgBootBizTipException("导入表格中存在重复IP地址");
		 }
		 
		 // 检查Excel内部名称重复
		 Set<String> uniqueNames = new HashSet<>();
		 boolean hasDuplicateName = list.stream().anyMatch(plc -> !uniqueNames.add(plc.getName()));
		 if (hasDuplicateName) {
			 throw new JeecgBootBizTipException("导入表格中存在重复PLC名称");
		 }
		 
		 // 检查EXCEL内部IP地址格式合法性
		 List<String> invalidIps = new ArrayList<>();
		 for (BcssPlc plc : list) {
			 String ip = plc.getIp();
			 if (!isValidIpAddress(ip)) {
				 invalidIps.add(ip);
			 }
		 }
		 if (!invalidIps.isEmpty()) {
			 String invalidIpStr;
			 if (invalidIps.size() > 3) {
				 // 如果不合法的IP数量超过3个，只显示前3个并加"等"
				 invalidIpStr = String.join("; ", invalidIps.subList(0, 3)) + " 等";
			 } else {
				 invalidIpStr = String.join("; ", invalidIps);
			 }
			 throw new JeecgBootBizTipException("以下IP地址格式不合法：" + invalidIpStr);
		 }
		 
		 List<String> stationCodeList = list.stream().map(BcssPlc::getIp).toList();
		 bcssPlcListIp = bcssPlcService.getBaseMapper().selectList(new LambdaQueryWrapper<BcssPlc>().in(BcssPlc::getIp, stationCodeList));
		 if (!bcssPlcListIp.isEmpty()){
			 List<String> existingIps = bcssPlcListIp.stream().map(BcssPlc::getIp).toList();
			 String stationCodes;
			 if (existingIps.size() > 3) {
				 // 如果已存在的IP数量超过3个，只显示前3个并加"等"
				 stationCodes = String.join("; ", existingIps.subList(0, 3)) + " 等";
			 } else {
				 stationCodes = String.join("; ", existingIps);
			 }
			 throw new JeecgBootBizTipException(" 设备IP " + stationCodes + " 已经注册，不能重复导入");
		 }
		 stationCodeList = list.stream().map(BcssPlc::getName).toList();
		 bcssPlcListName = bcssPlcService.getBaseMapper().selectList(new LambdaQueryWrapper<BcssPlc>().in(BcssPlc::getName, stationCodeList));
		 if (!bcssPlcListName.isEmpty()){
			 List<String> existingNames = bcssPlcListName.stream().map(BcssPlc::getName).toList();
			 String stationCodes;
			 if (existingNames.size() > 3) {
				 // 如果已存在的名称数量超过3个，只显示前3个并加"等"
				 stationCodes = String.join("; ", existingNames.subList(0, 3)) + " 等";
			 } else {
				 stationCodes = String.join("; ", existingNames);
			 }
			 throw new JeecgBootBizTipException(" 设备名称 " + stationCodes + " 已经注册，不能重复导入");
		 }
	 }
	 
	 /**
	  * 验证IP地址格式是否合法
	  * @param ip 要验证的IP地址
	  * @return 如果IP地址格式合法返回true，否则返回false
	  */
	 private boolean isValidIpAddress(String ip) {
		 if (ip == null || ip.isEmpty()) {
			 return false;
		 }
		 
		 // IPv4地址正则表达式
		 String ipRegex = "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
		 return ip.matches(ipRegex);
	 }

}
