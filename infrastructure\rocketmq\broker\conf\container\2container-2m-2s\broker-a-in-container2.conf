# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

#Master配置
brokerClusterName=DefaultCluster
brokerName=broker-a
brokerId=1
brokerRole=SLAVE
flushDiskType=ASYNC_FLUSH
storePathRootDir=/root/broker-a/store
storePathCommitLog=/root/broker-a/store/commitlog
listenPort=10911
haListenPort=10912
totalReplicas=2
inSyncReplicas=2
minInSyncReplicas=1
enableAutoInSyncReplicas=true
slaveReadEnable=true
brokerHeartbeatInterval=1000
brokerNotActiveTimeoutMillis=5000
sendHeartbeatTimeoutMillis=1000
enableSlaveActingMaster=true