package com.slhc.hcms.module.bcss.vo;

import lombok.Data;

/**
 * @ClassName: 报表数据实现
 * @Description: 报表数据实现
 * @Author: HCS
 * @Date: 2025/7/21 15:05
 */
@Data
public class BcssReportVo {
    /**
     * 站点编号
     */
    private String stationId;
    /**
     * 站点名称
     */
    private String stationName;
    /**
     * 任务类型
     */
//    private String taskType;
    /**
     * 任务日期
     */
    private String date;
    /**
     * 任务数量
     */
    private int taskQty;
    /**
     * 完成任务数量
     */
    private int finishTaskQty;
    /**
     * 取消任务数量
     */
    private int cancelTaskQty;
    /**
     * 超时任务数量
     */
    private int timeoutTaskQty;
    /**
     * 接收任务数量
     */
    private int receiveTaskQty;
    /**
     * 接收完成任务数量
     */
    private int receiveFinishTaskQty;

//    /**
//     * 总任务数量
//     */
//    private int totalTasks;
//    /**
//     * 已完成任务数量
//     */
//    private int finishedTasks;
    /**
     //     * 已取消任务数量
     //     */
//    private int canceledTasks;

//    public String getStationId() {
//        return stationId;
//    }
//
//    public void setStationId(String stationId) {
//        this.stationId = stationId;
//    }
//
//    public String getStationName() {
//        return stationName;
//    }
//
//    public void setStationName(String stationName) {
//        this.stationName = stationName;
//    }

//    public String getTaskType() {
//        return taskType;
//    }

//    public void setTaskType(String taskType) {
//        this.taskType = taskType;
//    }

//    public String getDate() {
//        return date;
//    }
//
//    public void setDate(String date) {
//        this.date = date;
//    }
//
//    public int getTaskQty() {
//        return taskQty;
//    }
//
//    public void setTaskQty(int taskQty) {
//        this.taskQty = taskQty;
//    }
//
//    public int getFinishTaskQty() {
//        return finishTaskQty;
//    }
//
//    public void setFinishTaskQty(int finishTaskQty) {
//        this.finishTaskQty = finishTaskQty;
//    }
//
//    public int getCancelTaskQty() {
//        return cancelTaskQty;
//    }
//
//    public void setCancelTaskQty(int cancelTaskQty) {
//        this.cancelTaskQty = cancelTaskQty;
//    }
//
//    public int getTimeoutTaskQty() {
//        return timeoutTaskQty;
//    }
//
//    public void setTimeoutTaskQty(int timeoutTaskQty) {
//        this.timeoutTaskQty = timeoutTaskQty;
//    }
//
//    public int getReceiveTaskQty() {
//        return receiveTaskQty;
//    }
//
//    public void setReceiveTaskQty(int receiveTaskQty) {
//        this.receiveTaskQty = receiveTaskQty;
//    }
//
//    public int getReceiveFinishTaskQty() {
//        return receiveFinishTaskQty;
//    }
//
//    public void setReceiveFinishTaskQty(int receiveFinishTaskQty) {
//        this.receiveFinishTaskQty = receiveFinishTaskQty;
//    }
//
//    public void setTotalTasks(Integer totalTasks) {
//        this.taskQty = totalTasks;
//    }
//
//    public void setFinishedTasks(Integer finishedTasks) {
//        this.finishTaskQty = finishedTasks;
//    }
//
//    public void setCanceledTasks(Integer canceledTasks) {
//        this.cancelTaskQty = canceledTasks;
//    }
}
