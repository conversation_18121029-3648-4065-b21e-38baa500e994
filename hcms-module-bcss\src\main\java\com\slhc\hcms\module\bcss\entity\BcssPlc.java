package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: PLC表
 * @Author: jeecg-boot
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Data
@TableName("bcss_plc")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="PLC表")
public class BcssPlc implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**删除状态(0-正常,1-已删除)*/
    @Schema(description = "删除状态(0-正常,1-已删除)")
    @TableLogic
    private java.lang.Integer delFlag;
	/**名称*/
	@Excel(name = "名称", width = 15)
    @Schema(description = "名称")
    private java.lang.String name;
	/**ip地址*/
	@Excel(name = "ip地址", width = 15)
    @Schema(description = "ip地址")
    private java.lang.String ip;
	/**端口*/
	@Excel(name = "端口", width = 15)
    @Schema(description = "端口")
    private java.lang.Integer port;
	/**机架号*/
	@Excel(name = "机架号", width = 15)
    @Schema(description = "机架号")
    private java.lang.Integer raw;
	/**槽位号*/
	@Excel(name = "槽位号", width = 15)
    @Schema(description = "槽位号")
    private java.lang.Integer slot;
	/**PLC类型*/
	@Excel(name = "PLC类型", width = 15, dicCode = "cpu_type")
    @Schema(description = "PLC类型")
    @Dict(dicCode = "cpu_type")
    private java.lang.Integer cpuType;
	/**读取BD*/
	@Excel(name = "读取BD", width = 15)
    @Schema(description = "读取BD")
    private java.lang.Integer readDbNum;
	/**高频读取首地址*/
	@Excel(name = "高频读取首地址", width = 15)
    @Schema(description = "高频读取首地址")
    private java.lang.Integer readHighStartAddr;
	/**高频读地址长度*/
	@Excel(name = "高频读地址长度", width = 15)
    @Schema(description = "高频读地址长度")
    private java.lang.Integer readHighLen;
	/**低频读取首地址*/
	@Excel(name = "低频读取首地址", width = 15)
    @Schema(description = "低频读取首地址")
    private java.lang.Integer readLowStartAddr;
	/**低频读地址长度*/
	@Excel(name = "低频读地址长度", width = 15)
    @Schema(description = "低频读地址长度")
    private java.lang.Integer readLowLen;
	/**写入BD*/
	@Excel(name = "写入BD", width = 15)
    @Schema(description = "写入BD")
    private java.lang.Integer writeDbNum;
	/**写入首地址*/
	@Excel(name = "写入首地址", width = 15)
    @Schema(description = "写入首地址")
    private java.lang.Integer writeStartAddr;
	/**写入地址长度*/
	@Excel(name = "写入地址长度", width = 15)
    @Schema(description = "写入地址长度")
    private java.lang.Integer writeLen;
	/**是否可用：1可用、0不可用*/
	@Excel(name = "启用/停用", width = 15, dicCode = "plc_status")
    @Dict(dicCode = "plc_status")
    @Schema(description = "是否可用：1可用、0不可用")
    private java.lang.Integer status;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**租户Id*/
    @Schema(description = "租户Id")
    private java.lang.String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
}
