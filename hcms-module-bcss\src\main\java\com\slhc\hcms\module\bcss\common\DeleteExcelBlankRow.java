package com.slhc.hcms.module.bcss.common;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
public class DeleteExcelBlankRow {

    /**
     * 删除列表中所有字段都是null或空值的对象
     * @param list 要处理的泛型列表
     * @param <T> 列表中元素的类型
     * @return 处理后的列表
     */
    public static <T> List<T> removeEmptyObjects(List<T> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        List<T> result = new ArrayList<>();

        for (T item : list) {
            if (item == null) {
                continue; // 跳过null元素
            }

            // 检查对象是否所有字段都是null或空值
            if (!isAllFieldsEmpty(item)) {
                result.add(item);
            }
        }

        list.clear();
        list.addAll(result);

        return list;
    }

    /**
     * 检查对象的所有字段是否都是null或空值
     * @param obj 要检查的对象
     * @return 如果所有字段都是null或空值则返回true，否则返回false
     */
    private static boolean isAllFieldsEmpty(Object obj) {
        // 获取对象的所有字段，包括私有字段
        Field[] fields = obj.getClass().getDeclaredFields();

        for (Field field : fields) {
            if (field.getName().equals("serialVersionUID"))
                continue;
            // 设置可以访问私有字段
            field.setAccessible(true);

            try {
                Object value = field.get(obj);

                // 如果字段值不是空的，则对象不是空对象
                if (!isEmptyValue(value)) {
                    return false;
                }
            } catch (IllegalAccessException e) {
                // 处理反射访问异常
                log.error(e.getMessage());
                // 遇到访问异常时，默认认为字段有值
                return false;
            }
        }

        // 所有字段都是空值
        return true;
    }

    /**
     * 检查值是否为null或空值
     * @param value 要检查的值
     * @return 如果是null或空值则返回true，否则返回false
     */
    private static boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }

        // 检查字符串
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }

        // 检查集合
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }

        // 检查数组
        if (value.getClass().isArray()) {
            return ((Object[]) value).length == 0;
        }

        // 其他类型只要不为null就认为不是空值
        return false;
    }

}
