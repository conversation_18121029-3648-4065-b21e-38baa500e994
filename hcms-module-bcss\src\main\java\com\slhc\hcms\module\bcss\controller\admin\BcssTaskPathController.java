package com.slhc.hcms.module.bcss.controller.admin;

import java.util.Arrays;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.slhc.hcms.module.bcss.entity.BcssTaskPath;
import com.slhc.hcms.module.bcss.service.IBcssTaskPathService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-11
 * @Version: V1.0
 */
@Tag(name="设备表")
@RestController
@RequestMapping("/admin/bcssTaskPath")
@Slf4j
public class BcssTaskPathController extends JeecgController<BcssTaskPath, IBcssTaskPathService> {
	@Autowired
	private IBcssTaskPathService bcssTaskPathService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bcssTaskPath
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "设备表-分页列表查询")
	@Operation(summary="设备表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BcssTaskPath>> queryPageList(BcssTaskPath bcssTaskPath,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<BcssTaskPath> queryWrapper = QueryGenerator.initQueryWrapper(bcssTaskPath, req.getParameterMap());
		Page<BcssTaskPath> page = new Page<BcssTaskPath>(pageNo, pageSize);
		IPage<BcssTaskPath> pageList = bcssTaskPathService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bcssTaskPath
	 * @return
	 */
	@AutoLog(value = "设备表-添加")
	@Operation(summary="设备表-添加")
	@RequiresPermissions("admin:bcss_task_path:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BcssTaskPath bcssTaskPath) {
		bcssTaskPathService.save(bcssTaskPath);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bcssTaskPath
	 * @return
	 */
	@AutoLog(value = "设备表-编辑")
	@Operation(summary="设备表-编辑")
	@RequiresPermissions("admin:bcss_task_path:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BcssTaskPath bcssTaskPath) {
		bcssTaskPathService.updateById(bcssTaskPath);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备表-通过id删除")
	@Operation(summary="设备表-通过id删除")
	@RequiresPermissions("admin:bcss_task_path:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bcssTaskPathService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备表-批量删除")
	@Operation(summary="设备表-批量删除")
	@RequiresPermissions("admin:bcss_task_path:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bcssTaskPathService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备表-通过id查询")
	@Operation(summary="设备表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BcssTaskPath> queryById(@RequestParam(name="id",required=true) String id) {
		BcssTaskPath bcssTaskPath = bcssTaskPathService.getById(id);
		if(bcssTaskPath==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bcssTaskPath);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bcssTaskPath
    */
    @RequiresPermissions("admin:bcss_task_path:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BcssTaskPath bcssTaskPath) {
        return super.exportXls(request, bcssTaskPath, BcssTaskPath.class, "设备表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:bcss_task_path:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, BcssTaskPath.class);
    }

}
