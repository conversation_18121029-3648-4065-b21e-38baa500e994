package com.slhc.hcms.module.bcss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.slhc.hcms.module.bcss.entity.BcssStation;

import java.util.List;

/**
 * @Description: 站点表
 * @Author: jeecg-boot
 * @Date:   2025-07-16
 * @Version: V1.0
 */
public interface IBcssStationService extends IService<BcssStation> {

    void enableBox(String id);


    void disableBox(String id);

    void deviceBind(String id, String deviceId);

    void deviceUnbind(String id, String deviceId);

    boolean checkDeviceBinding(String deviceId, String stationId);

    boolean removeByIds(List<String> list);
}
