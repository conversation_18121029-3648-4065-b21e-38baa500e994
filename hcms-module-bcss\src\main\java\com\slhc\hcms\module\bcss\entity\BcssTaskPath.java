package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-11
 * @Version: V1.0
 */
@Data
@TableName("bcss_task_path")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="设备表")
public class BcssTaskPath implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**删除状态(0-正常,1-已删除)*/
	@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @Schema(description = "删除状态(0-正常,1-已删除)")
    @TableLogic
    private java.lang.Integer delFlag;
	/**任务路径*/
	@Excel(name = "任务路径", width = 15)
    @Schema(description = "任务路径")
    private java.lang.String path;
	/**起始站点编号*/
	@Excel(name = "起始站点编号", width = 15)
    @Schema(description = "起始站点编号")
    private java.lang.Integer sourStationId;
	/**目的站点编号*/
	@Excel(name = "目的站点编号", width = 15)
    @Schema(description = "目的站点编号")
    private java.lang.String destionStationId;
	/**路径完成耗时*/
	@Excel(name = "路径完成耗时", width = 15)
    @Schema(description = "路径完成耗时")
    private java.lang.Integer consumeTime;
	/**路径完成的次数*/
	@Excel(name = "路径完成的次数", width = 15)
    @Schema(description = "路径完成的次数")
    private java.lang.Integer frequency;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**租户Id*/
	@Excel(name = "租户Id", width = 15)
    @Schema(description = "租户Id")
    private java.lang.String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
}
