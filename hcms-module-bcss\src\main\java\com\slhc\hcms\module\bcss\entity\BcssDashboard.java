package com.slhc.hcms.module.bcss.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.util.Date;
import java.util.List;
/**
 * @Description: 仪表盘统计信息
 * @Author: jyuntou
 * @Date:   2025-08-15
 * @Version: V1.0
 */
@Data
public class BcssDashboard {

//    private String deviceCode;
//    private String stationId;
//    private String stationName;
    private String stationCode;

    /**
     *  任务量统计
     */
    private String totalSend;      // 发箱总数
    private String completedSend;  // 发箱完成
    private String doReceived;     // 已经接收
    private String notReceived;    // 还未接收
    // 统计数据要用string
    public void setIntTotalSend(int totalSend) {
        this.totalSend = String.valueOf(totalSend);
    }
    public void setIntCompletedSend(int completedSend) {
        this.completedSend = String.valueOf(completedSend);
    }
    public void setIntDoReceived(int doReceived) {
        this.doReceived = String.valueOf(doReceived);
    }
    public void setIntNotReceived(int notReceived) {
        this.notReceived = String.valueOf(notReceived);
    }
    /**
     *  展示任务列表
     */
    private List<TaskView> toReceivedTasks;  // 对应前端 waiting 列表
    private List<TaskView> toFinishedTasks;  // 对应前端 todo 列表

    //内部静态类，用来放要展示的单个任务信息
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true) // 忽略未定义的字段
    public static class TaskView {
        private String id;                 // 任务编号 (对应前端 id)
        private String transportId;        // 运输编号 (对应前端 transportId)
        private Integer status;             // 任务状态 (对应前端 status)
        private Date start_time;         // 开始时间 (对应前端 start_time)
        private String sour_station_name;  // 起始站点 (对应前端 sour_station_name)
        private String dest_station_name;  // 目标站点 (对应前端 dest_station_name)

        // 构造方法：从 BcssTask 实体转换
        public TaskView(BcssTask task) {
            this.id = task.getId();
            this.transportId = task.getBoxBarcode();
            this.status = task.getStatus(); // 状态码转文字
            this.start_time = task.getStartTime();       // 假设 create_time 对应 start_time
            this.sour_station_name = task.getSourStationName();
            this.dest_station_name = task.getDestStationName();
        }
    }
    // 将 BcssTask 列表中的任务筛选放到 TaskView 列表
    public void setToReceivedTasks(List<BcssTask> tasks) {
        this.toReceivedTasks = tasks.stream()
                .map(TaskView::new)
                .collect(java.util.stream.Collectors.toList());
    }
    public void setToFinishedTasks(List<BcssTask> tasks) {
        this.toFinishedTasks = tasks.stream()
                .map(TaskView::new)
                .collect(java.util.stream.Collectors.toList());
    }
    /**
     *  展示设备状态列表
     */
    private List<DeviceStatusView> deviceStatusViews;
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true) // 忽略未定义的字段
    public static class DeviceStatusView {
        private String name;                 // 设备名称 (对应前端 name)
        private Integer status;             // 设备状态 (对应前端 status)

        // 构造方法：从 BcssDevice 实体转换
        public DeviceStatusView(BcssDevice device) {
            this.name = device.getName();
            this.status = device.getStatus();
        }
    }
    public void setDeviceStatusView(List<BcssDevice> devices) {
        this.deviceStatusViews = devices.stream()
                .map(DeviceStatusView::new)
                .collect(java.util.stream.Collectors.toList());
    }
}


