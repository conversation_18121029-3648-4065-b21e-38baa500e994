// 文件：hcms-module-bcss/src/test/java/com/slhc/hcms/modules/admin/controller/BcssBoxControllerIntegrationTest.java
package com.slhc.hcms.module.bcss.controller.admin;


import com.slhc.hcms.module.bcss.entity.BcssBox;
import com.slhc.hcms.module.bcss.service.IBcssBoxService;
import org.jeecg.common.api.vo.Result;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class BcssBoxControllerIntegrationTest {

    @Autowired
    private IBcssBoxService bcssBoxService;

    @Autowired
    private BcssBoxController bcssBoxController;

    @AfterEach
    void tearDown() {
        // 清理测试数据
        //bcssBoxService.removeById("test-id-1");
    }

    @Test
    void queryById_数据库真实访问() {
        // 插入测试数据
        BcssBox box = new BcssBox();
//        box.setId("test-id-1");
//        bcssBoxService.save(box);

        // 调用 controller 查询
        Result<BcssBox> result = bcssBoxController.queryById("1");

        assertNotNull(result);
        assertEquals(Result.OK().getCode(), result.getCode());
        assertNotNull(result.getResult());
        assertEquals("test-id-1", result.getResult().getId());
    }
}