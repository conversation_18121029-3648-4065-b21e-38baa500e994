package com.slhc.hcms.module.bcss.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
public class QueryFileLoader {

    /**
     * 从 classpath 读取查询文件内容（注：后期考虑整合）
     *
     * @param filePath 文件路径，如 "es-queries/taskQueries.json"
     */
    public static String readQueryFromFile(String filePath) throws IOException {
        ClassPathResource resource = new ClassPathResource(filePath);
        try (InputStream is = resource.getInputStream()) {
            return StreamUtils.copyToString(is, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("读取查询文件失败: {}", filePath, e);
            throw e;
        }
    }
}
