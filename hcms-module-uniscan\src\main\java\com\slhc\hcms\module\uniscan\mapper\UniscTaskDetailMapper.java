package com.slhc.hcms.module.uniscan.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.slhc.hcms.module.uniscan.entity.UniscTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: UniScan任务明细表
 * @Author: jeecg-boot
 * @Date:   2025-09-22
 * @Version: V1.0
 */
@Mapper
public interface UniscTaskDetailMapper extends BaseMapper<UniscTaskDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<UniscTaskDetail>
   */
	public List<UniscTaskDetail> selectByMainId(@Param("mainId") String mainId);
}
