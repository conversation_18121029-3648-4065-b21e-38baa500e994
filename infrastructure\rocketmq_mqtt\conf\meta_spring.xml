<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        classpath:/org/springframework/beans/factory/xml/spring-beans-4.1.xsd
        http://www.springframework.org/schema/context
        classpath:/org/springframework/context/config/spring-context-4.1.xsd
        http://www.springframework.org/schema/tool
        classpath:/org/springframework/beans/factory/xml/spring-tool-4.1.xsd
        http://www.springframework.org/schema/task
        classpath:/org/springframework/scheduling/config/spring-task-4.1.xsd"
       default-autowire="byName">

    <context:component-scan base-package="org.apache.rocketmq.mqtt.meta"/>


</beans>