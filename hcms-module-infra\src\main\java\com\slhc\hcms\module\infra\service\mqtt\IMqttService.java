package com.slhc.hcms.module.infra.service.mqtt;

import org.eclipse.paho.client.mqttv3.MqttException;

import java.util.Map;

/**
 * MQTT服务接口
 * 提供MQTT消息发布、订阅、事件监听等功能
 * 
 * @author: HCMS Team
 * @version: 2.0
 */
public interface IMqttService {
    
    /**
     * 发布消息到指定主题
     *
     * @param topic   主题
     * @param payload 消息内容
     * @return 是否发送成功
     */
    boolean publish(String topic, String payload);

    /**
     * 发布消息到指定主题，并指定QoS
     *
     * @param topic   主题
     * @param payload 消息内容
     * @param qos     服务质量等级(0,1,2)
     * @return 是否发送成功
     */
    boolean publish(String topic, String payload, int qos);
    
    /**
     * 发布消息到指定主题，并指定QoS和保留标志
     *
     * @param topic    主题
     * @param payload  消息内容
     * @param qos      服务质量等级(0,1,2)
     * @param retained 是否保留消息
     * @return 是否发送成功
     */
    boolean publish(String topic, String payload, int qos, boolean retained);

    /**
     * 订阅主题
     * @param topic 要订阅的主题
     */
    void subscribe(String topic);

    /**
     * 订阅主题，并指定QoS
     * @param topic 要订阅的主题
     * @param qos   QoS等级
     */
    void subscribe(String topic, int qos);
    
    /**
     * 使用事件监听器订阅主题
     * @param listener 事件监听器
     */
    void subscribe(MqttEventListener listener);
    
    /**
     * 取消订阅
     * @param topic 要取消订阅的主题
     */
    void unsubscribe(String topic);
    
    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    void registerEventListener(MqttEventListener listener);
    
    /**
     * 注销事件监听器
     * @param listener 事件监听器
     */
    void unregisterEventListener(MqttEventListener listener);
    
    /**
     * 获取连接状态
     * @return 是否已连接
     */
    boolean isConnected();
    
    /**
     * 获取连接信息
     * @return 连接信息
     */
    Map<String, Object> getConnectionInfo();
    
    /**
     * 获取消息队列状态
     * @return 队列状态信息
     */
    Map<String, Object> getMessageQueueStats();
    
    /**
     * 手动重新连接
     */
    void reconnect();
    
    /**
     * 断开连接
     */
    void disconnect();
    
    /**
     * 清空消息队列
     */
    void clearMessageQueue();
}
