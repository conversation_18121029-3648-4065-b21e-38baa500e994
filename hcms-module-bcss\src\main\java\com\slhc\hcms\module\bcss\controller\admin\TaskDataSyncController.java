package com.slhc.hcms.module.bcss.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.slhc.hcms.module.bcss.job.TaskDataSyncJob;
import com.slhc.hcms.module.infra.es.service.impl.ElasticsearchServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @ClassName: TaskDataSyncController
 * @Description: 测试用，任务数据同步控制器
 * @Author: HCS
 * @Date: 2023/10/23 10:05
 * @Version: 1.0
 */

@Slf4j
@RestController
@RequestMapping("/admin/taskDataSync")
public class TaskDataSyncController {

    @Resource
    private ElasticsearchServiceImpl elasticsearchService;

    @Resource
    private TaskDataSyncJob taskDataSyncJob;

    /**
     * 测试用，手动触发任务数据同步
     *
     * @return ResponseEntity<String>
     */
    @PostMapping("/run-task")
    public ResponseEntity<String> runTask() {
        try {
            taskDataSyncJob.execute(null);
            return ResponseEntity.ok("任务执行成功");
        } catch (Exception e) {
            return ResponseEntity.status(500).body("任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试用，手动触发es查询
     *
     * @return ResponseEntity<String>
     */
    @PostMapping("/searh-task")
    public ResponseEntity<JSONObject> searchTask() {
        try {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String todayStr = today.format(formatter);
            // 按天查询任务发送站点汇总数
            String sourQuery = "{\n" +
                    "    \"size\": 0,\n" +
                    "    \"aggs\": {\n" +
                    "        \"by_station\": {\n" +
                    "            \"terms\": {\n" +
                    "                \"field\": \"sourStationId\",\n" +
                    "                \"size\": 10000\n" +
                    "            },\n" +
                    "            \"aggs\": {\n" +
                    "                \"by_day\": {\n" +
                    "                    \"date_histogram\": {\n" +
                    "                        \"field\": \"createTime\",\n" +
                    "                        \"calendar_interval\": \"day\",\n" +
                    "                        \"format\": \"yyyy-MM-dd\",\n" +
                    "                        \"time_zone\": \"+08:00\"\n" +
                    "                    },\n" +
                    "                    \"aggs\": {\n" +
                    "                        \"total_tasks\": {\n" +
                    "                            \"value_count\": {\n" +
                    "                                \"field\": \"id\"\n" +
                    "                            }\n" +
                    "                        },\n" +
                    "                        \"canceled_tasks\": {\n" +
                    "                            \"filter\": {\n" +
                    "                                \"term\": {\n" +
                    "                                    \"status\": 4\n" +
                    "                                }\n" +
                    "                            },\n" +
                    "                            \"aggs\": {\n" +
                    "                                \"count\": {\n" +
                    "                                    \"value_count\": {\n" +
                    "                                        \"field\": \"id\"\n" +
                    "                                    }\n" +
                    "                                }\n" +
                    "                            }\n" +
                    "                        },\n" +
                    "                        \"finished_tasks\": {\n" +
                    "                            \"filter\": {\n" +
                    "                                \"term\": {\n" +
                    "                                    \"status\": 3\n" +
                    "                                }\n" +
                    "                            },\n" +
                    "                            \"aggs\": {\n" +
                    "                                \"count\": {\n" +
                    "                                    \"value_count\": {\n" +
                    "                                        \"field\": \"id\"\n" +
                    "                                    }\n" +
                    "                                }\n" +
                    "                            }\n" +
                    "                        }\n" +
                    "                    }\n" +
                    "                }\n" +
                    "            }\n" +
                    "        }\n" +
                    "    }\n" +
                    "}";

            // 按天查询任务接收站点汇总数
            String destQuery = "{\n" +
                    "    \"size\": 0,\n" +
                    "    \"aggs\": {\n" +
                    "        \"by_station\": {\n" +
                    "            \"terms\": {\n" +
                    "                \"field\": \"destStationId\",\n" +
                    "                \"size\": 10000\n" +
                    "            },\n" +
                    "            \"aggs\": {\n" +
                    "                \"by_day\": {\n" +
                    "                    \"date_histogram\": {\n" +
                    "                        \"field\": \"createTime\",\n" +
                    "                        \"calendar_interval\": \"day\",\n" +
                    "                        \"format\": \"yyyy-MM-dd\",\n" +
                    "                        \"time_zone\": \"+08:00\"\n" +
                    "                    },\n" +
                    "                    \"aggs\": {\n" +
                    "                        \"received_tasks\": {\n" +
                    "                            \"value_count\": {\n" +
                    "                                \"field\": \"id\"\n" +
                    "                            }\n" +
                    "                        },\n" +
                    "                        \"finished_tasks\": {\n" +
                    "                            \"filter\": {\n" +
                    "                                \"term\": {\n" +
                    "                                    \"status\": 3\n" +
                    "                                }\n" +
                    "                            },\n" +
                    "                            \"aggs\": {\n" +
                    "                                \"count\": {\n" +
                    "                                    \"value_count\": {\n" +
                    "                                        \"field\": \"id\"\n" +
                    "                                    }\n" +
                    "                                }\n" +
                    "                            }\n" +
                    "                        }\n" +
                    "                    }\n" +
                    "                }\n" +
                    "            }\n" +
                    "        }\n" +
                    "    }\n" +
                    "}";
            JSONObject jsonObject = JSON.parseObject(sourQuery);
            var result = elasticsearchService.searchData("bcss_task-" + todayStr, "", jsonObject);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询任务数据失败", e);
        }
        return ResponseEntity.status(500).body(null);
    }
}
