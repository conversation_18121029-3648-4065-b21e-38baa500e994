package com.slhc.hcms.module.bcss.controller.admin;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.slhc.hcms.module.bcss.entity.BcssDeviceSignal;
import com.slhc.hcms.module.bcss.entity.BcssPlc;
import com.slhc.hcms.module.bcss.service.IBcssDeviceSignalService;
import com.slhc.hcms.module.bcss.service.IBcssPlcService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import com.slhc.hcms.module.bcss.entity.BcssDevice;
import com.slhc.hcms.module.bcss.service.IBcssDeviceService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.util.oConvertUtils;
/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-21
 * @Version: V1.0
 */
@Tag(name="设备表")
@RestController
@RequestMapping("/admin/bcssDevice")
@Slf4j
public class BcssDeviceController extends JeecgController<BcssDevice, IBcssDeviceService> {
	@Autowired
	private IBcssDeviceService bcssDeviceService;
	@Autowired
	private IBcssDeviceSignalService bcssDeviceSignalService;
	@Autowired
	private IBcssPlcService bcssPlcService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bcssDevice
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "设备表-分页列表查询")
	@Operation(summary="设备表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BcssDevice>> queryPageList(BcssDevice bcssDevice,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


		
		// 保存需要模糊查询的字段值
		String deviceName = bcssDevice.getName();
		String stationId = bcssDevice.getStationId();
		
		// 清空这些字段，避免框架自动生成精确匹配条件
		bcssDevice.setName(null);
		bcssDevice.setStationId(null);
		
		// 使用清空后的对象生成基础查询条件
		QueryWrapper<BcssDevice> queryWrapper = QueryGenerator.initQueryWrapper(bcssDevice, req.getParameterMap());
		
		// 手动添加模糊查询条件
		if (oConvertUtils.isNotEmpty(deviceName)) {
			queryWrapper.like("name", deviceName);

		}
		if (oConvertUtils.isNotEmpty(stationId)) {
			queryWrapper.like("station_id", stationId);

		}
		
        log.info("生成的QueryWrapper SQL: {}", queryWrapper.getTargetSql());
		Page<BcssDevice> page = new Page<BcssDevice>(pageNo, pageSize);
		IPage<BcssDevice> pageList = bcssDeviceService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bcssDevice
	 * @return
	 */
	@AutoLog(value = "设备表-添加")
	@Operation(summary="设备表-添加")
	@RequiresPermissions("admin:bcss_device:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BcssDevice bcssDevice) {
		// 获取传入值的plc的id
		String devicePlcId = bcssDevice.getPlcId();

		// 1. 后端非空校验
		if (devicePlcId == null || devicePlcId.trim().isEmpty()) {
			return Result.error("所属PLC的ID不能为空！");
		}

		// 2. 构建精确的查询条件，同时检查ID和逻辑删除标志
		QueryWrapper<BcssPlc> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("id", devicePlcId)
				.eq("del_flag", 0); // 核心：确保PLC是有效状态

		// 3. 使用 getOne 来执行查询
		BcssPlc plc = bcssPlcService.getOne(queryWrapper);

		// 4. 判断查询结果
		if(plc == null) {
			// 此处错误包含了两种可能：1. ID根本不存在 2. ID存在但已被逻辑删除
			return Result.ok("关联的PLC不存在或已被删除，请检查PLC设备ID是否正确！");
		}

		// 5. 设置创建人信息和租户ID
		org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
		if (sysUser != null) {
			bcssDevice.setCreateBy(sysUser.getUsername());
			bcssDevice.setUpdateBy(sysUser.getUsername());
			bcssDevice.setTenantId(sysUser.getRelTenantIds()); // 设置租户ID
		}

		// 6. 设置默认值
		if (bcssDevice.getStatus() == null) {
			bcssDevice.setStatus(1); // 默认状态：1-正常
		}
		if (bcssDevice.getIsActive() == null) {
			bcssDevice.setIsActive(1); // 默认启用：1-启用
		}

		// 7. 确认关联数据有效后，才执行保存操作
		bcssDeviceService.save(bcssDevice);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bcssDevice
	 * @return
	 */
	@AutoLog(value = "设备表-编辑")
	@Operation(summary="设备表-编辑")
	@RequiresPermissions("admin:bcss_device:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BcssDevice bcssDevice) {
		bcssDeviceService.updateById(bcssDevice);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备表-通过id删除")
	@Operation(summary="设备表-通过id删除")
	@RequiresPermissions("admin:bcss_device:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bcssDeviceService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备表-批量删除")
	@Operation(summary="设备表-批量删除")
	@RequiresPermissions("admin:bcss_device:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bcssDeviceService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备表-通过id查询")
	@Operation(summary="设备表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BcssDevice> queryById(@RequestParam(name="id",required=true) String id) {
		BcssDevice bcssDevice = bcssDeviceService.getById(id);
		if(bcssDevice==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bcssDevice);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bcssDevice
    */
    @RequiresPermissions("admin:bcss_device:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BcssDevice bcssDevice) {
        return super.exportXls(request, bcssDevice, BcssDevice.class, "设备表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:bcss_device:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return bcssDeviceService.importExcelWithValidation(request, response);
    }

    /**
     * 下载设备数据模板
     */
    @AutoLog(value = "设备表-下载模板")
    @Operation(summary="下载设备数据模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 模板文件路径
            String templatePath = System.getProperty("user.dir") + File.separator + "data" + File.separator + "ImportTemplate" + File.separator + "设备数据模板.xlsx";
            File templateFile = new File(templatePath);
            
            if (!templateFile.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("设备数据模板.xlsx", "UTF-8"));
            
            // 读取文件并写入响应流
            try (FileInputStream fis = new FileInputStream(templateFile);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
        } catch (Exception e) {
            log.error("下载设备数据模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 启用设备
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备表-启用设备")
    @Operation(summary="设备表-启用设备")
    @RequiresPermissions("admin:bcss_device:edit")
    @PatchMapping(value = "/enable/{id}")
    public Result<String> enableDevice(@PathVariable(name="id",required=true) String id) {
        BcssDevice device = bcssDeviceService.getById(id);
        if(device == null) {
            return Result.error("设备不存在！");
        }
        device.setIsActive(1); // 1-启用
        boolean success = bcssDeviceService.updateById(device);
        if(!success) {
            return Result.error("启用设备失败！");
        }
        return Result.OK("启用设备成功！");
    }

    /**
     * 停用设备
     *
     * @param id
     * @return
     */
    @AutoLog(value = "设备表-停用设备")
    @Operation(summary="设备表-停用设备")
    @RequiresPermissions("admin:bcss_device:edit")
    @PatchMapping(value = "/disable/{id}")
    public Result<String> disableDevice(@PathVariable(name="id",required=true) String id) {
        BcssDevice device = bcssDeviceService.getById(id);
        if(device == null) {
            return Result.error("设备不存在！");
        }
        device.setIsActive(0); // 0-未启用
        boolean success = bcssDeviceService.updateById(device);
        if(!success) {
            return Result.error("停用设备失败！");
        }
        return Result.OK("停用设备成功！");
    }

    /**
     * 获取设备信号列表
     *
     * @param id 设备ID
     * @return
     */
    @AutoLog(value = "设备表-获取设备信号列表")
    @Operation(summary="设备表-获取设备信号列表")
    @RequiresPermissions("admin:bcss_device:edit")
    @GetMapping(value = "/{id}/signals")
    public Result<?> getDeviceSignals(@PathVariable(name="id",required=true) String id) {
        BcssDevice device = bcssDeviceService.getById(id);
        if(device == null) {
            return Result.error("设备不存在！");
        }
        // 根据设备ID查询该设备下的所有信号列表（这才是正确的业务逻辑）
//        QueryWrapper<BcssDeviceSignal> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("device_id", id); // 注意：这里用的是传入的设备ID，不是device.getId()
//        queryWrapper.eq("del_flag", 0); // 只查询未删除的记录
//        queryWrapper.orderByAsc("create_time"); // 按创建时间排序
        List<BcssDeviceSignal> signals = bcssDeviceSignalService.queryBySingleId(id);
        return Result.OK(signals);

    }

    /**
     * 检查设备名称是否重复
     *
     * @param name 设备名称
     * @param id 设备ID（编辑时传入，新增时为空）
     * @return
     */
    @Operation(summary="设备表-检查设备名称是否重复")
    @GetMapping(value = "/checkDeviceName")
    public Result<Boolean> checkDeviceName(@RequestParam(name="name",required=true) String name,
                                          @RequestParam(name="id",required=false) String id) {
        QueryWrapper<BcssDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        queryWrapper.eq("del_flag", 0); // 只检查未删除的记录
        
        // 如果是编辑操作，排除当前记录
        if(id != null && !id.trim().isEmpty()) {
            queryWrapper.ne("id", id);
        }
        
        long count = bcssDeviceService.count(queryWrapper);
        boolean isAvailable = count == 0;
        
        return Result.OK(isAvailable); // 返回true表示名称可用，false表示重复
    }

}
