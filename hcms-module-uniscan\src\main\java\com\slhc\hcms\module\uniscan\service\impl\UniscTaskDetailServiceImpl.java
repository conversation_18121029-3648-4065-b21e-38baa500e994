package com.slhc.hcms.module.uniscan.service.impl;
import com.slhc.hcms.module.uniscan.entity.UniscTaskDetail;
import com.slhc.hcms.module.uniscan.mapper.UniscTaskDetailMapper;
import com.slhc.hcms.module.uniscan.service.IUniscTaskDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: UniScan任务明细表
 * @Author: jeecg-boot
 * @Date:   2025-09-22
 * @Version: V1.0
 */
@Service
public class UniscTaskDetailServiceImpl extends ServiceImpl<UniscTaskDetailMapper, UniscTaskDetail> implements IUniscTaskDetailService {
	
	@Autowired
	private UniscTaskDetailMapper uniscTaskDetailMapper;
	
	@Override
	public List<UniscTaskDetail> selectByMainId(String mainId) {
		return uniscTaskDetailMapper.selectByMainId(mainId);
	}
}
