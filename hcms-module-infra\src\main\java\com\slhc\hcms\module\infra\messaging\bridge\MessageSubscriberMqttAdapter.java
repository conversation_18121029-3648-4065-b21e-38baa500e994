package com.slhc.hcms.module.infra.messaging.bridge;

import com.slhc.hcms.module.infra.messaging.core.MessageBrokerType;
import com.slhc.hcms.module.infra.messaging.core.MessageContext;
import com.slhc.hcms.module.infra.messaging.core.MessageProcessor;
import com.slhc.hcms.module.infra.messaging.core.MessageRegistry;
import com.slhc.hcms.module.infra.service.mqtt.IMqttService;
import com.slhc.hcms.module.infra.service.mqtt.MqttEventListener;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * MessageSubscriber到MQTT的桥接适配器
 * 负责将@MessageSubscriber注解的处理器注册为MQTT事件监听器
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
@Slf4j
public class MessageSubscriberMqttAdapter implements ApplicationListener<ApplicationReadyEvent> {

    // 存储处理器与监听器的映射关系
    private final Map<String, MqttEventListenerBridge> listenerBridges = new HashMap<>();
    @Autowired
    private MessageRegistry messageRegistry;
    @Autowired
    private IMqttService mqttService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("初始化MessageSubscriber到MQTT的桥接适配器");
        registerMqttSubscribers();
        log.info("MessageSubscriber到MQTT的桥接适配器初始化完成");
    }

    /**
     * 注册所有MQTT订阅者
     */
    private void registerMqttSubscribers() {
        log.info("开始注册MQTT订阅者...");

        List<MessageRegistry.RegisteredHandler> handlers = messageRegistry.getAllHandlers();
        log.info("找到{}个已注册的消息处理器", handlers.size());

        int mqttHandlerCount = 0;
        for (MessageRegistry.RegisteredHandler handler : handlers)
            if (handler.getBrokerType() == MessageBrokerType.MQTT && handler.isEnabled()) try {
                registerMqttSubscriber(handler);
                mqttHandlerCount++;
            } catch (Exception e) {
                log.error("注册MQTT订阅者失败: {}", handler.getProcessor().getName(), e);
            }

        log.info("成功注册{}个MQTT订阅者", mqttHandlerCount);
    }

    /**
     * 注册单个MQTT订阅者
     */
    private void registerMqttSubscriber(MessageRegistry.RegisteredHandler registeredHandler) {
        MessageProcessor<?> processor = registeredHandler.getProcessor();
        String topic = registeredHandler.getTopic();
        String handlerKey = generateHandlerKey(registeredHandler);

        log.info("注册MQTT订阅者 - 处理器: {}, 主题: {}", processor.getName(), topic);

        // 创建桥接监听器
        MqttEventListenerBridge bridge = new MqttEventListenerBridge(
                processor,
                topic,
                registeredHandler.getAnnotation().concurrency()
        );

        // 注册为MQTT事件监听器
        mqttService.registerEventListener(bridge);

        // 订阅MQTT主题
        mqttService.subscribe(bridge);

        // 保存映射关系
        listenerBridges.put(handlerKey, bridge);

        log.info("MQTT订阅者注册成功 - 处理器: {}, 主题: {}", processor.getName(), topic);
    }

    /**
     * 生成处理器键
     */
    private String generateHandlerKey(MessageRegistry.RegisteredHandler handler) {
        return String.format("%s:%s:%s",
                handler.getTopic(),
                handler.getBrokerType(),
                handler.getProcessor().getName());
    }

    /**
     * MQTT事件监听器桥接类
     * 将MQTT消息转换为MessageProcessor格式并调用处理器
     */
    private static class MqttEventListenerBridge implements MqttEventListener {

        private final MessageProcessor<?> processor;
        private final String topic;
        private final int qos;

        public MqttEventListenerBridge(MessageProcessor<?> processor, String topic, int qos) {
            this.processor = processor;
            this.topic = topic;
            this.qos = qos;
        }

        @Override
        public void messageArrived(String topic, MqttMessage message) throws Exception {
            String payload = new String(message.getPayload());

            log.debug("MQTT消息到达 - 主题: {}, 处理器: {}, 消息长度: {}",
                    topic, processor.getName(), payload.length());

            try {
                // 创建消息上下文
                MessageContext context = createMessageContext(topic, message);

                // 验证处理器是否支持该主题
                if (!processor.supports(topic)) {
                    log.debug("处理器不支持该主题 - 处理器: {}, 主题: {}", processor.getName(), topic);
                    return;
                }

                // 转换消息类型
                Object convertedMessage = convertMessage(payload, processor);

                // 调用处理器
//                processor.process(convertedMessage, context);
                @SuppressWarnings("unchecked")
                MessageProcessor<Object> typedProcessor = (MessageProcessor<Object>) processor;
                typedProcessor.process(convertedMessage, context);

                log.debug("消息处理成功 - 处理器: {}, 主题: {}", processor.getName(), topic);

            } catch (Exception e) {
                log.error("消息处理失败 - 处理器: {}, 主题: {}, 错误: {}",
                        processor.getName(), topic, e.getMessage(), e);
                throw e;
            }
        }

        @Override
        public String getTopic() {
            return topic;
        }

        @Override
        public String getListenerName() {
            return String.format("MqttBridge_%s", processor.getName());
        }

        @Override
        public int getQos() {
            return qos;
        }

        @Override
        public void onListenerRegistered() {
            log.info("MQTT桥接监听器已注册 - 处理器: {}, 主题: {}", processor.getName(), topic);
        }

        @Override
        public void onListenerUnregistered() {
            log.info("MQTT桥接监听器已注销 - 处理器: {}, 主题: {}", processor.getName(), topic);
        }

        /**
         * 创建消息上下文
         */
        private MessageContext createMessageContext(String topic, MqttMessage mqttMessage) {
            MessageContext context = new MessageContext();
            context.setTopic(topic);
            context.setMessageId(UUID.randomUUID().toString());
            context.setBrokerType(MessageBrokerType.MQTT);
            context.setTimestamp(LocalDateTime.now());
            context.setHeaders(Map.of(
                    "qos", String.valueOf(mqttMessage.getQos()),
                    "retained", String.valueOf(mqttMessage.isRetained()),
                    "duplicate", String.valueOf(mqttMessage.isDuplicate())
            ));
            return context;
        }

        /**
         * 转换消息类型
         */
        private Object convertMessage(String payload, MessageProcessor<?> processor) throws Exception {
            Class<?> messageType = processor.getMessageType();

            // 如果处理器支持convertMessage方法，使用它
            try {
                if (processor instanceof com.slhc.hcms.module.infra.messaging.core.MessageProcessor) {
                    @SuppressWarnings("unchecked")
                    com.slhc.hcms.module.infra.messaging.core.MessageProcessor<String> stringProcessor =
                            (com.slhc.hcms.module.infra.messaging.core.MessageProcessor<String>) processor;
                    return stringProcessor.convertMessage(payload);
                }
            } catch (UnsupportedOperationException e) {
                // 如果convertMessage不支持，继续下面的逻辑
            }

            // 根据消息类型进行转换
            if (messageType == String.class) return payload;
            else // 对于其他类型，尝试使用JSON反序列化
                if (messageType == byte[].class) return payload.getBytes();
                else throw new UnsupportedOperationException(
                        String.format("消息类型转换未实现 - 目标类型: %s", messageType.getName()));
        }
    }
}
