package com.slhc.hcms.module.bcss.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.slhc.hcms.module.bcss.common.EchartsTimeSeriesBuilder;
import com.slhc.hcms.module.bcss.common.QueryFileLoader;
import com.slhc.hcms.module.bcss.service.IBcssTaskService;
import com.slhc.hcms.module.bcss.vo.BcssReportVo;
import com.slhc.hcms.module.bcss.vo.EchartsTimeSeriesVo;
import com.slhc.hcms.module.infra.es.service.IElasticsearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 数据报表
 * @Author: HCM
 * @Date: 2025-07-21
 */
@RestController
@RequestMapping("/admin/bcssReport")
@Slf4j
@Tag(name = "箱式数据报表")
public class BcssReportController {
    public static final String BCSS_TASK_INDEX_PREFIX = "bcss_task-";
    private static final String STATION_FIELD_SOURCE = "sourStationId";
    private static final String STATION_FIELD_DEST = "destStationId";
    @Resource
    private IBcssTaskService bcssTaskService;
    @Resource
    private IElasticsearchService elasticsearchService;

    /**
     * 根据日期构建索引范围
     *
     * @param startDate: 开始时间
     * @param endDate:   结束时间
     */
    private String buildIndexPattern(String startDate, String endDate) {
        // 同一天直接检索当前索引
        if (Objects.equals(startDate, endDate)) {
            return BCSS_TASK_INDEX_PREFIX + startDate;
        }

        // 同一年
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        if (!start.getMonth().equals(end.getMonth())) {
            // 跨月情况，使用通配符匹配整月
            return BCSS_TASK_INDEX_PREFIX + start.format(DateTimeFormatter.ofPattern("yyyy")) + "-*";
        }

        // 同一个月内的不同日期
        int startDay = start.getDayOfMonth();
        int endDay = end.getDayOfMonth();

        if (startDay / 10 == endDay / 10) {
            // 同一个十天段，如 15-18
            int decade = startDay / 10;
            String dayRange = decade == 0 ?
                    "[" + startDay + "-" + endDay + "]" :
                    (decade == 1 ?
                            "1[" + (startDay % 10) + "-" + (endDay % 10) + "]" :
                            "2[" + (startDay % 10) + "-" + (endDay % 10) + "]");
            return BCSS_TASK_INDEX_PREFIX + start.format(DateTimeFormatter.ofPattern("yyyy-MM")) + "-" + dayRange;
        }

        // 默认按整月查询
        return BCSS_TASK_INDEX_PREFIX + start.format(DateTimeFormatter.ofPattern("yyyy-MM")) + "-*";
    }

    /**
     * 构建任务每日数据
     *
     * @param data: es查询结果
     */
    private List<BcssReportVo> initReportData(JSONObject data) {

        List<BcssReportVo> reportList = new ArrayList<>();
        // 提取聚合数据
        JSONObject aggregations = data.getJSONObject("aggregations");
        if (aggregations == null) {
            return reportList;
        }
        JSONObject byStation = aggregations.getJSONObject("by_station");
        List<JSONObject> stationBuckets = byStation.getJSONArray("buckets").toJavaList(JSONObject.class);

        for (JSONObject stationBucket : stationBuckets) {
            String stationId = stationBucket.getString("key");

            JSONObject byDay = stationBucket.getJSONObject("by_day");
            List<JSONObject> dayBuckets = byDay.getJSONArray("buckets").toJavaList(JSONObject.class);

            for (JSONObject dayBucket : dayBuckets) {
                String date = dayBucket.getString("key_as_string");

                Integer totalTasks = dayBucket.getJSONObject("total_tasks").getInteger("value");
                Integer finishedTasks = dayBucket.getJSONObject("finished_tasks").getJSONObject("count").getInteger("value");
                Integer canceledTasks = dayBucket.getJSONObject("canceled_tasks").getJSONObject("count").getInteger("value");

                BcssReportVo vo = new BcssReportVo();
                vo.setStationId(stationId);
                vo.setDate(date);
                vo.setTaskQty(totalTasks);
                vo.setFinishTaskQty(finishedTasks);
                vo.setCancelTaskQty(canceledTasks);

                reportList.add(vo);
            }
        }
        return reportList;
    }


    /**
     * 合并起始站点和目标站点数据
     *
     * @param sourList 起始站点数据
     * @param destList 目标站点数据
     * @return 合并后的数据列表
     */
    private List<BcssReportVo> combineReportData(List<BcssReportVo> sourList, List<BcssReportVo> destList) {
        // 合并起始站点和目标站点数据
        for (BcssReportVo sour : sourList) {
            for (BcssReportVo dest : destList) {
                if (sour.getStationId().equals(dest.getStationId()) && sour.getDate().equals(dest.getDate())) {
                    sour.setReceiveTaskQty(dest.getTaskQty());
                    sour.setReceiveFinishTaskQty(dest.getFinishTaskQty());
                }
            }
        }
        return sourList;
    }

//    @GetMapping("/getMessage")
//    public String getMessage(@RequestParam(name = "name") String name) {
//        String msg = "测试";
//        log.info(" 微服务被调用：{} ", msg);
//        return msg;
//    }

    /**
     * 参数校验
     *
     * @param stationId 站点ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 校验结果
     */
    private Result<Void> validateParams(String stationId, String startDate, String endDate) {
        // 非空校验
        if (StringUtils.isBlank(stationId) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return Result.error("参数不能为空");
        }

        // 日期格式校验（假设格式为 yyyy-MM-dd）
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        try {
//            LocalDate.parse(startDate, formatter);
//            LocalDate.parse(endDate, formatter);
//        } catch (DateTimeParseException e) {
//            return Result.error("日期格式不正确，请使用 yyyy-MM-dd 格式");
//        }

        // 校验通过
        return Result.OK();
    }

    /**
     * 查询每日任务数据汇总
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    @AutoLog(value = "任务表-数据报表每日")
    @GetMapping(value = "/daily-task")
    @Operation(summary = "查询每日任务数据汇总")
    public Result<EchartsTimeSeriesVo> dailyTask(@RequestParam("stationId") String stationId,
                                                 @RequestParam("startDate") String startDate,
                                                 @RequestParam("endDate") String endDate) {
        try {
            Result<Void> validateResult = validateParams(stationId, startDate, endDate);
            if (!validateResult.isSuccess()) {
                // 使用泛型方法创建错误结果，避免强制类型转换
                return Result.error(validateResult.getMessage());
            }

            // 读取统一的查询文件
            String jsonContent = QueryFileLoader.readQueryFromFile("es-queries/dailyQuery.json");
            JSONObject queries = JSON.parseObject(jsonContent);

            // 构造起始站点查询
            JSONObject sourQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_SOURCE, stationId);
            var sourData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", sourQuery);
            var sourResult = initReportData(sourData);

            // 构造目标站点查询
            JSONObject destQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_DEST, stationId);
            var destData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", destQuery);
            var destResult = initReportData(destData);

            // 合并结果并构建图表数据
            var result = combineReportData(sourResult, destResult);
            EchartsTimeSeriesVo hourlyData = EchartsTimeSeriesBuilder.buildHourSeries(result);

            return Result.OK(hourlyData);

        } catch (Exception e) {
            log.error("查询每日任务数据失败", e);
            return Result.error("系统错误，请稍后再试");
        }
    }

    /**
     * 查询每月任务数据汇总
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    @AutoLog(value = "任务表-数据报表每月")
    @GetMapping(value = "/monthly-task")
    @Operation(summary = "任务表-数据报表每月")
    public Result<EchartsTimeSeriesVo> monthlyTask(@RequestParam("stationId") String stationId,
                                                   @RequestParam("startDate") String startDate,
                                                   @RequestParam("endDate") String endDate) {
        try {
            Result<Void> validateResult = validateParams(stationId, startDate, endDate);
            if (!validateResult.isSuccess()) {
                // 使用泛型方法创建错误结果，避免强制类型转换
                return Result.error(validateResult.getMessage());
            }

            // 读取统一的查询文件
            String jsonContent = QueryFileLoader.readQueryFromFile("es-queries/monthlyQuery.json");
            JSONObject queries = JSON.parseObject(jsonContent);

            // 构造起始站点查询
            JSONObject sourQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_SOURCE, stationId);
            var sourData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", sourQuery);
            var sourResult = initReportData(sourData);

            // 构造目标站点查询
            JSONObject destQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_DEST, stationId);
            var destData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", destQuery);
            var destResult = initReportData(destData);

            // 合并结果并构建图表数据
            var result = combineReportData(sourResult, destResult);
            EchartsTimeSeriesVo data = EchartsTimeSeriesBuilder.buildMonthSeries(result);

            return Result.OK(data);

        } catch (Exception e) {
            log.error("查询每月任务数据失败", e);
            return Result.error("系统错误，请稍后再试");
        }
    }

    /**
     * 查询年度任务数据汇总
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    @AutoLog(value = "任务表-数据报表年度")
    @GetMapping(value = "/yearly-task")
    @Operation(summary = "任务表-数据报表每月")
    public Result<EchartsTimeSeriesVo> yearlyTask(@RequestParam("stationId") String stationId,
                                                  @RequestParam("startDate") String startDate,
                                                  @RequestParam("endDate") String endDate) {
        try {
            Result<Void> validateResult = validateParams(stationId, startDate, endDate);
            if (!validateResult.isSuccess()) {
                // 使用泛型方法创建错误结果，避免强制类型转换
                return Result.error(validateResult.getMessage());
            }

            // 读取统一的查询文件
            String jsonContent = QueryFileLoader.readQueryFromFile("es-queries/yearlyQuery.json");
            JSONObject queries = JSON.parseObject(jsonContent);

            // 构造起始站点查询
            JSONObject sourQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_SOURCE, stationId);
            var sourData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", sourQuery);
            var sourResult = initReportData(sourData);

            // 构造目标站点查询
            JSONObject destQuery = buildStationQuery(queries.getJSONObject("sourQuery"), STATION_FIELD_DEST, stationId);
            var destData = elasticsearchService.searchData(buildIndexPattern(startDate, endDate), "", destQuery);
            var destResult = initReportData(destData);

            // 合并结果并构建图表数据
            var result = combineReportData(sourResult, destResult);
            EchartsTimeSeriesVo data = EchartsTimeSeriesBuilder.buildYearSeries(result);

            return Result.OK(data);

        } catch (Exception e) {
            log.error("查询年度任务数据失败", e);
            return Result.error("系统错误，请稍后再试");
        }
    }

    /**
     * 构建针对某个站点字段的查询对象
     *
     * @param baseQuery    基础查询模板
     * @param stationField 站点字段名（如 sourStationId 或 destStationId）
     * @param stationId    站点ID
     * @return 替换后的新查询对象
     */
    private JSONObject buildStationQuery(JSONObject baseQuery, String stationField, String stationId) {
        if (baseQuery == null || StringUtils.isBlank(stationField) || StringUtils.isBlank(stationId)) {
            throw new IllegalArgumentException("查询参数不能为空");
        }

        String queryStr = baseQuery.toJSONString()
                .replace("${stationId}", stationId)
                .replace("${stationField}", stationField);
        return JSON.parseObject(queryStr);
    }


}
