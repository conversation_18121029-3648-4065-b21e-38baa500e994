package com.slhc.hcms.module.bcss.messaging.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.slhc.hcms.module.bcss.dto.ReceiveMQTasKError;
import com.slhc.hcms.module.bcss.messaging.topics.TaskTopics;
import com.slhc.hcms.module.bcss.service.IBcssTaskService;
import com.slhc.hcms.module.infra.messaging.annotation.MessageSubscriber;
import com.slhc.hcms.module.infra.messaging.core.MessageBrokerType;
import com.slhc.hcms.module.infra.messaging.core.MessageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一任务消息处理器
 * 处理所有与任务相关的消息事件
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
@Slf4j
@MessageSubscriber(
        topic = "bcss/v1/slhc/rcs/createorder/+",  // 使用通配符订阅多个任务相关主题
        broker = MessageBrokerType.MQTT,
        description = "统一任务消息处理器",
        concurrency = 3,  // 并发消费者数量
        maxRetries = 3,
        retryInterval = 1000
)
public class TaskMessageHandler extends AbstractMessageHandler<String> {

    // 消息类型映射
    private final Map<String, TaskMessageType> messageTypeMap = new ConcurrentHashMap<>();
    @Autowired
    private IBcssTaskService taskService;
    @Autowired
    private ObjectMapper objectMapper;

    public TaskMessageHandler() {
        initializeMessageTypes();
    }

    /**
     * 初始化消息类型映射
     */
    private void initializeMessageTypes() {
        // Redis消息类型
        // TODO:按需增加

        // MQTT消息类型
        messageTypeMap.put(TaskTopics.TASK_ERROR, TaskMessageType.TASK_ERROR);
        messageTypeMap.put("bcss/v1/slhc/rcs/createorder/error", TaskMessageType.TASK_ERROR);
//        messageTypeMap.put("bcss/v1/slhc/rcs/createorder/success", TaskMessageType.TASK_CREATED);

        log.info("Initialized message type mappings: {}", messageTypeMap);
    }

    @Override
    protected boolean validate(String message, MessageContext context) {
        if (message == null || message.trim().isEmpty()) {
            log.warn("Empty message received, topic: {}", context.getTopic());
            return false;
        }

        // 验证消息格式是否为有效的JSON
        try {
            objectMapper.readTree(message);
            return true;
        } catch (Exception e) {
            log.error("Invalid message format, topic: {}, error: {}", context.getTopic(), e.getMessage());
            return false;
        }
    }

    /**
     * 统一处理所有任务相关消息
     */
    @Override
    protected void doProcess(String payload, MessageContext context) throws Exception {
        String topic = context.getTopic();
        log.info("Received message - Topic: {}, Payload: {}", topic, payload);
        log.info("Available message type mappings: {}", messageTypeMap);

        TaskMessageType messageType = messageTypeMap.get(topic);

        if (messageType == null) {
            log.warn("Unknown task message type for topic: {}, available types: {}", topic, messageTypeMap.keySet());
            // 尝试根据主题后缀判断消息类型
            if (topic.endsWith("/error")) {
                log.info("Detected error message by topic suffix, using TASK_ERROR handler");
                handleTaskError(payload, context);
                return;
            } else if (topic.endsWith("/success")) {
                log.info("Detected success message by topic suffix, using TASK_CREATED handler");
                handleTaskCreated(payload, context);
                return;
            }
            return;
        }

        log.info("Processing task message - Type: {}, Topic: {}", messageType, topic);

        switch (messageType) {
            case TASK_ERROR:
                handleTaskError(payload, context);
                break;
//            case TASK_CREATED:
//                handleTaskCreated(payload, context);
//                break;
            default:
                log.warn("Unhandled task message type: {}", messageType);
        }
    }

    /**
     * 处理任务错误事件
     */
    private void handleTaskError(String payload, MessageContext context) throws Exception {
        log.info("Handling task error event");

        ReceiveMQTasKError errorMessage = objectMapper.readValue(payload, ReceiveMQTasKError.class);

        try {
            // TODO: 业务逻辑处理
            log.info("Successfully processed task error for task: {}", errorMessage.getTaskId());

        } catch (Exception e) {
            log.error("Error processing task error for task: {}", errorMessage.getTaskId(), e);
            errorMessage.setErrorMessage(e.getMessage());
            throw e; // 重新抛出异常，触发重试机制
        }
    }

    /**
     * 处理任务创建事件
     */
    private void handleTaskCreated(String payload, MessageContext context) throws Exception {
        log.info("Handling task created event");

        try {
            // 解析任务创建消息
            Map<String, Object> taskData = objectMapper.readValue(payload, Map.class);
            log.info("Task created successfully: {}", taskData);

            // TODO: 业务逻辑处理
            // taskService.handleTaskCreated(taskData);

        } catch (Exception e) {
            log.error("Error processing task created event", e);
            throw e; // 重新抛出异常，触发重试机制
        }
    }


    @Override
    public Class<String> getMessageType() {
        return String.class;
    }

    @Override
    public String convertMessage(String payload) throws Exception {
        // 直接返回字符串payload，由具体的处理方法进行JSON解析
        return payload;
    }

    @Override
    public boolean supports(String topic) {
        return topic != null && (
                topic.startsWith("rcs:bcss:evt:task") ||
                        topic.startsWith("bcss/v1/task") ||
                        topic.startsWith("bcss/v1/slhc/rcs/")
        );
    }

    @Override
    public String getName() {
        return "TaskMessageHandler";
    }

    /**
     * 任务消息类型枚举
     */
    enum TaskMessageType {
        TASK_ERROR,
        // 按需拓展任务消息枚举
    }
}
