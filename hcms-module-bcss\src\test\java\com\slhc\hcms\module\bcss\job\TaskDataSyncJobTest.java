package com.slhc.hcms.module.bcss.job;

import com.slhc.hcms.module.bcss.entity.BcssTask;
import com.slhc.hcms.module.bcss.mapper.BcssTaskMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
//import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class TaskDataSyncTest {

    @MockBean
    private BcssTaskMapper bcssTaskMapper;

    @Resource
    private TaskDataSyncJob taskDataSyncJob;

    @MockBean
    private JobExecutionContext jobExecutionContext;

    @BeforeEach
    public void setUp() {
        // 设置 TaskDataSync 的 mapper（通过反射或 setter 注入）
        taskDataSyncJob = new TaskDataSyncJob();
        // 可通过反射设置 mapper 字段（或提供 setter）
    }

    @Test
    public void testExecute_ShouldCallSelectList() throws JobExecutionException {

        // Arrange
        List<BcssTask> mockTasks = Arrays.asList(
                new BcssTask(),
                new BcssTask()
        );

        // Arrange
        // 设置 mock 数据
        mockTasks.get(0).setId("1");
//        mockTasks.get(0).setTaskName("Task1");
        mockTasks.get(0).setStatus(1);
        mockTasks.get(0).setCreateTime(Date.from(
                LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));

        mockTasks.get(1).setId("2");
//        mockTasks.get(1).setsetTaskName("Task2");
        mockTasks.get(1).setStatus(1);
        mockTasks.get(1).setCreateTime(Date.from(
                LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));

        when(bcssTaskMapper.selectList(null)).thenReturn(mockTasks);

        // Act
        taskDataSyncJob.execute(jobExecutionContext);

        // Assert
        verify(bcssTaskMapper, times(1)).selectList(null);

        when(bcssTaskMapper.selectList(null)).thenReturn(mockTasks);

        // Act
        taskDataSyncJob.execute(jobExecutionContext);

        // Assert
        verify(bcssTaskMapper, times(1)).selectList(null);
    }

    @Test
    public void testExecute_WhenExceptionOccurs_ShouldThrowJobExecutionException() throws JobExecutionException {
        // Arrange
        when(bcssTaskMapper.selectList(null)).thenThrow(new RuntimeException("DB Error"));

        // Act & Assert
        assertThrows(JobExecutionException.class, () -> {
            taskDataSyncJob.execute(jobExecutionContext);
        });
    }
}
