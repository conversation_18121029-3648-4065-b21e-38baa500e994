package com.slhc.hcms.module.bcss.controller.admin;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.slhc.hcms.module.bcss.entity.BcssDeviceLog;
import com.slhc.hcms.module.bcss.service.IBcssDeviceLogService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
/**
 * @Description: 设备日志
 * @Author: jeecg-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Tag(name="设备日志表")
@RestController
@RequestMapping("/admin/bcssDeviceLog")
@Slf4j
public class BcssDeviceLogController extends JeecgController<BcssDeviceLog, IBcssDeviceLogService> {
	@Autowired
	private IBcssDeviceLogService bcssDeviceLogService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bcssDeviceLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "设备日志表-分页列表查询")
	@Operation(summary="设备日志表-分页列表查询")
	@PermissionData(pageComponent = "bcss/devicelog/index")//数据隔离
	@GetMapping(value = "/list")
	public Result<IPage<BcssDeviceLog>> queryPageList(BcssDeviceLog bcssDeviceLog,
	  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
	  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
	  HttpServletRequest req) {


		// 自定义查询规则
		Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
		// 自定义多选的查询规则为：LIKE_WITH_OR
		customeRuleMap.put("deviceType", QueryRuleEnum.EQ);
		customeRuleMap.put("type", QueryRuleEnum.EQ);
		customeRuleMap.put("status", QueryRuleEnum.EQ);
		QueryWrapper<BcssDeviceLog> queryWrapper = QueryGenerator.initQueryWrapper(bcssDeviceLog, req.getParameterMap(),customeRuleMap);
		Page<BcssDeviceLog> page = new Page<BcssDeviceLog>(pageNo, pageSize);
		IPage<BcssDeviceLog> pageList = bcssDeviceLogService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param bcssDeviceLog
	 * @return
	 */
	@AutoLog(value = "设备日志表-添加")
	@Operation(summary="设备日志表-添加")
	@RequiresPermissions("bcss:bcss_device_log:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BcssDeviceLog bcssDeviceLog) {
		bcssDeviceLogService.save(bcssDeviceLog);

		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param bcssDeviceLog
	 * @return
	 */
	@AutoLog(value = "设备日志表-编辑")
	@Operation(summary="设备日志表-编辑")
	@RequiresPermissions("bcss:bcss_device_log:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BcssDeviceLog bcssDeviceLog) {
		bcssDeviceLogService.updateById(bcssDeviceLog);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "设备日志表-通过id删除")
	@Operation(summary="设备日志表-通过id删除")
	@RequiresPermissions("bcss:bcss_device_log:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bcssDeviceLogService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "设备日志表-批量删除")
	@Operation(summary="设备日志表-批量删除")
	@RequiresPermissions("bcss:bcss_device_log:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bcssDeviceLogService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "设备日志表-通过id查询")
	@Operation(summary="设备日志表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BcssDeviceLog> queryById(@RequestParam(name="id",required=true) String id) {
		BcssDeviceLog bcssDeviceLog = bcssDeviceLogService.getById(id);
		if(bcssDeviceLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bcssDeviceLog);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param bcssDeviceLog
	 */
	@RequiresPermissions("bcss:bcss_device_log:exportXls")
	@RequestMapping(value = "/exportXls")
	@PermissionData(pageComponent = "bcss/devicelog/index")//数据隔离
	public ModelAndView exportXls(HttpServletRequest request, BcssDeviceLog bcssDeviceLog) {
		return super.exportXls(request, bcssDeviceLog, BcssDeviceLog.class, "设备日志表");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequiresPermissions("bcss:bcss_device_log:importExcel")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, BcssDeviceLog.class);
	}

}
