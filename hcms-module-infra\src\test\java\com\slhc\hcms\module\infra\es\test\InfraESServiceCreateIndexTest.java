//package com.slhc.hcms.modules.infra.es.test;
//
//import com.slhc.hcms.modules.infra.es.service.IInfraESService;
//import com.slhc.hcms.modules.infra.es.service.impl.InfraESServiceImpl;
//import org.junit.jupiter.api.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//
//import static org.junit.jupiter.api.Assertions.assertTrue;
//
//@SpringBootTest(classes = {
//        ElasticsearchTestConfig.class,
//        InfraESServiceImpl.class
//})
//@ActiveProfiles("test")
//@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
//class InfraESServiceCreateIndexTest {
//
//    @Resource
//    private IInfraESService esService;
//
//    private static final String TEST_INDEX = "test_index_" + System.currentTimeMillis();
//
//    @Test
//    @Order(1)
//    @DisplayName("测试索引创建")
//    void testCreateIndex() throws IOException {
//        // 确保索引不存在
//        if (esService.indexExists(TEST_INDEX)) {
//            esService.deleteIndex(TEST_INDEX);
//        }
//
//        // 执行测试 - 创建索引
//        boolean result = esService.createIndex(TEST_INDEX, 1, 0);
//
//        // 验证
//        assertTrue(result, "索引创建失败");
//        assertTrue(esService.indexExists(TEST_INDEX), "索引不存在");
//
//        // 打印索引信息
//        System.out.println("索引创建成功: " + TEST_INDEX);
//        System.out.println("索引信息: " + esService.getIndexInfo(TEST_INDEX));
//    }
//
//    @Test
//    @Order(2)
//    @DisplayName("测试索引删除")
//    void testDeleteIndex() throws IOException {
//        // 确保索引存在
//        if (!esService.indexExists(TEST_INDEX)) {
//            esService.createIndex(TEST_INDEX, 1, 0);
//        }
//
//        // 执行测试 - 删除索引
//        boolean result = esService.deleteIndex(TEST_INDEX);
//
//        // 验证
//        assertTrue(result, "索引删除失败");
//        assertTrue(!esService.indexExists(TEST_INDEX), "索引仍然存在");
//    }
//
//    @AfterAll
//    static void cleanUp(@Autowired IInfraESService service) throws IOException {
//        // 清理测试索引
//        if (service.indexExists(TEST_INDEX)) {
//            boolean deleted = service.deleteIndex(TEST_INDEX);
//            System.out.println("清理测试索引: " + TEST_INDEX + ", 结果: " + deleted);
//        }
//    }
//}