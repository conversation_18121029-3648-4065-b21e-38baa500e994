/* empty css                  */import{u as E,M as k}from"./Meta2DCanvas-9N-Tmw7R.js";import{u as D}from"./index-CD6ICDO9.js";import{d as I,r as p,e as R,q as j,a as u,f as t,g as _,h as s,v as x,t as w,u as a,w as f,E as F,i as N,j as y,F as B,k as M,s as $,l as z,m as b,n as O,p as U,o as c}from"./element-plus-D-V1KzVw.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./meta2d-DtcXQZZw.js";const J={class:"preview-container"},G={key:0,class:"toolbar-hover-area"},H={class:"toolbar-left"},L={class:"project-name"},A={class:"toolbar-right"},K={class:"preview-canvas"},Q={key:0,class:"data-panel"},W={class:"panel-header"},X={class:"panel-content"},Y=I({__name:"PublishedPreview",setup(Z){D();const r=E(),m=p(!1),i=p(!0);let l=null;const h=p(null),g=()=>{window.location.reload()},T=()=>{document.fullscreenElement?document.exitFullscreen():document.documentElement.requestFullscreen()},C=(d,e)=>{r.updateVariableData(d,e),console.log(`变量 ${d} 更新为:`,e)},P=()=>{l&&clearTimeout(l),l=window.setTimeout(()=>{i.value=!1},100)};return R(async()=>{const d=sessionStorage.getItem("publishedPreviewRefreshed")==="true";try{const e=await fetch("/meta2d.json");if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const n=await e.json();localStorage.setItem("meta2d",JSON.stringify(n)),console.log("meta2d数据已成功存储到localStorage"),d||(sessionStorage.setItem("publishedPreviewRefreshed","true"),setTimeout(()=>{window.location.reload()},100))}catch(e){console.error("读取meta2d.json文件失败:",e)}setTimeout(()=>{window.meta2d&&(window.meta2d.store.data.locked=1)},100),l=window.setTimeout(()=>{i.value=!1},1e3)}),j(()=>{l&&clearTimeout(l),sessionStorage.removeItem("publishedPreviewRefreshed")}),(d,e)=>{const n=y,S=F,V=U;return c(),u("div",J,[t("div",{class:"preview-toolbar-wrapper",onMouseenter:e[0]||(e[0]=o=>i.value=!0),onMouseleave:P},[i.value?_("",!0):(c(),u("div",G)),t("div",{class:x(["preview-toolbar",{"toolbar-visible":i.value}])},[t("div",H,[t("span",L,w(a(r).currentProject.name)+" - 发布预览模式",1)]),t("div",A,[s(S,null,{default:f(()=>[s(n,{icon:a(z),onClick:g},{default:f(()=>e[3]||(e[3]=[b("刷新",-1)])),_:1,__:[3]},8,["icon"]),s(n,{icon:a(O),onClick:T},{default:f(()=>e[4]||(e[4]=[b("全屏",-1)])),_:1,__:[4]},8,["icon"])]),_:1})])],2)],32),t("div",K,[s(k,{ref_key:"meta2dCanvasRef",ref:h,"read-only":!0},null,512)]),m.value?(c(),u("div",Q,[t("div",W,[e[5]||(e[5]=t("span",null,"数据模拟",-1)),s(n,{icon:a(N),size:"small",text:"",onClick:e[1]||(e[1]=o=>m.value=!1)},null,8,["icon"])]),t("div",X,[(c(!0),u(B,null,M(Object.keys(a(r).variableData),o=>(c(),u("div",{key:o,class:"variable-item"},[t("label",null,w(o)+":",1),s(V,{modelValue:a(r).variableData[o],"onUpdate:modelValue":v=>a(r).variableData[o]=v,size:"small",onInput:v=>C(o,v)},null,8,["modelValue","onUpdate:modelValue","onInput"])]))),128))])])):_("",!0),s(n,{class:"data-toggle-btn",icon:a($),circle:"",onClick:e[2]||(e[2]=o=>m.value=!m.value)},null,8,["icon"])])}}}),ne=q(Y,[["__scopeId","data-v-1b24d04d"]]);export{ne as default};
