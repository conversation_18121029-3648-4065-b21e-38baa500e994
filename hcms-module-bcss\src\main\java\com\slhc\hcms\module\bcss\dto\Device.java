package com.slhc.hcms.module.bcss.dto;

import lombok.Data;

// 设备类（对应list数组的元素）
@Data
public class Device {
    private String id;    // 如"aaa-1"
    private String name;  // 如"收箱轨道"

    // getter和setter
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}