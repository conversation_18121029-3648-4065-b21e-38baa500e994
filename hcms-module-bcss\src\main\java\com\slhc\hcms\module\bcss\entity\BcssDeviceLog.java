package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 设备日志
 * @Author: jeecg-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Data
@TableName("bcss_device_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="设备日志")
public class BcssDeviceLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
    /**删除状态*/
//    @Excel(name = "删除状态", width = 15, dicCode = "bcss_devicelog_del_status") //注释掉以不在excel内导出
    @Dict(dicCode = "bcss_devicelog_del_status")
    @Schema(description = "删除状态")
    @TableLogic
    private java.lang.Integer delFlag;
    /**设备名称*/
    @Excel(name = "设备名称", width = 15)
    @Schema(description = "设备名称")
    private java.lang.String deviceName;
    /**所属设备ID*/
    @Excel(name = "所属设备ID", width = 15)
    @Schema(description = "所属设备ID")
    private java.lang.String deviceId;
    /**设备类型*/
    @Excel(name = "设备类型", width = 15, dicCode = "device_type")
    @Dict(dicCode = "device_type")
    @Schema(description = "设备类型")
    private java.lang.Integer deviceType;
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private java.util.Date createTime;
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private java.util.Date updateTime;
    /**消息类型*/
    @Excel(name = "消息类型", width = 15, dicCode = "bcss_devicelog_msg_type")
    @Dict(dicCode = "bcss_devicelog_msg_type")
    @Schema(description = "消息类型")
    private java.lang.Integer type;
    /**消息状态*/
    @Excel(name = "消息状态", width = 15, dicCode = "bcss_devicelog_msg_sta")
    @Dict(dicCode = "bcss_devicelog_msg_sta")
    @Schema(description = "消息状态")
    private java.lang.Integer status;
    @Excel(name = "设备归属", width = 15, dicCode = "system_code")
    @Dict(dicCode = "system_code")
    @Schema(description = "设备归属")
    private java.lang.Integer systemCode;
    /**日志内容*/
    @Excel(name = "日志内容", width = 15)
    @Schema(description = "日志内容")
    private java.lang.String content;
    /**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
    /**租户ID*/
//    @Excel(name = "租户ID", width = 15) //注释掉以不在excel内导出
    @Schema(description = "租户ID")
    private java.lang.String tenantId;
    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private java.lang.String remark;
}
