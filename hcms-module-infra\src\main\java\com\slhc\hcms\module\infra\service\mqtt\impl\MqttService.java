package com.slhc.hcms.module.infra.service.mqtt.impl;

import com.slhc.hcms.module.infra.service.mqtt.*;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.UUID;

/**
 * 增强版MQTT服务实现
 * 提供完整的MQTT功能，包括连接管理、事件回调、消息队列等
 * 
 * @author: HCMS Team
 * @version: 2.0
 */
@Service
public class MqttService implements IMqttService {

    private static final Logger log = LoggerFactory.getLogger(MqttService.class);
    
    @Autowired
    private MqttClient mqttClient;
    
    @Autowired
    private MqttConnectionManager connectionManager;
    
    @Autowired
    private MqttEventManager eventManager;
    
    @Autowired
    private MqttMessageQueue messageQueue;
    
    /**
     * 构造函数
     */
    public MqttService() {
        log.info("初始化增强版MQTT服务");
    }
    
    @PostConstruct
    public void init() {
        log.info("启动MQTT服务");
        
        if (mqttClient == null) {
            log.error("MQTT客户端未初始化，服务启动失败");
            return;
        }
        
        // 启动连接管理器
        if (connectionManager != null) {
            connectionManager.connect();
        }
        
        log.info("MQTT服务启动完成");
    }
    
    @PreDestroy
    public void destroy() {
        log.info("销毁MQTT服务");
        
        // 清空事件监听器
        if (eventManager != null) {
            eventManager.clearAllListeners();
        }
        
        // 清空消息队列
        if (messageQueue != null) {
            messageQueue.clear();
        }
        
        log.info("MQTT服务销毁完成");
    }
    
    @Override
    public boolean publish(String topic, String payload) {
        return publish(topic, payload, 1, false);
    }
    
    @Override
    public boolean publish(String topic, String payload, int qos) {
        return publish(topic, payload, qos, false);
    }
    
    @Override
    public boolean publish(String topic, String payload, int qos, boolean retained) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("发布消息失败：主题不能为空");
            return false;
        }
        
        if (payload == null) {
            log.error("发布消息失败：消息内容不能为空");
            return false;
        }
        
        if (!connectionManager.isConnected()) {
            log.warn("MQTT未连接，消息将加入队列: {}", topic);
            return enqueueMessage(topic, payload, qos, retained);
        }
        
        try {
            MqttMessage message = new MqttMessage(payload.getBytes());
            message.setQos(qos);
            message.setRetained(retained);
            
            // 生成消息ID用于去重
            String messageId = generateMessageId(topic, payload, qos, retained);
            
            // 检查是否已发送过（去重）
            if (messageQueue.isMessageSent(messageId)) {
                log.debug("消息已发送过，跳过重复发送: {}", messageId);
                return true;
            }
            
            mqttClient.publish(topic, message);
            
            // 标记为已发送
            messageQueue.markMessageSent(messageId);
            
            log.info("消息发布成功 - 主题: {}, QoS: {}, 长度: {}, 保留: {}", 
                     topic, qos, payload.length(), retained);
            return true;
            
        } catch (MqttException e) {
            log.error("消息发布失败 - 主题: {}, 错误: {}", topic, e.getMessage(), e);
            
            // 如果发布失败，将消息加入队列
            return enqueueMessage(topic, payload, qos, retained);
        } catch (Exception e) {
            log.error("消息发布异常 - 主题: {}, 错误: {}", topic, e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public void subscribe(String topic) {
        subscribe(topic, 1);
    }
    
    @Override
    public void subscribe(String topic, int qos) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("订阅主题失败：主题不能为空");
            return;
        }
        
        if (!connectionManager.isConnected()) {
            log.warn("MQTT未连接，无法订阅主题: {}", topic);
            return;
        }
        
        try {
            mqttClient.subscribe(topic, qos);
            log.info("订阅主题成功: {} (QoS: {})", topic, qos);
        } catch (MqttException e) {
            log.error("订阅主题失败: {} - {}", topic, e.getMessage(), e);
        }
    }
    
    @Override
    public void subscribe(MqttEventListener listener) {
        if (listener == null) {
            log.error("订阅失败：监听器不能为空");
            return;
        }
        
        String topic = listener.getTopic();
        int qos = listener.getQos();
        
        log.info("使用监听器订阅主题: {} (QoS: {})", topic, qos);
        
        // 注册事件监听器
        registerEventListener(listener);
        
        // 执行订阅
        subscribe(topic, qos);
    }
    
    @Override
    public void unsubscribe(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("取消订阅失败：主题不能为空");
            return;
        }
        
        if (!connectionManager.isConnected()) {
            log.warn("MQTT未连接，无法取消订阅主题: {}", topic);
            return;
        }
        
        try {
            mqttClient.unsubscribe(topic);
            log.info("取消订阅主题成功: {}", topic);
        } catch (MqttException e) {
            log.error("取消订阅主题失败: {} - {}", topic, e.getMessage(), e);
        }
    }
    
    @Override
    public void registerEventListener(MqttEventListener listener) {
        if (listener == null) {
            log.error("注册事件监听器失败：监听器不能为空");
            return;
        }
        
        try {
            eventManager.registerListener(listener);
            log.info("事件监听器注册成功: {} -> {}", listener.getListenerName(), listener.getTopic());
        } catch (Exception e) {
            log.error("事件监听器注册失败: {}", listener.getListenerName(), e);
        }
    }
    
    @Override
    public void unregisterEventListener(MqttEventListener listener) {
        if (listener == null) {
            log.error("注销事件监听器失败：监听器不能为空");
            return;
        }
        
        try {
            eventManager.unregisterListener(listener);
            log.info("事件监听器注销成功: {}", listener.getListenerName());
        } catch (Exception e) {
            log.error("事件监听器注销失败: {}", listener.getListenerName(), e);
        }
    }
    
    @Override
    public boolean isConnected() {
        return connectionManager != null && connectionManager.isConnected();
    }
    
    @Override
    public Map<String, Object> getConnectionInfo() {
        if (connectionManager != null) {
            return connectionManager.getConnectionInfo();
        }
        
        return Map.of("connected", false, "error", "连接管理器未初始化");
    }
    
    @Override
    public Map<String, Object> getMessageQueueStats() {
        if (messageQueue != null) {
            return messageQueue.getQueueStats();
        }
        
        return Map.of("error", "消息队列未初始化");
    }
    
    @Override
    public void reconnect() {
        if (connectionManager != null) {
            connectionManager.reconnect();
        }
    }
    
    @Override
    public void disconnect() {
        if (connectionManager != null) {
            connectionManager.disconnect();
        }
    }
    
    @Override
    public void clearMessageQueue() {
        if (messageQueue != null) {
            messageQueue.clear();
            log.info("消息队列已清空");
        }
    }
    
    /**
     * 将消息加入队列
     */
    private boolean enqueueMessage(String topic, String payload, int qos, boolean retained) {
        if (messageQueue == null) {
            log.error("消息队列未初始化，无法加入队列");
            return false;
        }
        
        boolean success = messageQueue.enqueue(topic, payload, qos, retained);
        if (success) {
            log.info("消息已加入队列 - 主题: {}, 队列大小: {}", topic, messageQueue.getQueueSize());
        } else {
            log.error("消息加入队列失败 - 主题: {}", topic);
        }
        
        return success;
    }
    
    /**
     * 生成消息ID（用于去重）
     */
    private String generateMessageId(String topic, String payload, int qos, boolean retained) {
        return UUID.nameUUIDFromBytes((topic + payload + qos + retained).getBytes()).toString();
    }
}
