//package com.slhc.hcms.moduels.infra.es.service.impl;
//
//
//import org.elasticsearch.client.indices.GetIndexResponse;
//
//import com.slhc.hcms.moduels.infra.es.service.IInfraESService;
//import com.slhc.hcms.moduels.infra.es.config.ElasticsearchTestConfig;
//import org.junit.jupiter.api.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import java.io.IOException;
//import static org.junit.jupiter.api.Assertions.*;
//
//@SpringBootTest(classes = {
//        ElasticsearchTestConfig.class,
//        InfraESServiceImpl.class
//})
//@ActiveProfiles("test")
//@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
//class InfraESServiceCreateIndexTest {
//
//    @Autowired
//    private IInfraESService esService;
//
//    private static final String TEST_INDEX = "test_index_" + System.currentTimeMillis();
//
//    @Test
//    @Order(1)
//    @DisplayName("创建新索引-成功")
//    void testCreateIndexSuccess() throws IOException {
//        // 执行创建
//        boolean result = esService.createIndex(TEST_INDEX, 1, 0);
//
//        // 验证结果
//        assertTrue(result);
//        assertTrue(esService.indexExists(TEST_INDEX));
//    }
//
//    @Test
//    @Order(2)
//    @DisplayName("验证索引配置-分片和副本数")
//    void testIndexSettings() throws IOException {
//        GetIndexResponse response = esService.getIndexInfo(TEST_INDEX);
//
//        // 验证分片数
//        assertEquals(1,
//                Integer.parseInt(response.getSettings().get(TEST_INDEX)
//                        .get("index.number_of_shards")));
//
//        // 验证副本数
//        assertEquals(0,
//                Integer.parseInt(response.getSettings().get(TEST_INDEX)
//                        .get("index.number_of_replicas")));
//    }
//
//    @Test
//    @Order(3)
//    @DisplayName("重复创建索引-失败")
//    void testCreateDuplicateIndex() throws IOException {
//        // 期望抛出异常
//        assertThrows(org.elasticsearch.client.ResponseException.class, () -> {
//            esService.createIndex(TEST_INDEX, 1, 0);
//        });
//    }
//
//    @AfterAll
//    static void cleanUp(@Autowired IInfraESService service) throws IOException {
//        // 测试完成后清理索引
//        if (service.indexExists(TEST_INDEX)) {
//            service.deleteIndex(TEST_INDEX);
//        }
//    }
//}