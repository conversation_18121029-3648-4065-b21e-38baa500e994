//package com.slhc.hcms.modules.infra.es.config;
//
//import co.elastic.clients.elasticsearch.ElasticsearchClient;
//import co.elastic.clients.json.jackson.JacksonJsonpMapper;
//import co.elastic.clients.transport.rest_client.RestClientTransport;
//import org.apache.http.HttpHost;
//import org.apache.http.auth.AuthScope;
//import org.apache.http.auth.UsernamePasswordCredentials;
//import org.apache.http.client.CredentialsProvider;
//import org.apache.http.impl.client.BasicCredentialsProvider;
//import org.apache.http.ssl.SSLContextBuilder;
//import org.apache.http.ssl.SSLContexts;
//import org.elasticsearch.client.RestClient;
//import org.elasticsearch.client.RestClientBuilder;
//import org.jeecg.common.util.SpringContextHolder;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.DependsOn;
//import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.PreDestroy;
//import javax.net.ssl.SSLContext;
//import java.io.IOException;
//import java.io.InputStream;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.security.KeyManagementException;
//import java.security.KeyStore;
//import java.security.KeyStoreException;
//import java.security.NoSuchAlgorithmException;
//import java.security.cert.CertificateException;
//import java.util.Arrays;
//import java.util.Objects;
//
///**
// * Elasticsearch 9.x 配置类
// */
//@Configuration
//public class ElasticsearchConfig {
//
//    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchConfig.class);
//
//    @Value("${elasticsearch.cluster-nodes:127.0.0.1:9200}")
//    private String[] clusterNodes;
//
//    @Value("${elasticsearch.connect-timeout:5000}")
//    private Integer connectTimeout;
//
//    @Value("${elasticsearch.socket-timeout:10000}")
//    private Integer socketTimeout;
//
//    @Value("${elasticsearch.username:#{null}}")
//    private String username;
//
//    @Value("${elasticsearch.password:#{null}}")
//    private String password;
//
//    @Value("${elasticsearch.keystore.path:#{null}}")
//    private String keystorePath;
//
//    @Value("${elasticsearch.keystore.password:#{null}}")
//    private String keystorePassword;
//
//    private ElasticsearchClient elasticsearchClient;
//    private RestClient restClient;
//
//    /**
//     * 配置底层RestClient
//     */
//    @Bean
//    public RestClient restClient() {
//        HttpHost[] hosts = Arrays.stream(clusterNodes)
//                .map(this::buildHttpHost)
//                .filter(Objects::nonNull)
//                .toArray(HttpHost[]::new);
//
//        RestClientBuilder builder = RestClient.builder(hosts)
//                .setRequestConfigCallback(requestConfigBuilder -> {
//                    requestConfigBuilder.setConnectTimeout(connectTimeout);
//                    requestConfigBuilder.setSocketTimeout(socketTimeout);
//                    return requestConfigBuilder;
//                });
//
//        // 配置认证和SSL
//        configureSecurity(builder);
//
//        this.restClient = builder.build();
//        return restClient;
//    }
//
//    /**
//     * 配置安全连接
//     */
//    private void configureSecurity(RestClientBuilder builder) {
//        // 基本认证
//        if (username != null && password != null) {
//            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
//            credentialsProvider.setCredentials(AuthScope.ANY,
//                    new UsernamePasswordCredentials(username, password));
//
//            builder.setHttpClientConfigCallback(httpClientBuilder -> {
//                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
//
//                // SSL配置
//                if (keystorePath != null) {
//                    try {
//                        SSLContext sslContext = buildSSLContext();
//                        httpClientBuilder.setSSLContext(sslContext);
//                    } catch (Exception e) {
//                        logger.error("Failed to configure SSL context", e);
//                    }
//                }
//                return httpClientBuilder;
//            });
//        }
//    }
//
//    /**
//     * 构建SSL上下文
//     */
//    private SSLContext buildSSLContext() throws KeyStoreException, IOException, NoSuchAlgorithmException,
//            CertificateException, KeyManagementException {
//        Path trustStorePath = Paths.get(keystorePath);
//        KeyStore truststore = KeyStore.getInstance("pkcs12");
//        try (InputStream is = Files.newInputStream(trustStorePath)) {
//            truststore.load(is, keystorePassword.toCharArray());
//        }
//        SSLContextBuilder sslBuilder = SSLContexts.custom()
//                .loadTrustMaterial(truststore, null);
//        return sslBuilder.build();
//    }
//
//    /**
//     * 配置ElasticsearchClient
//     */
//    @Bean
//    @DependsOn("restClient")
//    public ElasticsearchClient elasticsearchClient() {
//        RestClientTransport transport = new RestClientTransport(
//                restClient,
//                new JacksonJsonpMapper()
//        );
//
//        this.elasticsearchClient = new ElasticsearchClient(transport);
//        checkClusterHealth();
//        return elasticsearchClient;
//    }
//
//    /**
//     * 配置ElasticsearchTemplate (Spring Data Elasticsearch 4.x+)
//     */
//    @Bean
//    @DependsOn("elasticsearchClient")
//    public ElasticsearchTemplate elasticsearchTemplate() {
//        return null;
//        //return new ElasticsearchTemplate(elasticsearchClient);
//    }
//
//    /**
//     * 解析配置的节点地址
//     */
//    private HttpHost buildHttpHost(String node) {
//        try {
//            String[] parts = StringUtils.split(node, ":");
//            if (parts.length == 2) {
//                return new HttpHost(parts[0], Integer.parseInt(parts[1]),
//                        parts[0].startsWith("https") ? "https" : "http");
//            }
//            return new HttpHost(parts[0], 9200,
//                    parts[0].startsWith("https") ? "https" : "http");
//        } catch (Exception e) {
//            throw new IllegalArgumentException("Invalid ES nodes configuration: " + node, e);
//        }
//    }
//
//    /**
//     * 集群健康检查
//     */
//    private void checkClusterHealth() {
//        try {
//            String clusterName = elasticsearchClient.info()
//                    .clusterName();
//            String version = elasticsearchClient.info()
//                    .version()
//                    .number();
//            logger.info("Connected to Elasticsearch cluster [{}], version {}", clusterName, version);
//        } catch (IOException e) {
//            logger.error("Failed to connect Elasticsearch cluster", e);
//            throw new IllegalStateException("Failed to connect Elasticsearch cluster", e);
//        }
//    }
//
//    /**
//     * 应用关闭时释放资源
//     */
//    @PreDestroy
//    public void destroy() {
//        if (restClient != null) {
//            try {
//                restClient.close();
//                logger.info("Elasticsearch client closed successfully");
//            } catch (Exception e) {
//                logger.error("Error closing Elasticsearch client", e);
//            }
//        }
//    }
//
//    /**
//     * 提供静态工具类访问
//     */
//    public static ElasticsearchClient getClient() {
//        return SpringContextHolder.getBean(ElasticsearchClient.class);
//    }
//}