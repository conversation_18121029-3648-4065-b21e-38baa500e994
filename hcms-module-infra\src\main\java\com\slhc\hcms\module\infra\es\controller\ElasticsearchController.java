package com.slhc.hcms.module.infra.es.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.slhc.hcms.module.infra.es.service.impl.ElasticsearchServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Tag(name = "es操作")
@Slf4j
@RestController
@RequestMapping("/es")
public class ElasticsearchController {

    @Resource
    private ElasticsearchServiceImpl elasticsearchService;

    /**
     * 创建es索引（注：已存在返回true）
     *
     * @param indexName 索引名称
     */
    @PostMapping("/create-index")
    public ResponseEntity<Boolean> createIndex(@RequestParam String indexName, @RequestBody JSONObject data) {

        boolean result = elasticsearchService.createIndex(indexName, data);
        return ResponseEntity.ok(result);
    }


    @PostMapping("/save-data")
    public ResponseEntity<Boolean> saveData(
            @RequestParam String indexName,
            @RequestParam String typeName,
            @RequestParam String dataId,
            @RequestBody JSONObject data) {
        boolean result = elasticsearchService.saveData(indexName, typeName, dataId, data);
        return ResponseEntity.ok(result);
    }

    /**
     * 处理es POST请求的搜索功能接口
     * 该接口允许用户根据索引名、类型名和查询条件来搜索数据
     *
     * @param indexName 索引名称，标识要搜索的数据集
     * @param typeName  类型名称，用于细化搜索范围，默认为空(es:7.x版本已弃用)
     * @param query     查询条件，用户定义的搜索条件，格式为JSONObject
     */
    @PostMapping("/search")
    public ResponseEntity<JSONObject> searchData(
            @RequestParam String indexName,
            @RequestParam(required = false, defaultValue = "") String typeName,
            @RequestBody JSONObject query) {


        JSONObject result = elasticsearchService.searchData(indexName, typeName, query);

        return ResponseEntity.ok(result);
    }

    /**
     * 按年份聚合 qty 字段并返回结果
     */
    @PostMapping("/search/yearly-qty")
    public ResponseEntity<JSONArray> searchYearlyQty(
            @RequestParam String indexName,
            @RequestParam(required = false, defaultValue = "") String typeName) {

        // 构建 date_histogram 聚合，按年份分组，并格式化输出为 yyyy
        JSONObject dateHistogram = new JSONObject();
        dateHistogram.put("field", "created_at");
        dateHistogram.put("calendar_interval", "year");
        dateHistogram.put("format", "yyyy"); // 只显示年份

        // 构建子聚合：{ "sum": { "field": "qty" } }
        JSONObject sumQty = new JSONObject();
        {
            JSONObject sumContent = new JSONObject();
            sumContent.put("field", "qty");
            sumQty.put("sum", sumContent);
        }

        // 主聚合：按年分组 + qty 求和
        JSONObject groupByYear = new JSONObject();
        groupByYear.put("date_histogram", dateHistogram);
        {
            JSONObject aggs = new JSONObject();
            aggs.put("sum_qty", sumQty);
            groupByYear.put("aggs", aggs);
        }

        // 构建完整聚合结构
        JSONObject aggregations = new JSONObject();
        aggregations.put("group_by_year", groupByYear);

        // 构建整个查询体
        JSONObject searchQuery = new JSONObject();
        searchQuery.put("size", 0); // 不返回原始文档
        searchQuery.put("aggregations", aggregations);

        // 执行 ES 查询
        JSONObject result = elasticsearchService.searchData(indexName, typeName, searchQuery);

        // 解析聚合结果
        JSONArray responseArray = new JSONArray();

        if (result.containsKey("aggregations")) {
            JSONObject aggregationsResult = result.getJSONObject("aggregations");
            JSONArray buckets = aggregationsResult.getJSONObject("group_by_year").getJSONArray("buckets");

            for (int i = 0; i < buckets.size(); i++) {
                JSONObject bucket = buckets.getJSONObject(i);
                String year = bucket.getString("key_as_string");
                double totalQty = bucket.getJSONObject("sum_qty").getDoubleValue("value");

                JSONObject item = new JSONObject();
                item.put("year", year);
                item.put("totalQty", totalQty);

                responseArray.add(item);
            }
        }

        return ResponseEntity.ok(responseArray);
    }

}

