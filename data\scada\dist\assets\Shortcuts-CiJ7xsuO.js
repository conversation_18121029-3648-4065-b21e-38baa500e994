import{_ as d}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{a,b,o as v}from"./element-plus-D-V1KzVw.js";const r={},s={class:"shortcuts-container"};function l(e,t){return v(),a("div",s,t[0]||(t[0]=[b('<div class="shortcuts-section" data-v-d9b38284><h3 data-v-d9b38284>快捷键描述</h3><table class="shortcuts-table" data-v-d9b38284><tbody data-v-d9b38284><tr data-v-d9b38284><td data-v-d9b38284>空格 + 鼠标拖拽</td><td data-v-d9b38284>移动画布</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + 滚轮</td><td data-v-d9b38284>缩放画布</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + 点击 Pen</td><td data-v-d9b38284>多选</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + A</td><td data-v-d9b38284>全选</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + C</td><td data-v-d9b38284>复制</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + X</td><td data-v-d9b38284>剪切</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + V</td><td data-v-d9b38284>粘贴，+shift原位粘贴</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + Z</td><td data-v-d9b38284>撤销</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + Y （或 Shift + Z）</td><td data-v-d9b38284>重做</td></tr><tr data-v-d9b38284><td data-v-d9b38284>V</td><td data-v-d9b38284>钢笔绘画</td></tr><tr data-v-d9b38284><td data-v-d9b38284>B</td><td data-v-d9b38284>铅笔绘画</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Enter</td><td data-v-d9b38284>画线中，完成画线；选中线，闭合连线</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Escape</td><td data-v-d9b38284>完成画线或退出其他模式</td></tr><tr data-v-d9b38284><td data-v-d9b38284>方向键</td><td data-v-d9b38284>移动选中图形 （Ctrl - 移动 10 像素； Shift - 移动 5 像素；默认移动 1 像素）</td></tr><tr data-v-d9b38284><td data-v-d9b38284>H</td><td data-v-d9b38284>选中连线锚点时，添加手柄</td></tr><tr data-v-d9b38284><td data-v-d9b38284>D</td><td data-v-d9b38284>选中连线锚点时，删除手柄</td></tr><tr data-v-d9b38284><td data-v-d9b38284>A</td><td data-v-d9b38284>选中 Pen 时，添加锚点</td></tr><tr data-v-d9b38284><td data-v-d9b38284>G</td><td data-v-d9b38284>鼠标移入节点锚点后，按下 G 进入移动瞄点状态，鼠标按下拖拽（或者方向键）都可以移动</td></tr><tr data-v-d9b38284><td data-v-d9b38284>F</td><td data-v-d9b38284>框选图元跟随最后一个选中图元</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Delete （或 Backspace）</td><td data-v-d9b38284>删除选中节点</td></tr><tr data-v-d9b38284><td data-v-d9b38284>Ctrl + 拖拽节点四个角（resize）</td><td data-v-d9b38284>等比 resize 节点</td></tr><tr data-v-d9b38284><td data-v-d9b38284>线连接线时，按下 ctrl或alt</td><td data-v-d9b38284>合并成一条连线，丢失另外一条的属性和样式</td></tr><tr data-v-d9b38284><td data-v-d9b38284>选中线，移入线锚点，按下 S</td><td data-v-d9b38284>切割连线，成为两条连线</td></tr><tr data-v-d9b38284><td data-v-d9b38284>关闭自动锚点条件下，Ctrl+Shift+Alt+点击</td><td data-v-d9b38284>添加锚点并连线</td></tr><tr data-v-d9b38284><td data-v-d9b38284>L</td><td data-v-d9b38284>moveConnectedLine：false时，长按允许拖动连线</td></tr><tr data-v-d9b38284><td data-v-d9b38284>[</td><td data-v-d9b38284>下一层</td></tr><tr data-v-d9b38284><td data-v-d9b38284>]</td><td data-v-d9b38284>上一层</td></tr><tr data-v-d9b38284><td data-v-d9b38284>{</td><td data-v-d9b38284>置底</td></tr><tr data-v-d9b38284><td data-v-d9b38284>}</td><td data-v-d9b38284>置顶</td></tr><tr data-v-d9b38284><td data-v-d9b38284>ctrl+shift+G</td><td data-v-d9b38284>取消选中图元组合</td></tr><tr data-v-d9b38284><td data-v-d9b38284>ctrl+G</td><td data-v-d9b38284>组合选中图元</td></tr></tbody></table></div><div class="shortcuts-section" data-v-d9b38284><h3 data-v-d9b38284>Shift 快捷键状态描述</h3><table class="shortcuts-table" data-v-d9b38284><tbody data-v-d9b38284><tr data-v-d9b38284><td data-v-d9b38284>编辑选中 Node</td><td data-v-d9b38284>显示水平垂直大小控制点</td></tr><tr data-v-d9b38284><td data-v-d9b38284>选中连线锚点</td><td data-v-d9b38284>切换手柄类型</td></tr></tbody></table></div><div class="shortcuts-section" data-v-d9b38284><ul class="shortcuts-notes" data-v-d9b38284><li data-v-d9b38284>●画线过程中，点击alt键，可用于切换连线类型；当连线类型为直线时，按住shift控制直线水平，按住ctrl控制直线垂直,按住ctrl+shift，移动鼠标，直线会定位到距离最新的规则角度。</li><li data-v-d9b38284>●拖动图元过程中，长按shift可以控制图元水平移动，长按ctrl可以控制图元垂直移动。</li><li data-v-d9b38284>●拖动画布过程中，长按shift可以控制画布水平移动，长按ctrl可以控制画布垂直移动。</li></ul></div>',3)]))}const i=d(r,[["render",l],["__scopeId","data-v-d9b38284"]]);export{i as default};
