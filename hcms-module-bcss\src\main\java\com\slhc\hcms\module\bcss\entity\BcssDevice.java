package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-21
 * @Version: V1.0
 */
@Data
@TableName("bcss_device")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="设备表")
public class BcssDevice implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**删除状态(0-正常,1-已删除)*/
	//@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @Schema(description = "删除状态(0-正常,1-已删除)")
    @TableLogic
    private Integer delFlag;
	/**设备名称*/
	@Excel(name = "设备名称", width = 15)
    @Schema(description = "设备名称")
    private String name;
	/**plc的id*/
	@Excel(name = "PLC名称", width = 15, dictTable = "bcss_plc", dicCode = "id", dicText = "name")
    @Schema(description = "plc的id")
    @Dict(dictTable = "bcss_plc", dicCode = "id", dicText = "name")
    private String plcId;
	/**设备位置*/
	@Excel(name = "设备位置", width = 15)
    @Schema(description = "设备位置")
    private String location;
	/**设备类型*/
	@Excel(name = "设备类型", width = 15, dicCode = "device_type")
	@Dict(dicCode = "device_type")
    @Schema(description = "设备类型")
    private Integer deviceType;
	/**默认站点编号*/
	@Excel(name = "默认站点编号", width = 15)
    @Schema(description = "默认站点编号")
    private String stationId;
	/**固件Id*/
	@Excel(name = "固件Id", width = 15)
    @Schema(description = "固件Id")
    private String deviceCode;
	/**状态: 1.正常 2.异常*/
	@Excel(name = "状态", width = 15, dicCode = "device_operation_status")
	@Dict(dicCode = "device_operation_status")
    @Schema(description = "状态")
    private Integer status;
	/**是否启用: 1.启用 0.未启用*/
	@Excel(name = "是否启用", width = 15, dicCode = "device_active_status")
	@Dict(dicCode = "device_active_status")
    @Schema(description = "是否启用")
    @TableField("is_active")
    private Integer isActive;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户Id*/
	//@Excel(name = "租户Id", width = 15)
    @Schema(description = "租户Id")
    private String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
}
