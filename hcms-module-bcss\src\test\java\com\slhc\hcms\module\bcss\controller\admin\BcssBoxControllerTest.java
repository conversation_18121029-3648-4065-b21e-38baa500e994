package com.slhc.hcms.module.bcss.controller.admin;

import com.slhc.hcms.module.bcss.entity.BcssBox;
import com.slhc.hcms.module.bcss.service.IBcssBoxService;
import org.jeecg.common.api.vo.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = DemoApplication.class)
@SpringBootTest
public class BcssBoxControllerTest {

    @Mock
    private IBcssBoxService bcssBoxService;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private BcssBoxController bcssBoxController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void queryById_数据库真实访问() {
        // 先插入一条测试数据
        BcssBox box = new BcssBox();
        box.setId("test-id-1");
        bcssBoxService.save(box);

        // 调用 controller 查询
        Result<BcssBox> result = bcssBoxController.queryById("1");

        assertNotNull(result);
        assertEquals(Result.OK().getCode(), result.getCode());
        assertNotNull(result.getResult());
        assertEquals("test-id-1", result.getResult().getId());
    }


    @Test
    void queryPageList() {
    }

    @Test
    void queryById() {

        String id = "1";
        BcssBox box = new BcssBox();
        when(bcssBoxService.getById(id)).thenReturn(box);

        Result<BcssBox> result = bcssBoxController.queryById(id);

        assertNotNull(result);
        assertEquals(Result.OK().getCode(), result.getCode());
        assertEquals(box, result.getResult());
    }

    @Test
    void testQueryPageList() {
    }
}