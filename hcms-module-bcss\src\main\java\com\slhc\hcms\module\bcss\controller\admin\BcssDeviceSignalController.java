package com.slhc.hcms.module.bcss.controller.admin;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import com.slhc.hcms.module.bcss.entity.BcssDeviceSignal;
import com.slhc.hcms.module.bcss.service.IBcssDeviceSignalService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 箱式设备信号表
 * @Author: jeecg-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
@Tag(name="箱式设备信号表")
@RestController
@RequestMapping("/admin/bcssDeviceSignal")
@Slf4j
public class BcssDeviceSignalController extends JeecgController<BcssDeviceSignal, IBcssDeviceSignalService> {
	@Autowired
	private IBcssDeviceSignalService bcssDeviceSignalService;
	
	/**
	 * 分页列表查询
	 *
	 * @param bcssDeviceSignal
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "箱式设备信号表-分页列表查询")
	@Operation(summary="箱式设备信号表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<BcssDeviceSignal>> queryPageList(BcssDeviceSignal bcssDeviceSignal,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

		// 保存需要模糊查询的字段值
		String signalName = bcssDeviceSignal.getSignalName();
		String signalCode = bcssDeviceSignal.getSignalCode();
		Integer status = bcssDeviceSignal.getStatus();
		
		// 清空这些字段，避免框架自动生成精确匹配条件
		bcssDeviceSignal.setSignalName(null);
		bcssDeviceSignal.setSignalCode(null);
		bcssDeviceSignal.setStatus(null);
		
		// 使用清空后的对象生成基础查询条件
		QueryWrapper<BcssDeviceSignal> queryWrapper = QueryGenerator.initQueryWrapper(bcssDeviceSignal, req.getParameterMap());

		// 手动添加模糊查询条件
		if (oConvertUtils.isNotEmpty(signalName)) {
			queryWrapper.like("signal_name", signalName);
		}
		if (oConvertUtils.isNotEmpty(signalCode)) {
			queryWrapper.like("signal_code", signalCode);
		}
		// 手动添加状态查询条件（如果有指定状态）
		// 注释掉状态过滤，显示所有状态的数据
		// if (status != null) {
		//	queryWrapper.eq("status", status);
		// }

        // 手动处理deviceId，增加查询条件
        String deviceId = req.getParameter("deviceId");
        if (oConvertUtils.isNotEmpty(deviceId)) {
            queryWrapper.eq("device_id", deviceId);
        }
        
        // 处理设备名称查询 - 需要关联设备表
        String deviceName = req.getParameter("deviceName");
        if (oConvertUtils.isNotEmpty(deviceName)) {
            // 通过设备ID关联查询，deviceName实际传递的是设备ID
            queryWrapper.eq("device_id", deviceName );
        }
        
        // 添加排序规则：按设备ID、PLC ID、信号名称排序，确保数据有序显示
        queryWrapper.orderByAsc("device_id", "plc_id", "signal_name");
        
		Page<BcssDeviceSignal> page = new Page<BcssDeviceSignal>(pageNo, pageSize);
		IPage<BcssDeviceSignal> pageList = bcssDeviceSignalService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param bcssDeviceSignal
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-添加")
	@Operation(summary="箱式设备信号表-添加")
	@RequiresPermissions("admin:bcss_device_signal:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody BcssDeviceSignal bcssDeviceSignal) {
		bcssDeviceSignalService.save(bcssDeviceSignal);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param bcssDeviceSignal
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-编辑")
	@Operation(summary="箱式设备信号表-编辑")
	@RequiresPermissions("admin:bcss_device_signal:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody BcssDeviceSignal bcssDeviceSignal) {
		bcssDeviceSignalService.updateById(bcssDeviceSignal);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-通过id删除")
	@Operation(summary="箱式设备信号表-通过id删除")
	@RequiresPermissions("admin:bcss_device_signal:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		bcssDeviceSignalService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 启用设备信号
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-启用设备信号")
	@Operation(summary="箱式设备信号表-启用设备信号")
	@RequiresPermissions("admin:bcss_device_signal:edit")
	@PatchMapping(value = "/enable/{id}")
	public Result<String> enableDeviceSignal(@PathVariable(name="id",required=true) String id) {
		BcssDeviceSignal deviceSignal = bcssDeviceSignalService.getById(id);
		if(deviceSignal == null) {
			return Result.error("设备信号不存在！");
		}
		deviceSignal.setStatus(1); // 1-启用
		boolean success = bcssDeviceSignalService.updateById(deviceSignal);
		if(!success) {
			return Result.error("启用设备信号失败！");
		}
		return Result.OK("启用设备信号成功！");
	}

	/**
	 * 停用设备信号
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-停用设备信号")
	@Operation(summary="箱式设备信号表-停用设备信号")
	@RequiresPermissions("admin:bcss_device_signal:edit")
	@PatchMapping(value = "/disable/{id}")
	public Result<String> disableDeviceSignal(@PathVariable(name="id",required=true) String id) {
		BcssDeviceSignal deviceSignal = bcssDeviceSignalService.getById(id);
		if(deviceSignal == null) {
			return Result.error("设备信号不存在！");
		}
		deviceSignal.setStatus(2); // 2-未启用
		boolean success = bcssDeviceSignalService.updateById(deviceSignal);
		if(!success) {
			return Result.error("停用设备信号失败！");
		}
		return Result.OK("停用设备信号成功！");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "箱式设备信号表-批量删除")
	@Operation(summary="箱式设备信号表-批量删除")
	@RequiresPermissions("admin:bcss_device_signal:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.bcssDeviceSignalService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "箱式设备信号表-通过id查询")
	@Operation(summary="箱式设备信号表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<BcssDeviceSignal> queryById(@RequestParam(name="id",required=true) String id) {
		BcssDeviceSignal bcssDeviceSignal = bcssDeviceSignalService.getById(id);
		if(bcssDeviceSignal==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(bcssDeviceSignal);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param bcssDeviceSignal
    */
    @RequiresPermissions("admin:bcss_device_signal:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BcssDeviceSignal bcssDeviceSignal) {
        // 生成带时间戳的文件名
        String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmssSSS").format(new java.util.Date());
        String fileName = "箱式设备信号表";
        return super.exportXls(request, bcssDeviceSignal, BcssDeviceSignal.class, fileName);
    }

    /**
      * 通过excel导入数据（带完整业务校验）
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("admin:bcss_device_signal:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            // 使用带完整业务校验的导入方法
            return bcssDeviceSignalService.importExcelWithValidation(file);
        }
        return Result.error("上传文件为空");
    }

    /**
     * 验证信号业务编码是否重复（同一设备下的同一PLC唯一）
     *
     * @param signalCode 信号业务编码
     * @param plcId PLC ID
     * @param deviceId 设备ID（必填）
     * @param id 当前记录ID（编辑时传入，新增时为空）
     * @return
     */
    @Operation(summary="验证信号业务编码是否重复（同一设备下的同一PLC唯一）")
    @GetMapping(value = "/checkSignalCode")
    public Result<Boolean> checkSignalCode(@RequestParam(name="signalCode") String signalCode,
                                         @RequestParam(name="plcId") String plcId,
                                         @RequestParam(name="deviceId") String deviceId,
                                         @RequestParam(name="id", required=false) String id) {
        if (oConvertUtils.isEmpty(signalCode) || oConvertUtils.isEmpty(plcId) || oConvertUtils.isEmpty(deviceId)) {
            return Result.OK(true); // 空值不验证
        }
        
        QueryWrapper<BcssDeviceSignal> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("signal_code", signalCode);
        queryWrapper.eq("plc_id", plcId);
        queryWrapper.eq("device_id", deviceId); // 同一设备下的同一PLC验证唯一性
        
        // 如果是编辑操作，排除当前记录
        if (oConvertUtils.isNotEmpty(id)) {
            queryWrapper.ne("id", id);
        }
        
        // 使用 list() 方法避免 selectOne() 的多结果错误
        List<BcssDeviceSignal> existingSignals = bcssDeviceSignalService.list(queryWrapper);
        boolean isUnique = (existingSignals == null || existingSignals.isEmpty());
        
        return Result.OK(isUnique);
    }

    /**
     * 下载设备信号模板
     */
    @AutoLog(value = "箱式设备信号表-下载模板")
    @Operation(summary="下载设备信号模板")
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 模板文件路径
            String templatePath = System.getProperty("user.dir") + File.separator + "data" + File.separator + "ImportTemplate" + File.separator + "设备信号模板.xlsx";
            File templateFile = new File(templatePath);
            
            if (!templateFile.exists()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=device-signal-template.xlsx");
            response.setContentLength((int) templateFile.length());
            
            // 读取文件并写入响应流
            try (FileInputStream fis = new FileInputStream(templateFile);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
        } catch (IOException e) {
            log.error("下载模板文件失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

}
