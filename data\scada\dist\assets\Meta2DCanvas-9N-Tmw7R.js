const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./meta2d-DtcXQZZw.js","./element-plus-D-V1KzVw.js"])))=>i.map(i=>d[i]);
import{d as I,_}from"./index-CD6ICDO9.js";import{r as d,A as T,B as j,d as D,e as M,q as N,a as C,o as H}from"./element-plus-D-V1KzVw.js";import{M as J,g as x,E as k}from"./meta2d-DtcXQZZw.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";const O="meta2d",S={save(s){localStorage.setItem(O,JSON.stringify(s))},load(){try{const s=localStorage.getItem(O);return s?JSON.parse(s):null}catch{return null}},clear(){localStorage.removeItem(O)}},K=I("editor",()=>{const s=d(null),c=d((()=>{try{const o=localStorage.getItem("currentProject");if(o){const p=JSON.parse(o);if(p.id&&p.name)return p}}catch(o){console.error("恢复项目数据失败:",o)}return{id:"default",name:"新建项目",variables:{},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}})()),i=d([]),b=d(!0),g=T({scale:1,translateX:0,translateY:0,width:800,height:600}),l=T({isActive:!1,selectedTemplate:null}),u=d({}),w=(o,p=!1)=>{p?i.value.includes(o)?i.value=i.value.filter(E=>E!==o):i.value.push(o):i.value=[o]},f=()=>{i.value=[]},h=(o,p)=>{u.value[o]=p},y=()=>{try{const o=S.load()||null;return JSON.stringify(o,null,2)}catch(o){return console.error("导出项目失败:",o),JSON.stringify(c.value,null,2)}},e=o=>{try{const p=JSON.parse(o);return r(p)?(S.save(p),!0):(console.error("项目数据格式不正确"),!1)}catch(p){return console.error("导入项目失败:",p),!1}},r=o=>!(!o||typeof o!="object"),a=o=>{l.isActive=!0,l.selectedTemplate=o},n=()=>{l.isActive=!1,l.selectedTemplate=null},v=()=>{try{localStorage.setItem("currentProject",JSON.stringify(c.value))}catch(o){console.error("保存项目数据失败:",o)}};return j(c,()=>{v()},{deep:!0,immediate:!0}),{meta2d:s,currentProject:c,selectedNodes:i,isEditMode:b,canvasState:g,addMode:l,variableData:u,selectNode:w,clearSelection:f,updateVariableData:h,exportProject:y,importProject:e,validateProjectData:r,enterAddMode:a,exitAddMode:n,saveProject:()=>{c.value.updatedAt=new Date().toISOString(),v()},clearStorage:()=>{try{localStorage.removeItem("currentProject")}catch(o){console.error("清除存储失败:",o)}}}}),q=I("canvas",()=>{const s=d(null),t=d(null),c=d(!1),i=d("edit"),b=d("meta2d"),g=a=>{s.value=a},l=a=>{a&&Object.defineProperties(a,{mqtt:{value:a.mqtt,writable:!0,configurable:!0,enumerable:!0},mqttOptions:{value:a.mqttOptions,writable:!0,configurable:!0,enumerable:!0},mqttTopics:{value:a.mqttTopics,writable:!0,configurable:!0,enumerable:!0},https:{value:a.https,writable:!0,configurable:!0,enumerable:!0},http:{value:a.http,writable:!0,configurable:!0,enumerable:!0},httpTimeInterval:{value:a.httpTimeInterval,writable:!0,configurable:!0,enumerable:!0},httpHeaders:{value:a.httpHeaders,writable:!0,configurable:!0,enumerable:!0},websocket:{value:a.websocket,writable:!0,configurable:!0,enumerable:!0},websocketProtocols:{value:a.websocketProtocols,writable:!0,configurable:!0,enumerable:!0}}),t.value=a},u=()=>{var a;(a=t.value)!=null&&a.cleanup&&t.value.cleanup(),s.value=null,t.value=null},w=()=>{if(!t.value)return;const n={...t.value.store.data,mqtt:t.value.mqtt,mqttOptions:t.value.mqttOptions,mqttTopics:t.value.mqttTopics,https:t.value.https,http:t.value.http,httpTimeInterval:t.value.httpTimeInterval,httpHeaders:t.value.httpHeaders,websocket:t.value.websocket,websocketProtocols:t.value.websocketProtocols};S.save(n)},f=d("meta2d");return{pixiApp:s,meta2dApp:t,setPixiApp:g,setMeta2dApp:l,clearInstances:u,mode:i,activeEngine:b,showMap:c,toggleMap:()=>(c.value=!c.value,c.value),save:w,load:()=>S.load(),clearStorage:()=>S.clear(),currentCanvas:f,syncNodesToCanvas:()=>{}}}),z={mqttOptions:{},mqtt:"",mqttTopics:[],https:[],websocket:"",websocketProtocols:[]};function B(){const s=q(),t=d(null);async function c(){try{const{rectangle:e,circle:r,diamond:a,triangle:n}=await _(async()=>{const{rectangle:v,circle:m,diamond:A,triangle:o}=await import("./meta2d-DtcXQZZw.js").then(p=>p.i);return{rectangle:v,circle:m,diamond:A,triangle:o}},__vite__mapDeps([0,1]),import.meta.url);[e,r,a,n].forEach(v=>{const m=v.name||v.type;m&&v&&(x.path2dDraws[m]=v)})}catch(e){console.warn("基本图元注册失败",e)}}function i(e){if(!t.value)return;const r=Object.entries(z).reduce((a,[n,v])=>({...a,[n]:{value:e[n]||v,writable:!0,configurable:!0,enumerable:!0}}),{});Object.defineProperties(t.value,r)}function b(e){var r,a;if(t.value)try{e.mqttOptions&&Object.keys(e.mqttOptions).length>0&&((a=(r=t.value).initMqtt)==null||a.call(r,e.mqttOptions)),Array.isArray(e.https)&&g(e.https),e.websocket&&l(e.websocket,e.websocketProtocols)}catch(n){console.error("Communication initialization error:",n)}}function g(e){t.value&&e.forEach(r=>{if(!r.httpTimeInterval)return;const a=setInterval(async()=>{try{const n=typeof r.httpHeaders=="string"?JSON.parse(r.httpHeaders):r.httpHeaders||{},m=await(await fetch(r.http,{method:r.method||"GET",headers:n,body:r.method!=="GET"?r.body:void 0})).json();console.log("HTTP polling data:",m)}catch(n){n instanceof SyntaxError?console.error("HTTP headers parsing error:",{headers:r.httpHeaders,error:n.message}):console.error("HTTP polling error:",{url:r.http,method:r.method,error:n.message})}},r.httpTimeInterval);t.value.httpIntervals=t.value.httpIntervals||[],t.value.httpIntervals.push(a)})}function l(e,r){if(t.value)try{const a=new WebSocket(e,r);a.onopen=()=>{console.log("WebSocket connected:",e)},a.onmessage=n=>{console.log("WebSocket message received:",n.data)},a.onerror=n=>{console.error("WebSocket error:",n)},a.onclose=()=>{console.log("WebSocket closed")},t.value.ws=a,t.value.websocket=e,t.value.websocketProtocols=r}catch(a){console.error("WebSocket initialization error:",{url:e,protocols:r,error:a.message})}}async function u(e){if(t.value)return t.value;t.value=new J(e,{grid:!0}),await c(),s.setMeta2dApp(t.value);const r=()=>{var a;return(a=t.value)==null?void 0:a.resize(e.clientWidth,e.clientHeight)};return window.addEventListener("resize",r),t.value.cleanup=()=>{var a;window.removeEventListener("resize",r),(a=t.value)==null||a.destroy(),t.value=null,s.clearInstances()},t.value}function w(e){if(t.value)try{t.value.clear(),i(e),b(e);const r=f(e);h(e,r),t.value.setValue(r),s.setMeta2dApp(t.value)}catch(r){throw console.error("Error loading data:",r),r}}function f(e){return t.value?{...t.value.data(),scale:e.scale||1,origin:e.origin||{x:0,y:0},center:e.center||{x:0,y:0},paths:e.paths||{},lineAnimateDraws:e.lineAnimateDraws||{},iot:e.iot||{},dataPoints:e.dataPoints||[],mqttOptions:e.mqttOptions,mqtt:e.mqtt,mqttTopics:e.mqttTopics,https:e.https,websocket:e.websocket,websocketProtocols:e.websocketProtocols,pens:[]}:null}function h(e,r){(e.pens||e.nodes||(Array.isArray(e)?e:[])).forEach(n=>{var m;const v={...n,name:n.name||n.type,type:n.type};r.pens.push(v),(m=t.value)==null||m.addPen(v)})}function y(){var e,r;t.value&&(t.value.httpIntervals&&(t.value.httpIntervals.forEach(clearInterval),t.value.httpIntervals=[]),t.value.ws instanceof WebSocket&&t.value.ws.close(),(r=(e=t.value).cleanup)==null||r.call(e))}return{instance:t,init:u,load:w,cleanup:y}}var L=(s=>(s[s.File=0]="File",s[s.Pen=1]="Pen",s))(L||{});const P=T({mode:0,pens:void 0}),F=()=>({selections:P,select:t=>{if(!t||t.length===0){P.mode=0,P.pens=void 0;return}P.mode=1,P.pens=t}}),R=D({__name:"Meta2DCanvas",props:{readOnly:{type:Boolean,default:!1}},setup(s){const{select:t}=F(),c=d(null),i=q(),b=l=>{t(l)},g=()=>{t()};return M(async()=>{if(!c.value)return;const{init:l,instance:u,load:w}=B();if(await l(c.value),!u)return;const f=S.load();f&&w(f);const h=c.value;h.addEventListener("drop",e=>{e.preventDefault();const r=JSON.parse(e.dataTransfer.getData("application/meta-node")),a=h.getBoundingClientRect();u.value.addNode({...r,x:e.clientX-a.left,y:e.clientY-a.top})}),h.addEventListener("dragover",e=>e.preventDefault()),i.setMeta2dApp(u.value),u.value.on("active",b),u.value.on("inactive",g);const y=e=>{switch(e.action){case k.Link:if(e.value&&typeof e.value=="string"){const r=e.value.replace(/^['"]|['"]$/g,"");window.open(r,"_blank")}break;case k.OpenPage:e.value&&console.log("打开页面:",e.value);break;case k.JavaScript:if(e.value)try{console.log("执行JavaScript:",e.value)}catch(r){console.error("JavaScript执行错误:",r)}break;default:console.log("未处理的事件类型:",e.action)}};u.value.on("click",e=>{if(!e||!e.pen)return;const r=e.pen;if(!r.events||!Array.isArray(r.events))return;const a=r.events.find(n=>n.name==="click");a&&y(a)})}),N(()=>{var l,u;(u=(l=i.meta2dApp)==null?void 0:l.destroy)==null||u.call(l),i.clearInstances()}),(l,u)=>(H(),C("div",{ref_key:"canvasContainer",ref:c,class:"meta2d-canvas-container",style:{width:"100%",height:"100%"}},null,512))}}),U=W(R,[["__scopeId","data-v-8528c157"]]);export{U as M,L as S,q as a,F as b,B as c,S as s,K as u};
