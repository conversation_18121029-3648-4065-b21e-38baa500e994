package com.slhc.hcms.module.bcss.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.slhc.hcms.module.bcss.entity.BcssTask;
import com.slhc.hcms.module.bcss.mapper.BcssTaskMapper;
import com.slhc.hcms.module.infra.es.service.impl.ElasticsearchServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 定时任务：同步任务数据
 */

@Slf4j
@Component
public class TaskDataSyncJob implements Job {

    @Resource
    private BcssTaskMapper bcssTaskMapper;

    @Resource
    private ElasticsearchServiceImpl elasticsearchService;

    public TaskDataSyncJob() {
    }

    /**
     * 构建索引映射
     */
    private static JSONObject buildIndexMapping() {
        JSONObject id = new JSONObject();
        id.put("type", "keyword");

        JSONObject sourStationId = new JSONObject();
        sourStationId.put("type", "keyword");

        JSONObject destStationId = new JSONObject();
        destStationId.put("type", "keyword");

        JSONObject status = new JSONObject();
        status.put("type", "integer");

        JSONObject isTimeout = new JSONObject();
        isTimeout.put("type", "integer");

        JSONObject timeField = new JSONObject();
        timeField.put("type", "date");
        timeField.put("format", "yyyy-MM-dd HH:mm:ss");

        JSONObject properties = new JSONObject();
        properties.put("id", id);
        properties.put("sourStationId", sourStationId);
        properties.put("destStationId", destStationId);
        properties.put("status", status);
        properties.put("isTimeout", isTimeout);
        properties.put("startTime", timeField.clone());
        properties.put("finishTime", timeField.clone());
        properties.put("createTime", timeField.clone());
        properties.put("updateTime", timeField.clone());

        JSONObject mappings = new JSONObject();
        mappings.put("properties", properties);

        JSONObject mapping = new JSONObject();
        mapping.put("mappings", mappings);

        return mapping;
    }

    /**
     * 定时任务：定时同步bcss_task表数据到Elasticsearch
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String todayStr = today.format(formatter);

            String indexName = String.format("bcss_task-%s", todayStr);

            // 检查索引是否存在
            JSONObject mapping = buildIndexMapping();
            // 创建索引
            boolean created = elasticsearchService.createIndex(indexName, mapping);
            if (!created) {
                log.error("创建索引 {} 失败", indexName);
                return;
            }

            LocalDateTime startOfDay = LocalDateTime.of(today, LocalTime.MIN);
            LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);
            Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

            QueryWrapper<BcssTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("create_time", startDate, endDate);
            queryWrapper.eq("del_flag", 0);
            List<BcssTask> taskList = bcssTaskMapper.selectList(queryWrapper);

            if (taskList == null || taskList.isEmpty()) {
                log.info("未查询到当天任务数据");
                return;
            }

            // 写入数据
            for (BcssTask task : taskList) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(task));
                elasticsearchService.saveData(indexName, "", task.getId(), jsonObject);
            }

        } catch (Exception e) {
            log.error("同步任务数据到Elasticsearch失败，时间：{}", new Date(), e);
            throw new JobExecutionException(e);
        }
    }

}
