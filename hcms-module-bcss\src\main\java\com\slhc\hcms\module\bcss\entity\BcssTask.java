package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 任务表
 * @Author: jeecg-boot
 * @Date:   2025-07-11
 * @Version: V1.0
 */
@Data
@TableName("bcss_task")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="任务表")
public class BcssTask implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**删除状态(0-正常,1-已删除)*/
	    //@Excel(name = "删除状态(0-正常,1-已删除)", width = 15)
    @Schema(description = "删除状态(0-正常,1-已删除)")
    @TableLogic
    private Integer delFlag;
	/**任务类型*/
	@Excel(name = "任务类型", width = 15, dicCode = "task_type")
	@Dict(dicCode = "task_type")
    @Schema(description = "任务类型")
    private Integer taskType;
	/**运输箱编号*/
	@Excel(name = "运输箱编号", width = 15)
    @Schema(description = "运输箱编号")
    private String boxBarcode;
	/**起始站点编号*/
	@Excel(name = "起始站点编号", width = 15)
    @Schema(description = "起始站点编号")
    private String sourStationId;
	/**起始站点名称*/
	@Excel(name = "起始站点名称", width = 15)
    @Schema(description = "起始站点名称")
    private String sourStationName;
	/**目标站点编号*/
	@Excel(name = "目标站点编号", width = 15)
    @Schema(description = "目标站点编号")
    private String destStationId;
	/**目标站点名称*/
	@Excel(name = "目标站点名称", width = 15)
    @Schema(description = "目标站点名称")
    private String destStationName;
	/**任务状态,进行中:1、已完成:2、已取消:3*/
	@Excel(name = "任务状态", width = 15, dicCode = "task_status")
	@Dict(dicCode = "task_status")
    @Schema(description = "任务状态")
    private Integer status;
	/**任务开始时间*/
	@Excel(name = "任务开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务开始时间")
    private Date startTime;
	/**任务结束时间*/
	@Excel(name = "任务结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "任务结束时间")
    private Date finishTime;
	/**是否超时*/
	@Excel(name = "是否超时", width = 15)
    @Schema(description = "是否超时")
    private Integer isTimeout;
	/**预计耗时*/
	@Excel(name = "预计耗时", width = 15)
    @Schema(description = "预计耗时")
    private Integer estimatedTime;
	/**实际耗时*/
	@Excel(name = "实际耗时", width = 15)
    @Schema(description = "实际耗时")
    private Integer consumeTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户Id*/
	    //@Excel(name = "租户Id", width = 15)
    @Schema(description = "租户Id")
    private String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
}
