package org.jeecg.modules.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.entity.SysTableWhiteList;
import org.jeecg.modules.system.service.ISysTableWhiteListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 系统表白名单
 * @Author: jeecg-boot
 * @Date: 2023-09-12
 * @Version: V1.0
 */
@Slf4j
@Tag(name = "系统表白名单")
@RestController
@RequestMapping("/sys/tableWhiteList")
public class SysTableWhiteListController extends JeecgController<SysTableWhiteList, ISysTableWhiteListService> {

    @Autowired
    private ISysTableWhiteListService sysTableWhiteListService;

    /**
     * 分页列表查询
     *
     * @param sysTableWhiteList
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:list")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(
            SysTableWhiteList sysTableWhiteList,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req
    ) {
        QueryWrapper<SysTableWhiteList> queryWrapper = QueryGenerator.initQueryWrapper(sysTableWhiteList, req.getParameterMap());
        Page<SysTableWhiteList> page = new Page<>(pageNo, pageSize);
        IPage<SysTableWhiteList> pageList = sysTableWhiteListService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param sysTableWhiteList
     * @return
     */
    @AutoLog(value = "系统表白名单-添加")
    @Operation(summary = "系统表白名单-添加")
    //@RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:add")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SysTableWhiteList sysTableWhiteList) {
        if (sysTableWhiteListService.add(sysTableWhiteList)) {
            return Result.OK("添加成功！");
        } else {
            return Result.error("添加失败！");
        }
    }

    /**
     * 编辑
     *
     * @param sysTableWhiteList
     * @return
     */
    @AutoLog(value = "系统表白名单-编辑")
    @Operation(summary = "系统表白名单-编辑")
    //@RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody SysTableWhiteList sysTableWhiteList) {
        if (sysTableWhiteListService.edit(sysTableWhiteList)) {
            return Result.OK("编辑成功！");
        } else {
            return Result.error("编辑失败！");
        }
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "系统表白名单-通过id删除")
    @Operation(summary = "系统表白名单-通过id删除")
//    @RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:delete")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        if (sysTableWhiteListService.deleteByIds(id)) {
            return Result.OK("删除成功！");
        } else {
            return Result.error("删除失败！");
        }
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "系统表白名单-批量删除")
    @Operation(summary = "系统表白名单-批量删除")
//    @RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        if (sysTableWhiteListService.deleteByIds(ids)) {
            return Result.OK("批量删除成功！");
        } else {
            return Result.error("批量删除失败！");
        }
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "系统表白名单-通过id查询")
    @Operation(summary = "系统表白名单-通过id查询")
//    @RequiresRoles("admin")
    @RequiresPermissions("system:tableWhite:queryById")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        SysTableWhiteList sysTableWhiteList = sysTableWhiteListService.getById(id);
        return Result.OK(sysTableWhiteList);
    }

}
