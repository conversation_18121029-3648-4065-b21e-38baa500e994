package com.slhc.hcms.module.bcss.service.impl;

import com.slhc.hcms.module.bcss.entity.BcssDeviceSignal;
import com.slhc.hcms.module.bcss.mapper.BcssDeviceSignalMapper;
import com.slhc.hcms.module.bcss.service.IBcssDeviceSignalService;
import com.slhc.hcms.module.bcss.entity.BcssDevice;
import com.slhc.hcms.module.bcss.service.IBcssDeviceService;
import com.slhc.hcms.module.bcss.entity.BcssPlc;
import com.slhc.hcms.module.bcss.service.IBcssPlcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

/**
 * @Description: 箱式设备信号表
 * @Author: jeecg-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class BcssDeviceSignalServiceImpl extends ServiceImpl<BcssDeviceSignalMapper, BcssDeviceSignal> implements IBcssDeviceSignalService {

    @Autowired
    private IBcssDeviceService bcssDeviceService;
    
    @Autowired
    private IBcssPlcService bcssPlcService;

    @Override
    public List<BcssDeviceSignal> queryBySingleId(String id) {
        return this.list(new QueryWrapper<BcssDeviceSignal>().eq("device_id", id));
    }

    @Override
    public Result<?> importExcelWithValidation(MultipartFile file) {
        if (file != null && !file.isEmpty()) {
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(false);
            
            try {
                List<BcssDeviceSignal> listBcssDeviceSignals = ExcelImportUtil.importExcel(file.getInputStream(), BcssDeviceSignal.class, params);
                
                log.info("Excel导入解析完成，共解析到{}条数据", listBcssDeviceSignals.size());
                
                // 查询并打印数据库中所有可用的设备名称，用于调试
                List<BcssDevice> allDevices = bcssDeviceService.list(new QueryWrapper<BcssDevice>().eq("del_flag", 0).eq("status", 1));
                log.info("数据库中可用的设备列表:");
                for (BcssDevice device : allDevices) {
                    log.info("  设备ID: {}, 设备名称: '{}'", device.getId(), device.getName());
                }
                
                // 数据校验
                List<String> errorMsgList = new ArrayList<>();
                Set<String> signalCodesInExcel = new HashSet<>();
                
                // 注意：不需要手动构建名称到ID的映射，框架的@Dict注解已自动完成转换
                
                // 用于存储有效的数据行
                List<BcssDeviceSignal> validSignals = new ArrayList<>();
                
                for (int i = 0; i < listBcssDeviceSignals.size(); i++) {
                    BcssDeviceSignal signal = listBcssDeviceSignals.get(i);
                    int rowNum = i + 4; // Excel行号：数据索引 + 标题行(2) + 表头行(1) + Excel从1开始计数
                    
                    // 跳过完全空行：检查所有主要字段是否都为空
                    if (isCompletelyEmptyRow(signal)) {
                        log.info("第{}行为完全空行，跳过", rowNum);
                        continue;
                    }
                    
                    // 手动处理@Dict转换失败的情况：将数值字符串转换为对应的ID
                    signal = handleDictConversion(signal, rowNum);
                    
                    // 添加调试日志，查看@Dict转换后的实际值
                    log.info("第{}行数据: signalName=[{}], deviceId=[{}], plcId=[{}]", 
                        rowNum, signal.getSignalName(), signal.getDeviceId(), signal.getPlcId());
                    
                    // 添加更详细的调试信息
                    log.info("第{}行 deviceId 详细信息: 值=[{}], 长度=[{}], 是否为数字=[{}]", 
                        rowNum, signal.getDeviceId(), 
                        signal.getDeviceId() != null ? signal.getDeviceId().length() : 0,
                        signal.getDeviceId() != null ? signal.getDeviceId().matches("\\d+") : false);
                    
                    // 1. 必填字段非空校验
                    if (oConvertUtils.isEmpty(signal.getSignalName()) || signal.getSignalName().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：信号逻辑名称不能为空");
                        continue;
                    }
                    
                    if (oConvertUtils.isEmpty(signal.getSignalCode()) || signal.getSignalCode().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：信号业务编码不能为空");
                        continue;
                    }
                    
                    if (oConvertUtils.isEmpty(signal.getSymbol()) || signal.getSymbol().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：PLC中的符号名不能为空");
                        continue;
                    }
                    
                    if (oConvertUtils.isEmpty(signal.getNodeId()) || signal.getNodeId().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：OPC UA 节点ID不能为空");
                        continue;
                    }
                    
                    if (signal.getDbNumber() == null) {
                        errorMsgList.add("第" + rowNum + "行：DB块地址不能为空");
                        continue;
                    }
                    
                    if (signal.getAddress() == null) {
                        errorMsgList.add("第" + rowNum + "行：内存地址不能为空");
                        continue;
                    }
                    
                    if (signal.getAddrOffset() == null) {
                        errorMsgList.add("第" + rowNum + "行：偏移地址不能为空");
                        continue;
                    }
                    
                    if (signal.getByteLength() == null) {
                        errorMsgList.add("第" + rowNum + "行：数据字节长度不能为空");
                        continue;
                    }
                    
                    // 2. 校验设备ID是否有效（框架已自动完成名称到ID的转换）
                    if (oConvertUtils.isEmpty(signal.getDeviceId())) {
                        errorMsgList.add("第" + rowNum + "行：所属设备名称不能为空");
                        continue;
                    }
                    
                    // 检查是否是有效的设备ID（雪花算法ID格式：18-19位数字）
                    // 如果不是，说明@Dict转换失败，设备名称不存在
                    if (!signal.getDeviceId().matches("\\d{18,19}")) {
                        errorMsgList.add("第" + rowNum + "行：设备名称'" + signal.getDeviceId() + "'不存在，请检查设备名称是否正确");
                        continue;
                    }
                    
                    // 3. 校验PLC ID是否有效（框架已自动完成名称到ID的转换）
                    if (oConvertUtils.isEmpty(signal.getPlcId())) {
                        errorMsgList.add("第" + rowNum + "行：所属PLC名称不能为空");
                        continue;
                    }
                    
                    // 检查是否是有效的PLC ID（雪花算法ID格式：18-19位数字）
                    // 如果不是，说明@Dict转换失败，可能是PLC名称为空或不存在
                    if (!signal.getPlcId().matches("\\d{18,19}")) {
                        // 如果plcId不是有效的雪花算法ID，可能是空值被@Dict自动填充了默认值
                        // 或者是无效的PLC名称
                        errorMsgList.add("第" + rowNum + "行：所属PLC名称不能为空或PLC名称不存在，请检查PLC名称是否正确");
                        continue;
                    }
                    
                    // 检查是否为@Dict自动填充的默认PLC ID
                    // 这个ID值是@Dict在找不到匹配PLC名称时自动填充的默认值
                    if ("1966420215330783233".equals(signal.getPlcId())) {
                        errorMsgList.add("第" + rowNum + "行：所属PLC名称不能为空，请填写正确的PLC名称");
                        continue;
                    }
                    
                    // 4. 校验信号业务编码在同一设备的同一PLC下的唯一性（Excel内部重复）
                    String signalCodeKey = signal.getDeviceId() + "_" + signal.getPlcId() + "_" + signal.getSignalCode();
                    if (signalCodesInExcel.contains(signalCodeKey)) {
                        errorMsgList.add("第" + rowNum + "行：信号业务编码'" + signal.getSignalCode() + "'在Excel中重复（同一设备的同一PLC下）");
                        continue;
                    }
                    signalCodesInExcel.add(signalCodeKey);
                    
                    // 5. 校验信号业务编码在数据库中的唯一性（同一设备的同一PLC下）
                    QueryWrapper<BcssDeviceSignal> signalCodeQuery = new QueryWrapper<>();
                    signalCodeQuery.eq("signal_code", signal.getSignalCode())
                                   .eq("device_id", signal.getDeviceId())
                                   .eq("plc_id", signal.getPlcId())
                                   .eq("del_flag", 0);
                    long signalCodeCount = this.count(signalCodeQuery);
                    if (signalCodeCount > 0) {
                        errorMsgList.add("第" + rowNum + "行：信号业务编码'" + signal.getSignalCode() + "'在同一设备的同一PLC下已存在");
                        continue;
                    }
                    
                    // 6. 校验字典字段的有效性
                    if (signal.getDataType() != null) {
                        // 数据类型校验 (根据字典表bcss_plc_signal_type的实际值范围)
                        if (signal.getDataType() < 0 || signal.getDataType() > 14) {
                            errorMsgList.add("第" + rowNum + "行：数据类型值无效，应为0-14之间的整数");
                            continue;
                        }
                    }
                    
                    if (signal.getSignalLevel() != null) {
                        // 信号级别校验 (根据字典表bcss_plc_signal_level的实际值范围)
                        if (signal.getSignalLevel() < 0 || signal.getSignalLevel() > 53) {
                            errorMsgList.add("第" + rowNum + "行：信号级别值无效，应为0-53之间的整数");
                            continue;
                        }
                    }
                    
                    if (signal.getSubscription() != null) {
                        // 是否订阅校验 (1-订阅, 2-未订阅)
                        if (signal.getSubscription() != 1 && signal.getSubscription() != 2) {
                            errorMsgList.add("第" + rowNum + "行：是否订阅值无效，应为1(订阅)或2(未订阅)");
                            continue;
                        }
                    }
                    
                    // 7. 数据范围校验
                    if (signal.getDbNumber() < 0) {
                        errorMsgList.add("第" + rowNum + "行：DB块地址不能为负数");
                        continue;
                    }
                    
                    if (signal.getAddress() < 0) {
                        errorMsgList.add("第" + rowNum + "行：内存地址不能为负数");
                        continue;
                    }
                    
                    if (signal.getAddrOffset() < 0) {
                        errorMsgList.add("第" + rowNum + "行：偏移地址不能为负数");
                        continue;
                    }
                    
                    if (signal.getByteLength() <= 0) {
                        errorMsgList.add("第" + rowNum + "行：数据字节长度必须大于0");
                        continue;
                    }
                    
                    // 8. 设置默认值和系统字段
                    signal.setStatus(1); // 状态默认为正常
                    
                    // 设置字典字段默认值（如果为空）
                    if (signal.getDataType() == null) {
                        signal.setDataType(0); // 默认数据类型：Bit_0
                    }
                    
                    if (signal.getSignalLevel() == null) {
                        signal.setSignalLevel(0); // 默认信号级别：Ordinary_0
                    }
                    
                    if (signal.getSubscription() == null) {
                        signal.setSubscription(1); // 默认订阅
                    }
                    
                    // 设置数字字段默认值（如果为空）
                    if (signal.getDbNumber() == null) {
                        signal.setDbNumber(0); // 默认DB块地址为0
                    }
                    
                    if (signal.getAddress() == null) {
                        signal.setAddress(0); // 默认内存地址为0
                    }
                    
                    if (signal.getAddrOffset() == null) {
                        signal.setAddrOffset(0); // 默认偏移地址为0
                    }
                    
                    if (signal.getByteLength() == null) {
                        signal.setByteLength(1); // 默认数据字节长度为1
                    }
                    
                    // 设置创建人信息和租户ID
                    org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
                    if (sysUser != null) {
                        signal.setCreateBy(sysUser.getUsername());
                        signal.setUpdateBy(sysUser.getUsername());
                        signal.setTenantId(sysUser.getRelTenantIds());
                    }
                    
                    // 校验通过，添加到有效数据列表
                    validSignals.add(signal);
                }
                
                // 如果有错误，返回错误信息
                if (!errorMsgList.isEmpty()) {
                    return Result.error("导入失败：\n" + String.join("\n", errorMsgList));
                }
                
                // 批量保存数据
                if (!validSignals.isEmpty()) {
                    this.saveBatch(validSignals);
                }
                
                return Result.OK("导入成功，共导入" + validSignals.size() + "条数据");
                
            } catch (Exception e) {
                log.error("导入失败：", e);
                
                // 检查是否是字典值错误导致的异常
                if (e.getMessage() != null && e.getMessage().equals("Excel 值获取失败")) {
                    String errorMsg = "导入失败：Excel中存在无效的枚举值。\n\n";
                    errorMsg += "请检查以下字段的值是否正确：\n";
                    errorMsg += "• 数据类型：必须是 Bit、Byte、Word、DWord、Int、DInt、Real、LReal、String、S7String、S7WString、Timer、Counter、DateTime、DateTimeLong 中的一个\n";
                    errorMsg += "• 信号级别：必须是 Ordinary、Warn、Fault、OutNumber、ScanResult、Box、RollerBox、DeviceStatus 等中的一个\n";
                    errorMsg += "• 是否订阅：必须是 订阅 或 未订阅\n";
                    errorMsg += "• 状态：必须是 正常 或 异常\n\n";
                    errorMsg += "请修正Excel中的错误值后重新导入。";
                    return Result.error(errorMsg);
                }
                
                return Result.error("导入失败：" + e.getMessage());
            }
        }
        
        return Result.error("请选择要导入的文件");
    }

    /**
     * 手动处理@Dict转换失败的情况
     * 当Excel中的数值被解析为"11.0"时，@Dict无法匹配数据库中的"11"
     * 需要手动查找并转换
     */
    private BcssDeviceSignal handleDictConversion(BcssDeviceSignal signal, int rowNum) {
        // 处理设备名称转换
        if (signal.getDeviceId() != null && !signal.getDeviceId().matches("\\d{18,19}")) {
            String deviceName = signal.getDeviceId().trim();
            // 如果是数值格式（如"11.0"），去掉小数点
            if (deviceName.matches("\\d+\\.0")) {
                deviceName = deviceName.substring(0, deviceName.indexOf("."));
            }
            
            log.info("第{}行：尝试查找设备名称 '{}'", rowNum, deviceName);
            
            // 查询数据库中匹配的设备
            QueryWrapper<BcssDevice> deviceQuery = new QueryWrapper<>();
            deviceQuery.eq("name", deviceName)
                       .eq("del_flag", 0)
                       .eq("status", 1);
            BcssDevice device = bcssDeviceService.getOne(deviceQuery);
            
            if (device != null) {
                log.info("第{}行：找到设备 '{}' -> ID: {}", rowNum, deviceName, device.getId());
                signal.setDeviceId(device.getId());
            } else {
                log.warn("第{}行：未找到设备名称 '{}'", rowNum, deviceName);
            }
        }
        
        // 处理PLC名称转换（类似逻辑）
        if (signal.getPlcId() != null && !signal.getPlcId().matches("\\d{18,19}")) {
            String plcName = signal.getPlcId().trim();
            // 如果是数值格式（如"11.0"），去掉小数点
            if (plcName.matches("\\d+\\.0")) {
                plcName = plcName.substring(0, plcName.indexOf("."));
            }
            
            log.info("第{}行：尝试查找PLC名称 '{}'", rowNum, plcName);
            
            // 查询数据库中匹配的PLC
            QueryWrapper<BcssPlc> plcQuery = new QueryWrapper<>();
            plcQuery.eq("name", plcName)
                    .eq("del_flag", 0)
                    .eq("status", 1);
            BcssPlc plc = bcssPlcService.getOne(plcQuery);
            
            if (plc != null) {
                log.info("第{}行：找到PLC '{}' -> ID: {}", rowNum, plcName, plc.getId());
                signal.setPlcId(plc.getId());
            } else {
                log.warn("第{}行：未找到PLC名称 '{}'", rowNum, plcName);
            }
        }
        
        return signal;
    }

    /**
     * 检查是否为完全空行
     * 不依赖deviceId和plcId判断，因为@Dict注解会自动填充这些字段
     * 只检查用户实际填写的核心业务字段
     */
    private boolean isCompletelyEmptyRow(BcssDeviceSignal signal) {
        return oConvertUtils.isEmpty(signal.getSignalName()) &&
               oConvertUtils.isEmpty(signal.getSignalCode()) &&
               oConvertUtils.isEmpty(signal.getSymbol()) &&
               oConvertUtils.isEmpty(signal.getNodeId()) &&
               signal.getDbNumber() == null &&
               signal.getAddress() == null &&
               signal.getAddrOffset() == null &&
               signal.getByteLength() == null;
    }

}
