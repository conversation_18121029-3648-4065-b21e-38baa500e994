package com.slhc.hcms.module.bcss.controller.admin;
import com.slhc.hcms.module.bcss.entity.BcssDashboard;
import com.slhc.hcms.module.bcss.service.IBcssDashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name="箱式首页")
@RestController
@RequestMapping("/admin/bcssDashboard")
@Slf4j
public class BcssDashboardController extends JeecgController<BcssDashboard, IBcssDashboardService>
{
    private final IBcssDashboardService dashboardService;

    @Autowired
    public BcssDashboardController(IBcssDashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    @AutoLog(value = "获取BCSS仪表盘数据")
    @Operation(summary="获取仪表盘数据", description="根据站点ID获取仪表盘数据")
    @PostMapping("/data")
    @CrossOrigin(origins = "*")
    public BcssDashboard getDashboardData(@RequestBody BcssDashboard request) {
        // 调用服务层处理请求
        BcssDashboard result = dashboardService.processStationData(request);
        return result;
    }

    @Operation(summary="心跳检测", description="接收前端心跳检测请求并返回状态")
    @GetMapping("/heartbeat")
    @CrossOrigin(origins = "*")
    public Result<Boolean> heartbeat() {
        return Result.ok(true);
    }
}
