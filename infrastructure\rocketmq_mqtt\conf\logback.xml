<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<configuration>
    <appender name="MainAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/mqtt.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/mqtt.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss:SSS,GMT+8} %p [%logger{0}] %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="AsyncMainAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="MainAppender"/>
    </appender>

    <appender name="RmqAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/rmq.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/rmq.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>10</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %p [%logger{0}] %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="AsyncRmqAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RmqAppender"/>
    </appender>


    <appender name="StatAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/stat.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/stat.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>10</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyy-MM-dd HH:mm:ss,GMT+8} %m%n</pattern>
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>
    <appender name="AsyncStatAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="StatAppender"/>
    </appender>

    <root>
        <level value="info"/>
        <appender-ref ref="AsyncMainAppender"/>
    </root>

    <logger name="StatLogger" additivity="false">
        <level value="info"/>
        <appender-ref ref="AsyncStatAppender"/>
    </logger>

    <logger name="RocketmqClient" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="AsyncRmqAppender"/>
    </logger>

    <logger name="RocketmqRemoting" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="AsyncRmqAppender"/>
    </logger>

</configuration>
