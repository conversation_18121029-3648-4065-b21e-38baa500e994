package com.slhc.hcms.module.bcss.vo;

import com.slhc.hcms.module.bcss.entity.BcssTask;
import com.slhc.hcms.module.bcss.entity.BcssTaskLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 任务详情VO
 * @Author: Cascade
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BcssTaskDetailVo extends BcssTask {
    private static final long serialVersionUID = 1L;

    @Schema(description = "任务日志列表")
    private List<BcssTaskLog> logs;
}
