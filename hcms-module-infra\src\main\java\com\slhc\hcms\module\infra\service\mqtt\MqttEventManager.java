package com.slhc.hcms.module.infra.service.mqtt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Pattern;

/**
 * MQTT事件管理器
 * 负责管理所有MQTT事件监听器，处理事件注册、注销和分发
 */
@Component
public class MqttEventManager {
    
    private static final Logger log = LoggerFactory.getLogger(MqttEventManager.class);
    
    // 存储主题和监听器的映射关系
    private final Map<String, List<MqttEventListener>> topicListeners = new ConcurrentHashMap<>();
    
    // 存储通配符主题模式
    private final Map<String, Pattern> wildcardPatterns = new ConcurrentHashMap<>();
    
    /**
     * 注册事件监听器
     * @param listener 事件监听器
     */
    public void registerListener(MqttEventListener listener) {
        String topic = listener.getTopic();
        String listenerName = listener.getListenerName();
        
        log.info("注册MQTT事件监听器: {} -> {}", listenerName, topic);
        
        topicListeners.computeIfAbsent(topic, k -> new CopyOnWriteArrayList<>()).add(listener);
        
        // 如果是通配符主题，编译正则表达式
        if (topic.contains("+") || topic.contains("#")) {
            String regex = topic.replace("+", "[^/]+").replace("#", ".*");
            wildcardPatterns.put(topic, Pattern.compile(regex));
        }
        
        try {
            listener.onListenerRegistered();
            log.info("MQTT事件监听器注册成功: {}", listenerName);
        } catch (Exception e) {
            log.error("MQTT事件监听器注册失败: {}", listenerName, e);
            throw new RuntimeException("监听器注册失败: " + listenerName, e);
        }
    }
    
    /**
     * 注销事件监听器
     * @param listener 事件监听器
     */
    public void unregisterListener(MqttEventListener listener) {
        String topic = listener.getTopic();
        String listenerName = listener.getListenerName();
        
        log.info("注销MQTT事件监听器: {} -> {}", listenerName, topic);
        
        List<MqttEventListener> listeners = topicListeners.get(topic);
        if (listeners != null) {
            listeners.remove(listener);
            if (listeners.isEmpty()) {
                topicListeners.remove(topic);
                wildcardPatterns.remove(topic);
            }
        }
        
        try {
            listener.onListenerUnregistered();
            log.info("MQTT事件监听器注销成功: {}", listenerName);
        } catch (Exception e) {
            log.error("MQTT事件监听器注销失败: {}", listenerName, e);
        }
    }
    
    /**
     * 根据主题获取所有匹配的监听器
     * @param topic 消息主题
     * @return 匹配的监听器列表
     */
    public List<MqttEventListener> getListenersForTopic(String topic) {
        List<MqttEventListener> result = new ArrayList<>();
        
        // 直接匹配
        List<MqttEventListener> directListeners = topicListeners.get(topic);
        if (directListeners != null) {
            result.addAll(directListeners);
        }
        
        // 通配符匹配
        for (Map.Entry<String, Pattern> entry : wildcardPatterns.entrySet()) {
            String patternTopic = entry.getKey();
            Pattern pattern = entry.getValue();
            
            if (pattern.matcher(topic).matches()) {
                List<MqttEventListener> wildcardListeners = topicListeners.get(patternTopic);
                if (wildcardListeners != null) {
                    result.addAll(wildcardListeners);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取所有已注册的监听器
     * @return 所有监听器的列表
     */
    public List<MqttEventListener> getAllListeners() {
        List<MqttEventListener> result = new ArrayList<>();
        for (List<MqttEventListener> listeners : topicListeners.values()) {
            result.addAll(listeners);
        }
        return result;
    }
    
    /**
     * 获取所有已注册的主题
     * @return 主题列表
     */
    public Set<String> getAllTopics() {
        return new HashSet<>(topicListeners.keySet());
    }
    
    /**
     * 清空所有监听器
     */
    public void clearAllListeners() {
        log.info("清空所有MQTT事件监听器");
        
        // 先注销所有监听器
        for (List<MqttEventListener> listeners : topicListeners.values()) {
            for (MqttEventListener listener : listeners) {
                try {
                    listener.onListenerUnregistered();
                } catch (Exception e) {
                    log.error("监听器注销失败: {}", listener.getListenerName(), e);
                }
            }
        }
        
        topicListeners.clear();
        wildcardPatterns.clear();
    }
    
    /**
     * 获取监听器数量
     * @return 监听器数量
     */
    public int getListenerCount() {
        int count = 0;
        for (List<MqttEventListener> listeners : topicListeners.values()) {
            count += listeners.size();
        }
        return count;
    }
}
