package com.slhc.hcms.module.bcss.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 任务日志表
 * @Author: Cascade
 * @Date:   2025-07-14
 * @Version: V1.0
 */
@Data
@TableName("bcss_task_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BcssTaskLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "删除标志")
    @TableLogic
    private Integer delFlag;

    @Excel(name = "料箱号", width = 15)
    @Schema(description = "料箱号")
    private String boxBarcode;

    @Excel(name = "任务ID", width = 15)
    @Schema(description = "任务ID")
    private String taskId;

    @Excel(name = "日志内容", width = 30)
    @Schema(description = "日志内容")
    private String info;

    @Schema(description = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @Schema(description = "创建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新人")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @Schema(description = "更新日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    @Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private String tenantId;

    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
}
