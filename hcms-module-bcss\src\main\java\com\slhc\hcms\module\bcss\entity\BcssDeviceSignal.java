package com.slhc.hcms.module.bcss.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 箱式设备信号表
 * @Author: jeecg-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
@Data
@TableName("bcss_device_signal")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="箱式设备信号表")
public class BcssDeviceSignal implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**所属设备ID (关联bcss_device.id)*/
	@Excel(name = "所属设备名称", width = 15, dictTable = "bcss_device", dicCode = "id", dicText = "name")
    @Schema(description = "所属设备ID (关联bcss_device.id)")
    @Dict(dictTable = "bcss_device", dicCode = "id", dicText = "name")
    private String deviceId;
	/**所属PLC ID (关联bcss_plc.id)*/
	@Excel(name = "所属PLC名称", width = 15, dictTable = "bcss_plc", dicCode = "id", dicText = "name")
    @Schema(description = "所属PLC ID (关联bcss_plc.id)")
    @Dict(dictTable = "bcss_plc", dicCode = "id", dicText = "name")
    private String plcId;
	/**信号逻辑名称 (如：启动信号)*/
	@Excel(name = "信号逻辑名称", width = 15)
    @Schema(description = "信号逻辑名称 (如：启动信号)")
    private String signalName;
	/**信号业务编码 (程序中唯一标识)*/
	@Excel(name = "信号业务编码", width = 15)
    @Schema(description = "信号业务编码 (程序中唯一标识)")
    private String signalCode;
	/**PLC中的符号名 (如: "Motor_Start")*/
	@Excel(name = "PLC中的符号名", width = 15)
    @Schema(description = "PLC中的符号名 ")
    private String symbol;
	/**OPC UA 节点ID*/
	@Excel(name = "OPC UA 节点ID", width = 15)
    @Schema(description = "OPC UA 节点ID")
    private String nodeId;
	/**DB块地址*/
	@Excel(name = "DB块地址", width = 15)
    @Schema(description = "DB块地址")
    private Integer dbNumber;
	/**内存地址*/
	@Excel(name = "内存地址", width = 15)
    @Schema(description = "内存地址")
    private Integer address;
	/**偏移地址 (位偏移)*/
	@Excel(name = "偏移地址", width = 15)
    @Schema(description = "偏移地址 (位偏移)")
    private Integer addrOffset;
	/**数据字节长度*/
	@Excel(name = "数据字节长度", width = 15)
    @Schema(description = "数据字节长度")
    private Integer byteLength;
	/**数据类型*/
	@Excel(name = "数据类型", width = 15, dicCode = "bcss_plc_signal_type")
	@Dict(dicCode = "bcss_plc_signal_type")
    @Schema(description = "数据类型 (枚举, 需与老系统保持一致)")
    private Integer dataType;
	/**信号级别/优先级*/
	@Excel(name = "信号级别", width = 15, dicCode = "bcss_plc_signal_level")
	@Dict(dicCode = "bcss_plc_signal_level")
    @Schema(description = "信号级别/优先级 (需与老系统保持一致)")
    private Integer signalLevel;
	/**是否订阅*/
	@Excel(name = "是否订阅", width = 15, dicCode = "device_subscription")
	@Dict(dicCode = "device_subscription")
    @Schema(description = "是否订阅 (通信服务是否主动轮询)")
    private Integer subscription;
	/**状态*/
	@Excel(name = "状态", width = 15, dicCode = "device_status")
	@Dict(dicCode = "device_status")
    @Schema(description = "状态 (1-启用, 0-禁用)")
    private Integer status = 1;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**逻辑删除标记 (0-正常, 1-已删除)*/
    @Schema(description = "逻辑删除标记 (0-正常, 1-已删除)")
    @TableLogic
    private Integer delFlag = 0;
	/**租户ID*/
    @Schema(description = "租户ID")
    private String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
}
