package com.slhc.hcms.module.bcss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.slhc.hcms.module.bcss.entity.BcssDevice;
import com.slhc.hcms.module.bcss.entity.BcssPlc;
import com.slhc.hcms.module.bcss.mapper.BcssDeviceMapper;
import com.slhc.hcms.module.bcss.service.IBcssDeviceService;
import com.slhc.hcms.module.bcss.service.IBcssPlcService;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.oConvertUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @Description: 设备表
 * @Author: jeecg-boot
 * @Date:   2025-07-21
 * @Version: V1.0
 */
@Slf4j
@Service
public class BcssDeviceServiceImpl extends ServiceImpl<BcssDeviceMapper, BcssDevice> implements IBcssDeviceService {

    @Autowired
    private IBcssPlcService bcssPlcService;

    @Override
    public Result<?> importExcelWithValidation(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(false);
            
            try {
                List<BcssDevice> listBcssDevices = ExcelImportUtil.importExcel(file.getInputStream(), BcssDevice.class, params);
                
                log.info("Excel导入解析完成，共解析到{}条数据", listBcssDevices.size());
                
                // 查询并打印数据库中所有可用的PLC名称，用于调试
                List<BcssPlc> allPlcs = bcssPlcService.list(new QueryWrapper<BcssPlc>().eq("del_flag", 0).eq("status", 1));
                log.info("数据库中可用的PLC列表:");
                for (BcssPlc plc : allPlcs) {
                    log.info("  PLC ID: {}, PLC名称: '{}'", plc.getId(), plc.getName());
                }
                
                // 数据校验
                List<String> errorMsgList = new ArrayList<>();
                Set<String> deviceNamesInExcel = new HashSet<>();
                
                // 用于存储有效的数据行
                List<BcssDevice> validDevices = new ArrayList<>();
                
                for (int i = 0; i < listBcssDevices.size(); i++) {
                    BcssDevice device = listBcssDevices.get(i);
                    int rowNum = i + 4; // Excel行号：数据索引 + 标题行(2) + 表头行(1) + Excel从1开始计数
                    
                    // 跳过完全空行：检查所有主要字段是否都为空
                    if (isCompletelyEmptyRow(device)) {
                        log.info("第{}行为完全空行，跳过", rowNum);
                        continue;
                    }
                    
                    // 手动处理@Dict转换失败的情况：将数值字符串转换为对应的ID
                    device = handleDictConversion(device, rowNum);
                    
                    // 添加调试日志，查看@Dict转换后的实际值
                    log.info("第{}行数据: name=[{}], plcId=[{}], location=[{}], isActive=[{}]", 
                        rowNum, device.getName(), device.getPlcId(), device.getLocation(), device.getIsActive());
                    
                    // 1. 必填字段非空校验
                    if (oConvertUtils.isEmpty(device.getName()) || device.getName().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：设备名称不能为空");
                        continue;
                    }
                    
                    if (oConvertUtils.isEmpty(device.getLocation()) || device.getLocation().trim().isEmpty()) {
                        errorMsgList.add("第" + rowNum + "行：设备位置不能为空");
                        continue;
                    }
                    
                    // 2. 校验PLC ID是否有效（框架已自动完成名称到ID的转换）
                    if (oConvertUtils.isEmpty(device.getPlcId())) {
                        errorMsgList.add("第" + rowNum + "行：所属PLC名称不能为空");
                        continue;
                    }
                    
                    // 检查是否是有效的PLC ID（雪花算法ID格式：18-19位数字）
                    // 如果不是，说明@Dict转换失败，PLC名称不存在
                    if (!device.getPlcId().matches("\\d{18,19}")) {
                        errorMsgList.add("第" + rowNum + "行：PLC名称'" + device.getPlcId() + "'不存在，请检查PLC名称是否正确");
                        continue;
                    }
                    
                    // 3. 校验设备名称在Excel中的唯一性
                    if (deviceNamesInExcel.contains(device.getName())) {
                        errorMsgList.add("第" + rowNum + "行：设备名称'" + device.getName() + "'在Excel中重复");
                        continue;
                    }
                    deviceNamesInExcel.add(device.getName());
                    
                    // 4. 校验设备名称在数据库中的唯一性
                    QueryWrapper<BcssDevice> deviceNameQuery = new QueryWrapper<>();
                    deviceNameQuery.eq("name", device.getName())
                                   .eq("del_flag", 0);
                    long deviceNameCount = this.count(deviceNameQuery);
                    if (deviceNameCount > 0) {
                        errorMsgList.add("第" + rowNum + "行：设备名称'" + device.getName() + "'已存在");
                        continue;
                    }
                    
                    // 5. 校验"是否启用"字段的有效性
                    if (device.getIsActive() != null) {
                        // 是否启用校验 (1-启用, 0-停用)
                        if (device.getIsActive() != 1 && device.getIsActive() != 0) {
                            errorMsgList.add("第" + rowNum + "行：是否启用值无效，应填写'启用'或'停用'");
                            continue;
                        }
                    }
                    
                    // 6. 设置默认值和系统字段
                    if (device.getStatus() == null) {
                        device.setStatus(1); // 默认状态：1-正常
                    }
                    
                    if (device.getIsActive() == null) {
                        device.setIsActive(1); // 默认启用：1-启用
                    }
                    
                    // 设置创建人信息和租户ID
                    org.jeecg.common.system.vo.LoginUser sysUser = (org.jeecg.common.system.vo.LoginUser) org.apache.shiro.SecurityUtils.getSubject().getPrincipal();
                    if (sysUser != null) {
                        device.setCreateBy(sysUser.getUsername());
                        device.setUpdateBy(sysUser.getUsername());
                        device.setTenantId(sysUser.getRelTenantIds());
                    }
                    
                    // 校验通过，添加到有效数据列表
                    validDevices.add(device);
                }
                
                // 如果有错误，返回错误信息
                if (!errorMsgList.isEmpty()) {
                    return Result.error("导入失败：\n" + String.join("\n", errorMsgList));
                }
                
                // 批量保存数据
                if (!validDevices.isEmpty()) {
                    this.saveBatch(validDevices);
                }
                
                return Result.OK("导入成功，共导入" + validDevices.size() + "条数据");
                
            } catch (Exception e) {
                log.error("导入失败：", e);
                
                // 检查是否是字典值错误导致的异常
                if (e.getMessage() != null && e.getMessage().equals("Excel 值获取失败")) {
                    String errorMsg = "导入失败：Excel中存在无效的枚举值。\n\n";
                    errorMsg += "请检查以下字段的值是否正确：\n";
                    errorMsg += "• 设备类型：必须是有效的设备类型值\n";
                    errorMsg += "• 状态：必须是 正常 或 异常\n";
                    errorMsg += "• 是否启用：必须是 启用 或 停用\n\n";
                    errorMsg += "请修正Excel中的错误值后重新导入。";
                    return Result.error(errorMsg);
                }
                
                return Result.error("导入失败：" + e.getMessage());
            }
        }
        
        return Result.error("请选择要导入的文件");
    }

    /**
     * 手动处理@Dict转换失败的情况
     * 当Excel中的数值被解析为"11.0"时，@Dict无法匹配数据库中的"11"
     * 需要手动查找并转换
     */
    private BcssDevice handleDictConversion(BcssDevice device, int rowNum) {
        // 处理PLC名称转换
        if (device.getPlcId() != null && !device.getPlcId().matches("\\d{18,19}")) {
            String plcName = device.getPlcId().trim();
            // 如果是数值格式（如"11.0"），去掉小数点
            if (plcName.matches("\\d+\\.0")) {
                plcName = plcName.substring(0, plcName.indexOf("."));
            }
            
            log.info("第{}行：尝试查找PLC名称 '{}'", rowNum, plcName);
            
            // 查询数据库中匹配的PLC
            QueryWrapper<BcssPlc> plcQuery = new QueryWrapper<>();
            plcQuery.eq("name", plcName)
                    .eq("del_flag", 0)
                    .eq("status", 1);
            BcssPlc plc = bcssPlcService.getOne(plcQuery);
            
            if (plc != null) {
                log.info("第{}行：找到PLC '{}' -> ID: {}", rowNum, plcName, plc.getId());
                device.setPlcId(plc.getId());
            } else {
                log.warn("第{}行：未找到PLC名称 '{}'", rowNum, plcName);
            }
        }
        
        return device;
    }

    /**
     * 检查是否为完全空行
     * 只检查用户实际填写的核心业务字段
     */
    private boolean isCompletelyEmptyRow(BcssDevice device) {
        return oConvertUtils.isEmpty(device.getName()) &&
               oConvertUtils.isEmpty(device.getPlcId()) &&
               oConvertUtils.isEmpty(device.getLocation()) &&
               device.getDeviceType() == null &&
               oConvertUtils.isEmpty(device.getStationId()) &&
               oConvertUtils.isEmpty(device.getDeviceCode()) &&
               device.getStatus() == null &&
               device.getIsActive() == null;
    }

}
