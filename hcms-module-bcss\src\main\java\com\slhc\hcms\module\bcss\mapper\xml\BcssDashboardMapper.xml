<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.slhc.hcms.module.bcss.mapper.BcssDashboardMapper">
<!--    <resultMap id="BcssDashBoard" type="com.slhc.hcms.module.bcss.entity.BcssDashboard" >-->
<!--        <result column="Titile" property="Titile" jdbcType="VARCHAR"/>-->
<!--    </resultMap>-->

    <!-- 根据站点ID查询相关任务 -->
    <select id="selectTasksByStationCode" resultType="com.slhc.hcms.module.bcss.entity.BcssTask">
        SELECT *
        FROM bcss_task
        WHERE del_flag = 0  <!-- 只查询未删除的任务 -->
        AND (sour_station_id = #{stationCode} OR dest_station_id = #{stationCode})
        ORDER BY create_time DESC  <!-- 按创建时间倒序 -->
    </select>
    <!-- 根据站点编号查询相关设备 -->
    <select id="selectDevicesByStationCode" resultType="com.slhc.hcms.module.bcss.entity.BcssDevice">
        SELECT *
        FROM bcss_device
        WHERE del_flag = 0  <!-- 只查询未删除的设备 -->
        AND station_id = #{stationCode}
        ORDER BY create_time DESC  <!-- 按创建时间倒序 -->
    </select>


</mapper>