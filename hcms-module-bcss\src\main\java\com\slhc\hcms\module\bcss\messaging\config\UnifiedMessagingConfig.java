package com.slhc.hcms.module.bcss.messaging.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.slhc.hcms.module.bcss.messaging.handler.UnifiedMessageDispatcher;
import com.slhc.hcms.module.infra.messaging.core.MessageErrorHandler;
import com.slhc.hcms.module.infra.messaging.core.MessageRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 统一消息系统配置类
 * 配置消息系统的所有组件
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Configuration
@EnableAsync
@EnableScheduling
@EnableAspectJAutoProxy
@ComponentScan(basePackages = {
        "com.slhc.hcms.module.infra.messaging",
        "com.slhc.hcms.module.bcss.messaging",
        "com.slhc.hcms.module.infra.messaging.bridge"
})
public class UnifiedMessagingConfig {

    /**
     * 配置ObjectMapper用于消息序列化
     */
    @Bean
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper objectMapper() {
        return Jackson2ObjectMapperBuilder.json()
                .modules(new JavaTimeModule())
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .build();
    }

    /**
     * 配置消息错误处理器
     */
    @Bean
    @ConditionalOnMissingBean(MessageErrorHandler.class)
    public MessageErrorHandler messageErrorHandler() {
        // TODO: 实现自定义错误处理器
        return null; // 使用默认处理器
//        return new BcssMessageErrorHandler();
    }

    /**
     * 配置消息注册中心
     */
    @Bean
    @ConditionalOnMissingBean(MessageRegistry.class)
    public MessageRegistry messageRegistry() {
        return new MessageRegistry();
    }

    /**
     * 配置统一消息分发器
     */
    @Bean
    @ConditionalOnMissingBean(UnifiedMessageDispatcher.class)
    public UnifiedMessageDispatcher unifiedMessageDispatcher() {
        return new UnifiedMessageDispatcher();
    }
}
