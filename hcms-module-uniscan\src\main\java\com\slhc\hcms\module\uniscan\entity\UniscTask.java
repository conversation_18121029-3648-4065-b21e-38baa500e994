package com.slhc.hcms.module.uniscan.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: UniScan任务表
 * @Author: jeecg-boot
 * @Date:   2025-09-22
 * @Version: V1.0
 */
@Schema(description="UniScan任务表")
@Data
@TableName("pa_unisc_task")
public class UniscTask implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**删除状态*/
    @Schema(description = "删除状态")
    @TableLogic
    private Integer delFlag;
    /**摆药数量**/
    @Excel(name = "摆药数量", width = 15)
    @Schema(description = "摆药数量")
    private Integer count;
	/**处方单号*/
	@Excel(name = "处方单号", width = 15)
    @Schema(description = "处方单号")
    private String prescriptionNumber;
	/**状态 1:已创建、2:进行中、3:已完成*/
    @Excel(name = "回溯状态", width = 15, dicCode = "unisc_task_status")
    @Schema(description = "回溯状态")
    @Dict(dicCode = "unisc_task_status")
    private Integer status;
	/**UniScan 设备名称*/
	@Excel(name = "UniScan 设备名称", width = 15)
    @Schema(description = "UniScan 设备名称")
    private String deviceName;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
	/**租户Id*/
    @Schema(description = "租户Id")
    private String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
}
