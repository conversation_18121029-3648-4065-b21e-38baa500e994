const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./browserAll-DGIeGU6h.js","./webworkerAll-3xkLBNn_.js","./CanvasPool-4QqOkCau.js","./colorToUniform-CCOUWOPT.js","./element-plus-D-V1KzVw.js","./Meta2DCanvas-9N-Tmw7R.js","./index-CD6ICDO9.js","./index-Bu4re2iw.css","./meta2d-DtcXQZZw.js","./_plugin-vue_export-helper-DlAUqK2U.js","./Meta2DCanvas-CCe9ZYSf.css","./el-button-CcaTBLwS.css","./el-form-item-DCFsf57O.css","./el-checkbox-DIj50LEB.css","./WebGPURenderer-BQmoXWXq.js","./SharedSystems-DYrBym79.js","./WebGLRenderer-0icBlQOH.js"])))=>i.map(i=>d[i]);
var ac=Object.defineProperty;var oc=(s,e,t)=>e in s?ac(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var Ie=(s,e,t)=>oc(s,typeof e!="symbol"?e+"":e,t);/* empty css                  */import{r as U,d as Ze,a as O,h as w,Z as za,w as P,F as J,k as ie,_ as Ga,f as M,$ as Bi,t as z,o as I,a0 as lc,e as ti,q as fn,A as gi,x as Ei,y as Ri,p as mn,a1 as Ya,a2 as mr,a3 as pn,a4 as Xa,M as Xe,b as cc,j as ii,m as N,u as D,a5 as hc,a6 as Gt,a7 as ja,a8 as Yt,a9 as Tt,aa as Ha,ab as uc,ac as dc,ad as Wa,g as Q,ae as Qa,af as W,ag as fc,z as j,B as jt,ah as mc,ai as Ii,aj as Di,ak as pc,al as pr,am as _c,an as cs,ao as _n,ap as gc,v as Ge,aq as Ka,D as Ja,ar as fi,as as yc,at as vc,au as Za,av as xc,aw as bc,ax as wc,ay as At,az as Ct,aA as Ac,aB as Cc,aC as Tc,aD as kc,c as $i,aE as Pc,E as Mc,aF as Sc,aG as Ec,aH as Rc,aI as Ic,aJ as Dc,aK as Br,aL as Nr}from"./element-plus-D-V1KzVw.js";import{a as vt,u as ni,b as Oi,s as tn,S as dt,c as Oc,M as $c}from"./Meta2DCanvas-9N-Tmw7R.js";import{d as Vc,_ as Bn,b as Fc,r as Lc,a as Uc,u as Bc}from"./index-CD6ICDO9.js";import{s as is,d as Nn,a as Ni,f as $s,b as Vs,c as Nc,r as qc,M as hs,E as Vt}from"./meta2d-DtcXQZZw.js";import{_ as Be}from"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css                     *//* empty css                    */const eo=Vc("ui",()=>{const s=U("properties");return{activeRightTab:s,setActiveRightTab:t=>{s.value=t}}});var Hi;(function(s){s[s.Add=0]="Add",s[s.Replace=1]="Replace",s[s.ReplaceAll=2]="ReplaceAll"})(Hi||(Hi={}));let Fs=["fontSize","nameGap","margin","width","symbolSize","itemWidth","itemHeight","fontWeight","top","left","right","bottom","zoom","edgeSymbolSize","nodeWidth","nodeGap","distance","length","length2","offsetCenter","size","symbolOffset","padding","barWidth","symbolOffset","shadowOffsetY","shadowOffsetX"];function zc(s){var n,r;let e=globalThis.echarts;if(!s.echarts||!e)return;if(typeof s.echarts=="string")try{s.echarts=JSON.parse(s.echarts)}catch{}Fs=((n=s.calculative.canvas.store.options.diagramOptions.chart)==null?void 0:n.keyWords)||Fs,s.onDestroy||(s.onDestroy=Yc,s.onMove=In,s.onResize=Xc,s.onRotate=In,s.onValue=Hc,s.onBeforeValue=Wc,s.onBinds=Kc,s.onMouseEnter=In,s.onRenderPenRaw=us,s.onScale=jc),s.calculative.singleton||(s.calculative.singleton={});const t=new Path2D,i=s.calculative.worldRect;if(!s.calculative.singleton.div){const a=document.createElement("div");a.style.position="absolute",a.style.outline="none",a.style.left="-9999px",a.style.top="-9999px",a.style.width=i.width+"px",a.style.height=i.height+"px",document.body.appendChild(a),(r=s.calculative.canvas.externalElements)==null||r.parentElement.appendChild(a),is(s,a),s.calculative.singleton.div=a,s.calculative.singleton.echart=e.init(a,s.echarts.theme),Gc(s),s.calculative.singleton.echartsReady=!0,s.echarts.geoName&&!e.getMap(s.echarts.geoName)&&(s.echarts.geoJson?e.registerMap(s.echarts.geoName,s.echarts.geoJson):s.echarts.geoUrl&&(s.calculative.singleton.echartsReady=!1,fetch(s.echarts.geoUrl).then(o=>{o.text().then(c=>{if(typeof c=="string")try{c=JSON.parse(c)}catch{}if(c.constructor!==Object&&c.constructor!==Array){console.warn("Invalid data:",c);return}e.registerMap(s.echarts.geoName,c),s.calculative.singleton.echartsReady=!0,s.calculative.singleton.echart.setOption(qn(s.echarts.option,s.calculative.canvas.store.data.scale),!0),s.calculative.singleton.echart.resize(),setTimeout(()=>{us(s)},300)})}))),s.calculative.singleton.echartsReady&&setTimeout(()=>{s.calculative.singleton.echart.setOption(qn(s.echarts.option,s.calculative.canvas.store.data.scale),!0),setTimeout(()=>us(s),300)})}return t}function Gc(s){var i;const e=s.calculative.singleton.echart,t=["click","dblclick","mousedown","mousemove","mouseup","mouseover","mouseout","globalout","contextmenu"];t.forEach(n=>{e.off(n)}),(i=s.events)==null||i.forEach(n=>{n.actions&&n.actions.length&&t.includes(n.name)&&e.on(n.name,r=>{let a=!1;n.conditions&&n.conditions.length?n.conditionType==="and"?a=n.conditions.every(o=>s.calculative.canvas.parent.judgeCondition(s,o.key,o)):n.conditionType==="or"&&(a=n.conditions.some(o=>s.calculative.canvas.parent.judgeCondition(s,o.key,o))):a=!0,a&&n.actions.forEach(o=>{if(o.timeout){let c=setTimeout(()=>{s.calculative.canvas.parent.events[o.action]&&(s.calculative.canvas.parent.events[o.action](s,o,r),clearTimeout(c),c=null)},o.timeout)}else s.calculative.canvas.parent.events[o.action]&&s.calculative.canvas.parent.events[o.action](s,o,r)})})})}function Yc(s){if(s.calculative.singleton&&s.calculative.singleton.div){s.calculative.singleton.div.remove();let e=globalThis.echarts;e&&e.dispose(s.calculative.singleton.echart),delete s.calculative.singleton.div,delete s.calculative.singleton.echart}}function In(s){s.calculative.singleton.div&&is(s,s.calculative.singleton.div)}function Xc(s){var e;In(s),(e=s.calculative.singleton)!=null&&e.echart&&s.calculative.singleton.echart.resize()}function jc(s){var t,i;if(!s.calculative.singleton.echart)return;let e=globalThis.echarts;if(is(s,s.calculative.singleton.div),!(s.echarts.geoName&&!e.getMap(s.echarts.geoName))){if(!s.echarts.diabled){if((t=s.echarts.option)!=null&&t.dataZoom){const r=s.calculative.singleton.echart.getOption().dataZoom;(i=s.echarts.option.dataZoom)==null||i.forEach((a,o)=>{r[o]&&(a.start=r[o].start,a.end=r[o].end)})}s.calculative.singleton.echart.setOption(qn(s.echarts.option,s.calculative.canvas.store.data.scale),!0)}s.calculative.singleton.echart.resize()}}function Hc(s){var e,t;if(s.calculative.singleton.echart&&(is(s,s.calculative.singleton.div),s.calculative.singleton.echartsReady))if(s.calculative.partialOption){const i=s.calculative.partialOption.echarts.option;(Array.isArray((e=s.echarts)==null?void 0:e.replaceMerge)?(t=s.echarts)==null?void 0:t.replaceMerge.some(r=>i[r]):!1)?s.calculative.singleton.echart.setOption(Nn(i),{replaceMerge:s.echarts.replaceMerge}):s.calculative.singleton.echart.setOption(Nn(i))}else s.calculative.singleton.echart.setOption(qn(s.echarts.option,s.calculative.canvas.store.data.scale),!0)}function Wc(s,e){if(s.calculative.partialOption=null,e.echarts){let g=globalThis.echarts;return e.echarts.geoName&&!g.getMap(e.echarts.geoName)&&(e.echarts.geoJson?g.registerMap(e.echarts.geoName,e.echarts.geoJson):e.echarts.geoUrl&&(s.calculative.singleton.echartsReady=!1,fetch(e.echarts.geoUrl).then(d=>{d.text().then(m=>{if(typeof m=="string")try{m=JSON.parse(m)}catch{}if(m.constructor!==Object&&m.constructor!==Array){console.warn("Invalid data:",m);return}return g.registerMap(e.echarts.geoName,m),s.calculative.singleton.echartsReady=!0,s.onValue(s),!1})}))),e}if(s.realTimes&&s.realTimes.length){s.echarts.dataMap&&e.data&&(e=Qc(s,e));let g=Object.keys(e);const{xAxis:d,yAxis:m}=s.echarts.option,{max:_,replaceMode:x,timeFormat:b}=s.echarts;let T=[],k=!1;for(let C in e)if(C.includes("echarts.option")){k=!0;let y=Ni(s,C);if(Array.isArray(y)&&x===Hi.Add&&(y.push(e[C]),_&&y.splice(0,y.length-_),e[C]=y,!g.includes("echarts.option.xAxis.data"))){let v="echarts.option.xAxis.data";Array.isArray(d)&&d.length&&(v="echarts.option.xAxis.0.data");let A=Ni(s,v),E=$s(b||"`${hours}:${minutes}:${seconds}`");A.push(E),_&&A.splice(0,A.length-_),e[v]=A}if(C.includes(".data.")){let v=C.substring(0,C.indexOf(".data.")+5);T.includes(v)||T.push(v)}}if(k){const C=Nn(e);s.calculative.partialOption=Jc(C,s),T.forEach(y=>{let v=Ni(s,y);Vs(s.calculative.partialOption,y,v)})}return e}if(!e.dataX&&!e.dataY)return e;const t=s.echarts,{max:i,replaceMode:n}=t;let r=e.dataX,a=e.dataY,o=[];a&&o.push("echarts.option.series");const c=t.option.series,l=c.length,{xAxis:h,yAxis:u}=t.option;Array.isArray(h)&&h.length>1&&console.warn("echarts 只支持单 x 轴，多 x 轴将被忽略");const f=Array.isArray(h)?h[0]:h,p=Array.isArray(u)?u[0]:u;if(n)if(n===Hi.Replace){if(!f&&!p)a&&(l===1?(!Array.isArray(a)&&(a=[a]),a.forEach((g,d)=>{const m=c[0].data.find(_=>_.name===g.name);m&&(m.value=g.value)})):c.forEach((g,d)=>{Array.isArray(a[d])||(a[d]=[a[d]]),a[d].forEach((m,_)=>{const x=g.data.find(b=>b.name===m.name);x&&(x.value=m.value)})}));else if((f.type==="category"||p.type==="category")&&r&&a){const g=f.type==="category"?f.data:p.data;!Array.isArray(r)&&(r=[r]),!Array.isArray(a)&&(a=[a]),f.type==="category"?o.push("echarts.option.xAxis"):o.push("echarts.option.yAxis"),l===1?a.forEach((d,m)=>{const _=g.indexOf(r[m]);c[0].data[_]=d}):c.forEach((d,m)=>{a[m].forEach((_,x)=>{const b=g.indexOf(r[x]);d.data[b]=_})})}}else n===Hi.ReplaceAll&&(r&&(f.data=r,f.data.splice(0,f.data.length-i),o.push("echarts.option.xAxis")),a&&(l===1?(c[0].data=a,c[0].data.splice(0,c[0].data.length-i)):c.forEach((g,d)=>{g.data=a[d],g.data.splice(0,g.data.length-i)})));else{if(r){!Array.isArray(r)&&(r=[r]);const g=f.data;g.push(...r),g.splice(0,g.length-i),o.push("echarts.option.xAxis")}if(a)if(l===1){!Array.isArray(a)&&(a=[a]);const g=c[0].data;g.push(...a),g.splice(0,g.length-i)}else c.forEach((g,d)=>{Array.isArray(a[d])||(a[d]=[a[d]]);const m=g.data;m.push(...a[d]),m.splice(0,m.length-i)})}return s.calculative.partialOption={},o.forEach(g=>{let d=Ni(s,g);Vs(s.calculative.partialOption,g,d)}),delete e.dataX,delete e.dataY,Object.assign(e,{echarts:t})}function Qc(s,e){var t,i;if(!s.echarts.dataMap||!e.data)return e;if(e.data){let n={};if(Array.isArray(e.data))for(const r in s.echarts.dataMap)s.echarts.dataMap.hasOwnProperty(r)&&((t=s.echarts.timeKeys)!=null&&t.length&&s.echarts.timeKeys.includes(s.echarts.dataMap[r])?n[r]=e.data.map(a=>$s(s.echarts.timeFormat,a[s.echarts.dataMap[r]])):n[r]=e.data.map(a=>a[s.echarts.dataMap[r]]));else for(const r in s.echarts.dataMap)s.echarts.dataMap.hasOwnProperty(r)&&((i=s.echarts.timeKeys)!=null&&i.length&&s.echarts.timeKeys.includes(s.echarts.dataMap[r])?n[r]=$s(s.echarts.timeFormat,e.data[s.echarts.dataMap[r]]):n[r]=e.data[s.echarts.dataMap[r]]);return delete e.data,Object.assign(e,n),e}}function Kc(s,e,t){if(t.key!=="dataY")return;const i=s.echarts,{xAxis:n,yAxis:r}=i.option;Array.isArray(n)&&n.length>1&&console.warn("echarts 只支持单 x 轴，多 x 轴将被忽略");const a=Array.isArray(n)?n[0]:n,o=Array.isArray(r)?r[0]:r,c=i.option.series;if(!a&&!o){const l=[];if(Array.isArray(c)&&c.length===1)return c[0].data.forEach(h=>{const{dataId:u}=t.dataIds.find(f=>f.name===h.name);if(u){const f=e.find(p=>p.dataId===u);f&&l.push({name:h.name,value:f.value})}}),{id:s.id,dataY:l}}else if(a.type==="category"||o.type==="category"){const l=[],h=[],u=a.type==="category"?a.data:o.data;return u==null||u.forEach(f=>{const{dataId:p}=t.dataIds.find(g=>g.name===f);if(p){const g=e.find(d=>d.dataId===p);g&&(h.push(f),l.push(g.value))}}),{id:s.id,dataY:l,dataX:h}}else if(a.type==="time"){const l=[],h=+new Date;let u=!1;if(c.forEach((f,p)=>{const g=[],{dataId:d}=t.dataIds.find(m=>m.name===f.name);if(d){const m=e.find(_=>_.dataId===d);m&&(g.push([h,m.value]),u=!0)}l[p]=g}),u)l.forEach((f,p)=>{if(!f||f.length===0){const g=c[p].data[c[p].data.length-1];l[p]=[[h,g[1]]]}});else return;return{id:s.id,dataY:l.length===1?l[0]:l}}}function us(s){var t,i;const e=new Image;e.src=(i=(t=s.calculative.singleton)==null?void 0:t.echart)==null?void 0:i.getDataURL({pixelRatio:2}),s.calculative.img=e}function qn(s,e){const t=Nn(s);if(t.dataZoom){let i=["right","top","width","height","left","bottom"];for(let n=0;n<i.length;n++)t.dataZoom.forEach(r=>{isNaN(r[i[n]])||(r[i[n]]*=e)})}return Nc(t,Fs,e),t}function Jc(s,e){const t={};return Object.keys(s).forEach(i=>{const n=i.split(".");let r=t;n.forEach((a,o)=>{const c=!isNaN(parseInt(a));if(o===6){let l=n.slice(0,7).join(".");Vs(e,i,s[i]);let h=Ni(e,l);r[n[o]]=h}else{if(o>6)return;if(o===n.length-1)c?(Array.isArray(r)||(r=[]),r[parseInt(a)]=s[i]):r[a]=s[i];else if(c){const l=parseInt(a);if(Array.isArray(r)||r[n[o-1]],r[l]||(r[l]={}),Array.isArray(r))for(let h=0;h<parseInt(a);h++)r[h]||(r[h]={});r=r[l]}else r[a]||(a==="series"?r[a]=[]:r[a]={}),r=r[a]}})}),t}function Zc(s){qc({echarts:zc})}const eh={class:"graphics"},th={class:"graphic-container"},ih=["onDragstart","onClick"],nh={class:"l-icon","aria-hidden":"true"},sh=["xlink:href"],rh=["title"],ah=Ze({__name:"Graphics",setup(s){console.log("ECharts:",window.echarts),Zc();const e=U(["基本形状","流程图","活动图","时序图和类图","故障树","脑图","ECHART","箱式物流"]),t=[{name:"基本形状",show:!0,list:[{name:"正方形",icon:"l-rect",id:1,data:{width:100,height:100,name:"square",text:"正方形"}},{name:"矩形",icon:"l-rectangle",id:2,data:{width:200,height:50,borderRadius:.1,name:"rectangle",text:"矩形"}},{name:"圆",icon:"l-circle",id:3,data:{width:100,height:100,name:"circle",text:"圆"}},{name:"三角形",icon:"l-triangle",id:4,data:{width:100,height:100,name:"triangle",text:"三角形"}},{name:"菱形",icon:"l-diamond",id:5,data:{width:100,height:100,name:"diamond",text:"菱形"}},{name:"五边形",icon:"l-pentagon",id:6,data:{width:100,height:100,name:"pentagon",text:"五边形"}},{name:"六边形",icon:"l-hexagon",id:7,data:{width:100,height:100,name:"hexagon",text:"六边形"}},{name:"五角星",icon:"l-pentagram",id:8,data:{width:100,height:100,name:"pentagram",text:"五角星"}},{name:"左箭头",icon:"l-arrow-left",id:9,data:{width:120,height:60,name:"leftArrow",text:"左箭头"}},{name:"右箭头",icon:"l-arrow-right",id:10,data:{width:120,height:60,name:"rightArrow",text:"右箭头"}},{name:"双向箭头",icon:"l-twoway-arrow",id:11,data:{width:150,height:60,name:"twowayArrow",text:"双向箭头"}},{name:"云",icon:"l-cloud",id:13,data:{width:100,height:100,name:"cloud",text:"云"}},{name:"消息框",icon:"l-msg",id:14,data:{textTop:-.1,width:100,height:100,name:"message",text:"消息框"}},{name:"文件",icon:"l-file",id:15,data:{width:80,height:100,name:"file",text:"文件"}},{name:"立方体",icon:"l-cube",id:18,data:{width:60,height:100,name:"cube",z:.25,text:"立方体",props:{custom:[{key:"z",label:"Z",type:"number",min:0,placeholder:"<= 1 即宽度的比例"},{key:"backgroundFront",label:"前背景色",type:"color"},{key:"backgroundUp",label:"顶背景色",type:"color"},{key:"backgroundRight",label:"右背景色",type:"color"}]}}},{name:"人",icon:"l-people",id:19,data:{width:70,height:100,name:"people",text:"人"}}]},{name:"流程图",show:!0,list:[{name:"开始/结束",icon:"l-flow-start",id:21,data:{text:"开始/结束",width:120,height:40,borderRadius:.5,name:"rectangle"}},{name:"流程",icon:"l-rectangle",id:22,data:{text:"流程",width:120,height:40,name:"rectangle"}},{name:"判定",icon:"l-diamond",id:23,data:{text:"判定",width:120,height:60,name:"diamond"}},{name:"数据",icon:"l-flow-data",id:24,data:{text:"数据",width:120,height:50,name:"flowData",offsetX:.14}},{name:"准备",icon:"l-flow-ready",id:25,data:{text:"准备",width:120,height:50,name:"hexagon"}},{name:"子流程",icon:"l-flow-subprocess",id:26,data:{text:"子流程",width:120,height:50,name:"flowSubprocess"}},{name:"数据库",icon:"l-db",id:27,data:{text:"数据库",width:80,height:120,name:"flowDb"}},{name:"文档",icon:"l-flow-document",id:28,data:{text:"文档",width:120,height:100,name:"flowDocument"}},{name:"内部存储",icon:"l-internal-storage",id:29,data:{text:"内部存储",width:120,height:80,name:"flowInternalStorage"}},{name:"外部存储",icon:"l-extern-storage",id:30,data:{text:"外部存储",width:120,height:80,name:"flowExternStorage"}},{name:"队列",icon:"l-flow-queue",id:31,data:{text:"队列",width:100,height:100,name:"flowQueue"}},{name:"手动输入",icon:"l-flow-manually",id:32,data:{text:"手动输入",width:120,height:80,name:"flowManually"}},{name:"展示",icon:"l-flow-display",id:33,data:{text:"展示",width:120,height:80,name:"flowDisplay"}},{name:"并行模式",icon:"l-flow-parallel",id:34,data:{text:"并行模式",width:120,height:50,name:"flowParallel"}},{name:"注释",icon:"l-flow-comment",id:35,data:{text:"注释",width:100,height:100,name:"flowComment"}}]},{name:"活动图",show:!0,list:[{name:"开始",icon:"l-inital",id:36,data:{text:"开始",width:30,height:30,name:"circle",background:"#555",lineWidth:0}},{name:"结束",icon:"l-final",id:37,data:{width:30,height:30,name:"activityFinal",text:"结束"}},{name:"活动",icon:"l-action",id:38,data:{text:"活动",width:120,height:50,borderRadius:.25,name:"rectangle"}},{name:"决策/合并",icon:"l-diamond",id:39,data:{text:"决策/合并",width:120,height:50,name:"diamond"}},{name:"垂直泳道",icon:"l-swimlane-v",id:40,data:{text:"垂直泳道",width:200,height:500,name:"swimlaneV",textBaseline:"top",textTop:20,lineTop:.08}},{name:"水平泳道",icon:"l-swimlane-h",id:41,data:{text:"水平泳道",width:500,height:200,name:"swimlaneH",textWidth:.01,textLeft:.04,textAlign:"left",lineLeft:.08}},{name:"垂直分岔/汇合",icon:"l-fork-v",id:42,data:{text:"垂直分岔/汇合",width:10,height:150,name:"forkV",fillStyle:"#555",strokeStyle:"transparent"}},{name:"水平分岔/汇合",icon:"l-fork",id:43,data:{text:"水平分岔/汇合",width:150,height:10,name:"forkH",fillStyle:"#555",strokeStyle:"transparent"}}]},{name:"时序图和类图",show:!0,list:[{name:"生命线",icon:"l-lifeline",id:44,data:{text:"生命线",width:150,height:400,textHeight:50,name:"lifeline"}},{name:"激活",icon:"l-focus",id:45,data:{text:"激活",width:12,height:200,name:"sequenceFocus"}},{name:"简单类",icon:"l-simple-class",id:46,data:{text:"Topolgoy",width:270,height:200,textHeight:200,name:"simpleClass",textAlign:"center",textBaseline:"top",textTop:10,list:[{text:`- name: string
+ setName(name: string): void`}]}},{name:"类",icon:"l-class",id:47,data:{text:"Topolgoy",width:270,height:200,textHeight:200,name:"interfaceClass",textAlign:"center",textBaseline:"top",textTop:10,list:[{text:"- name: string"},{text:"+ setName(name: string): void"}]}}]},{name:"故障树",show:!0,list:[{name:"与门",icon:"l-ANDmen",data:{name:"andGate",width:100,height:150,text:"与门"}},{name:"基本事件",icon:"l-jibenshijian",data:{name:"basicEvent",width:100,height:150,text:"基本事件"}},{name:"未展开事件",icon:"l-weizhankaishijian",data:{name:"unexpandedEvent",width:100,height:150,text:"未展开事件"}},{name:"优先AND门",icon:"l-youxianANDmen",data:{name:"priorityAndGate",width:100,height:150,text:"优先AND门"}},{name:"禁止门",icon:"l-jinzhimen",data:{name:"forbiddenGate",width:100,height:150,text:"禁止门"}},{name:"事件",icon:"l-shijian",data:{name:"event",width:100,height:150,text:"事件"}},{name:"开关事件",icon:"l-kaiguanshijian",data:{name:"switchEvent",width:100,height:150,text:"开关事件"}},{name:"条件事件",icon:"l-tiaojianshijian",data:{name:"conditionalEvent",width:150,height:100,text:"条件事件"}},{name:"转移符号",icon:"l-zhuanyifuhao",data:{name:"transferSymbol",width:100,height:150,text:"转移符号"}},{name:"或门",icon:"l-ORmen",data:{name:"orGate",width:100,height:150,text:"或门"}},{name:"异或门",icon:"l-yihuomen",data:{name:"xorGate",width:100,height:150,text:"异或门"}},{name:"表决门",icon:"l-biaojuemen",data:{name:"votingGate",width:100,height:150,text:"表决门"}}]},{name:"脑图",show:!0,list:[{name:"主题",icon:"l-zhuti",data:{text:"主题",width:200,height:50,name:"mindNode",borderRadius:.5}},{name:"子主题",icon:"l-zizhuti",data:{text:"子主题",width:160,height:40,name:"mindLine"}}]},{name:"ECHART",show:!0,list:[{name:"折线图",icon:"l-line-chart",data:{name:"echarts",width:400,height:300,externElement:!0,disableAnchor:!0,text:"折线图",echarts:{option:{grid:{top:10,bottom:50,left:40,right:5},dataZoom:[{height:16,bottom:10}],xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisLabel:{fontSize:12}},yAxis:{type:"value",axisLabel:{fontSize:12}},series:[{data:[820,932,901,934,1290,1330,1320],type:"line"}]},max:100}}},{name:"柱状图",icon:"l-bar-chart",data:{width:300,height:200,disableAnchor:!0,externElement:!0,name:"echarts",text:"柱状图",echarts:{option:{tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],axisTick:{alignWithLabel:!0}},yAxis:[{type:"value"}],series:[{name:"直接访问",type:"bar",barWidth:"60%",data:[10,52,200,334,390,330,220]}]},max:100}}},{name:"饼图",icon:"l-pie-chart",data:{width:200,height:200,disableAnchor:!0,externElement:!0,name:"echarts",text:"饼图",echarts:{option:{tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{},series:[{name:"访问来源",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{normal:{show:!1,position:"center"},emphasis:{show:!0,textStyle:{fontSize:"30",fontWeight:"bold"}}},labelLine:{normal:{show:!1}},data:[{value:335,name:"直接访问"},{value:310,name:"邮件营销"},{value:234,name:"联盟广告"},{value:135,name:"视频广告"},{value:1548,name:"搜索引擎"}]}]}}}},{name:"仪表盘",icon:"l-dashboard-chart",data:{width:300,height:300,disableAnchor:!0,externElement:!0,name:"echarts",text:"仪表盘",echarts:{option:{tooltip:{formatter:"{a} <br/>{b} : {c}%"},series:[{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}]}}}}]},{name:"箱式物流",show:!0,list:[{name:"站点",icon:"l-dot",id:20,data:{width:80,height:80,name:"square",text:"站点",image:"/src/assets/temp.jpeg"}},{name:"提升机",icon:"l-dot",data:{text:"提升机",width:80,height:80,name:"square",image:"/src/assets/temp.jpeg"}},{name:"轨道",icon:"l-dot",data:{text:"轨道",width:80,height:80,name:"square",image:"/src/assets/temp.jpeg"}},{name:"移栽器",icon:"l-dot",data:{text:"移栽器",width:80,height:80,name:"square",image:"/src/assets/temp.jpeg"}},{name:"箱子",icon:"l-dot",data:{text:"箱子",width:80,height:80,name:"square",image:"/src/assets/box.svg"}},{name:"读卡器",icon:"l-dot",data:{text:"读卡器",width:80,height:80,name:"square",image:"/src/assets/scanner.svg"}}]}],i=(n,r)=>{var a;r&&(n.stopPropagation(),n instanceof DragEvent?(a=n.dataTransfer)==null||a.setData("Meta2d",JSON.stringify(r.data)):hs&&hs.canvas?hs.canvas.addCaches=[r.data]:console.error("Meta2D.canvas is not initialized"))};return(n,r)=>{const a=Ga,o=za;return I(),O("div",eh,[w(o,{modelValue:e.value,"onUpdate:modelValue":r[0]||(r[0]=c=>e.value=c),class:"graphics-collapse"},{default:P(()=>[(I(),O(J,null,ie(t,c=>w(a,{key:c.name,title:c.name,name:c.name},{default:P(()=>[M("div",th,[(I(!0),O(J,null,ie(c.list,l=>(I(),O("div",{key:l.id,class:"graphic",draggable:!0,onDragstart:h=>i(h,l),onClick:Bi(h=>i(h,l),["prevent"])},[(I(),O("svg",nh,[M("use",{"xlink:href":"#"+l.icon},null,8,sh)])),M("p",{title:l.name},z(l.name),9,rh)],40,ih))),128))])]),_:2},1032,["title","name"])),64))]),_:1},8,["modelValue"])])}}}),oh=Be(ah,[["__scopeId","data-v-7c3583f8"]]);var de=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(de||{});const Ls=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s}}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},vn=(s,e)=>Ls(s).priority??e,ht={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(Ls).forEach(e=>{e.type.forEach(t=>{var i,n;return(n=(i=this._removeHandlers)[t])==null?void 0:n.call(i,e)})}),this},add(...s){return s.map(Ls).forEach(e=>{e.type.forEach(t=>{var r,a;const i=this._addHandlers,n=this._queue;i[t]?(a=i[t])==null||a.call(i,e):(n[t]=n[t]||[],(r=n[t])==null||r.push(e))})}),this},handle(s,e,t){var a;const i=this._addHandlers,n=this._removeHandlers;if(i[s]||n[s])throw new Error(`Extension type ${s} already has a handler`);i[s]=e,n[s]=t;const r=this._queue;return r[s]&&((a=r[s])==null||a.forEach(o=>e(o)),delete r[s]),this},handleByMap(s,e){return this.handle(s,t=>{t.name&&(e[t.name]=t.ref)},t=>{t.name&&delete e[t.name]})},handleByNamedList(s,e,t=-1){return this.handle(s,i=>{e.findIndex(r=>r.name===i.name)>=0||(e.push({name:i.name,value:i.ref}),e.sort((r,a)=>vn(a.value,t)-vn(r.value,t)))},i=>{const n=e.findIndex(r=>r.name===i.name);n!==-1&&e.splice(n,1)})},handleByList(s,e,t=-1){return this.handle(s,i=>{e.includes(i.ref)||(e.push(i.ref),e.sort((n,r)=>vn(r,t)-vn(n,t)))},i=>{const n=e.indexOf(i.ref);n!==-1&&e.splice(n,1)})},mixin(s,...e){for(const t of e)Object.defineProperties(s.prototype,Object.getOwnPropertyDescriptors(t))}},lh={extension:{type:de.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await Bn(()=>import("./browserAll-DGIeGU6h.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)}},ch={extension:{type:de.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await Bn(()=>import("./webworkerAll-3xkLBNn_.js"),__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13]),import.meta.url)}};class ve{constructor(e,t,i){this._x=t||0,this._y=i||0,this._observer=e}clone(e){return new ve(e??this._observer,this._x,this._y)}set(e=0,t=e){return(this._x!==e||this._y!==t)&&(this._x=e,this._y=t,this._observer._onUpdate(this)),this}copyFrom(e){return(this._x!==e.x||this._y!==e.y)&&(this._x=e.x,this._y=e.y,this._observer._onUpdate(this)),this}copyTo(e){return e.set(this._x,this._y),e}equals(e){return e.x===this._x&&e.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=${this._x} y=${this._y} scope=${this._observer}]`}get x(){return this._x}set x(e){this._x!==e&&(this._x=e,this._observer._onUpdate(this))}get y(){return this._y}set y(e){this._y!==e&&(this._y=e,this._observer._onUpdate(this))}}var to={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function n(c,l,h){this.fn=c,this.context=l,this.once=h||!1}function r(c,l,h,u,f){if(typeof h!="function")throw new TypeError("The listener must be a function");var p=new n(h,u||c,f),g=t?t+l:l;return c._events[g]?c._events[g].fn?c._events[g]=[c._events[g],p]:c._events[g].push(p):(c._events[g]=p,c._eventsCount++),c}function a(c,l){--c._eventsCount===0?c._events=new i:delete c._events[l]}function o(){this._events=new i,this._eventsCount=0}o.prototype.eventNames=function(){var l=[],h,u;if(this._eventsCount===0)return l;for(u in h=this._events)e.call(h,u)&&l.push(t?u.slice(1):u);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(h)):l},o.prototype.listeners=function(l){var h=t?t+l:l,u=this._events[h];if(!u)return[];if(u.fn)return[u.fn];for(var f=0,p=u.length,g=new Array(p);f<p;f++)g[f]=u[f].fn;return g},o.prototype.listenerCount=function(l){var h=t?t+l:l,u=this._events[h];return u?u.fn?1:u.length:0},o.prototype.emit=function(l,h,u,f,p,g){var d=t?t+l:l;if(!this._events[d])return!1;var m=this._events[d],_=arguments.length,x,b;if(m.fn){switch(m.once&&this.removeListener(l,m.fn,void 0,!0),_){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,h),!0;case 3:return m.fn.call(m.context,h,u),!0;case 4:return m.fn.call(m.context,h,u,f),!0;case 5:return m.fn.call(m.context,h,u,f,p),!0;case 6:return m.fn.call(m.context,h,u,f,p,g),!0}for(b=1,x=new Array(_-1);b<_;b++)x[b-1]=arguments[b];m.fn.apply(m.context,x)}else{var T=m.length,k;for(b=0;b<T;b++)switch(m[b].once&&this.removeListener(l,m[b].fn,void 0,!0),_){case 1:m[b].fn.call(m[b].context);break;case 2:m[b].fn.call(m[b].context,h);break;case 3:m[b].fn.call(m[b].context,h,u);break;case 4:m[b].fn.call(m[b].context,h,u,f);break;default:if(!x)for(k=1,x=new Array(_-1);k<_;k++)x[k-1]=arguments[k];m[b].fn.apply(m[b].context,x)}}return!0},o.prototype.on=function(l,h,u){return r(this,l,h,u,!1)},o.prototype.once=function(l,h,u){return r(this,l,h,u,!0)},o.prototype.removeListener=function(l,h,u,f){var p=t?t+l:l;if(!this._events[p])return this;if(!h)return a(this,p),this;var g=this._events[p];if(g.fn)g.fn===h&&(!f||g.once)&&(!u||g.context===u)&&a(this,p);else{for(var d=0,m=[],_=g.length;d<_;d++)(g[d].fn!==h||f&&!g[d].once||u&&g[d].context!==u)&&m.push(g[d]);m.length?this._events[p]=m.length===1?m[0]:m:a(this,p)}return this},o.prototype.removeAllListeners=function(l){var h;return l?(h=t?t+l:l,this._events[h]&&a(this,h)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=t,o.EventEmitter=o,s.exports=o})(to);var hh=to.exports;const gn=lc(hh),uh=Math.PI*2,dh=180/Math.PI,fh=Math.PI/180;class at{constructor(e=0,t=0){this.x=0,this.y=0,this.x=e,this.y=t}clone(){return new at(this.x,this.y)}copyFrom(e){return this.set(e.x,e.y),this}copyTo(e){return e.set(this.x,this.y),e}equals(e){return e.x===this.x&&e.y===this.y}set(e=0,t=e){return this.x=e,this.y=t,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return ds.x=0,ds.y=0,ds}}const ds=new at;class se{constructor(e=1,t=0,i=0,n=1,r=0,a=0){this.array=null,this.a=e,this.b=t,this.c=i,this.d=n,this.tx=r,this.ty=a}fromArray(e){this.a=e[0],this.b=e[1],this.c=e[3],this.d=e[4],this.tx=e[2],this.ty=e[5]}set(e,t,i,n,r,a){return this.a=e,this.b=t,this.c=i,this.d=n,this.tx=r,this.ty=a,this}toArray(e,t){this.array||(this.array=new Float32Array(9));const i=t||this.array;return e?(i[0]=this.a,i[1]=this.b,i[2]=0,i[3]=this.c,i[4]=this.d,i[5]=0,i[6]=this.tx,i[7]=this.ty,i[8]=1):(i[0]=this.a,i[1]=this.c,i[2]=this.tx,i[3]=this.b,i[4]=this.d,i[5]=this.ty,i[6]=0,i[7]=0,i[8]=1),i}apply(e,t){t=t||new at;const i=e.x,n=e.y;return t.x=this.a*i+this.c*n+this.tx,t.y=this.b*i+this.d*n+this.ty,t}applyInverse(e,t){t=t||new at;const i=this.a,n=this.b,r=this.c,a=this.d,o=this.tx,c=this.ty,l=1/(i*a+r*-n),h=e.x,u=e.y;return t.x=a*l*h+-r*l*u+(c*r-o*a)*l,t.y=i*l*u+-n*l*h+(-c*i+o*n)*l,t}translate(e,t){return this.tx+=e,this.ty+=t,this}scale(e,t){return this.a*=e,this.d*=t,this.c*=e,this.b*=t,this.tx*=e,this.ty*=t,this}rotate(e){const t=Math.cos(e),i=Math.sin(e),n=this.a,r=this.c,a=this.tx;return this.a=n*t-this.b*i,this.b=n*i+this.b*t,this.c=r*t-this.d*i,this.d=r*i+this.d*t,this.tx=a*t-this.ty*i,this.ty=a*i+this.ty*t,this}append(e){const t=this.a,i=this.b,n=this.c,r=this.d;return this.a=e.a*t+e.b*n,this.b=e.a*i+e.b*r,this.c=e.c*t+e.d*n,this.d=e.c*i+e.d*r,this.tx=e.tx*t+e.ty*n+this.tx,this.ty=e.tx*i+e.ty*r+this.ty,this}appendFrom(e,t){const i=e.a,n=e.b,r=e.c,a=e.d,o=e.tx,c=e.ty,l=t.a,h=t.b,u=t.c,f=t.d;return this.a=i*l+n*u,this.b=i*h+n*f,this.c=r*l+a*u,this.d=r*h+a*f,this.tx=o*l+c*u+t.tx,this.ty=o*h+c*f+t.ty,this}setTransform(e,t,i,n,r,a,o,c,l){return this.a=Math.cos(o+l)*r,this.b=Math.sin(o+l)*r,this.c=-Math.sin(o-c)*a,this.d=Math.cos(o-c)*a,this.tx=e-(i*this.a+n*this.c),this.ty=t-(i*this.b+n*this.d),this}prepend(e){const t=this.tx;if(e.a!==1||e.b!==0||e.c!==0||e.d!==1){const i=this.a,n=this.c;this.a=i*e.a+this.b*e.c,this.b=i*e.b+this.b*e.d,this.c=n*e.a+this.d*e.c,this.d=n*e.b+this.d*e.d}return this.tx=t*e.a+this.ty*e.c+e.tx,this.ty=t*e.b+this.ty*e.d+e.ty,this}decompose(e){const t=this.a,i=this.b,n=this.c,r=this.d,a=e.pivot,o=-Math.atan2(-n,r),c=Math.atan2(i,t),l=Math.abs(o+c);return l<1e-5||Math.abs(uh-l)<1e-5?(e.rotation=c,e.skew.x=e.skew.y=0):(e.rotation=0,e.skew.x=o,e.skew.y=c),e.scale.x=Math.sqrt(t*t+i*i),e.scale.y=Math.sqrt(n*n+r*r),e.position.x=this.tx+(a.x*t+a.y*n),e.position.y=this.ty+(a.x*i+a.y*r),e}invert(){const e=this.a,t=this.b,i=this.c,n=this.d,r=this.tx,a=e*n-t*i;return this.a=n/a,this.b=-t/a,this.c=-i/a,this.d=e/a,this.tx=(i*this.ty-n*r)/a,this.ty=-(e*this.ty-t*r)/a,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const e=new se;return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyTo(e){return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyFrom(e){return this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.tx=e.tx,this.ty=e.ty,this}equals(e){return e.a===this.a&&e.b===this.b&&e.c===this.c&&e.d===this.d&&e.tx===this.tx&&e.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return ph.identity()}static get shared(){return mh.identity()}}const mh=new se,ph=new se,Ut=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],Bt=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Nt=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],qt=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],Us=[],io=[],xn=Math.sign;function _h(){for(let s=0;s<16;s++){const e=[];Us.push(e);for(let t=0;t<16;t++){const i=xn(Ut[s]*Ut[t]+Nt[s]*Bt[t]),n=xn(Bt[s]*Ut[t]+qt[s]*Bt[t]),r=xn(Ut[s]*Nt[t]+Nt[s]*qt[t]),a=xn(Bt[s]*Nt[t]+qt[s]*qt[t]);for(let o=0;o<16;o++)if(Ut[o]===i&&Bt[o]===n&&Nt[o]===r&&qt[o]===a){e.push(o);break}}}for(let s=0;s<16;s++){const e=new se;e.set(Ut[s],Bt[s],Nt[s],qt[s],0,0),io.push(e)}}_h();const ee={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>Ut[s],uY:s=>Bt[s],vX:s=>Nt[s],vY:s=>qt[s],inv:s=>s&8?s&15:-s&7,add:(s,e)=>Us[s][e],sub:(s,e)=>Us[s][ee.inv(e)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,e)=>Math.abs(s)*2<=Math.abs(e)?e>=0?ee.S:ee.N:Math.abs(e)*2<=Math.abs(s)?s>0?ee.E:ee.W:e>0?s>0?ee.SE:ee.SW:s>0?ee.NE:ee.NW,matrixAppendRotationInv:(s,e,t=0,i=0)=>{const n=io[ee.inv(e)];n.tx=t,n.ty=i,s.append(n)},transformRectCoords:(s,e,t,i)=>{const{x:n,y:r,width:a,height:o}=s,{x:c,y:l,width:h,height:u}=e;return t===ee.E?(i.set(n+c,r+l,a,o),i):t===ee.S?i.set(h-r-o+c,n+l,o,a):t===ee.W?i.set(h-n-a+c,u-r-o+l,a,o):t===ee.N?i.set(r+c,u-n-a+l,o,a):i.set(n+c,r+l,a,o)}},bn=[new at,new at,new at,new at];class ot{constructor(e=0,t=0,i=0,n=0){this.type="rectangle",this.x=Number(e),this.y=Number(t),this.width=Number(i),this.height=Number(n)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new ot(0,0,0,0)}clone(){return new ot(this.x,this.y,this.width,this.height)}copyFromBounds(e){return this.x=e.minX,this.y=e.minY,this.width=e.maxX-e.minX,this.height=e.maxY-e.minY,this}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){return this.width<=0||this.height<=0?!1:e>=this.x&&e<this.x+this.width&&t>=this.y&&t<this.y+this.height}strokeContains(e,t,i,n=.5){const{width:r,height:a}=this;if(r<=0||a<=0)return!1;const o=this.x,c=this.y,l=i*(1-n),h=i-l,u=o-l,f=o+r+l,p=c-l,g=c+a+l,d=o+h,m=o+r-h,_=c+h,x=c+a-h;return e>=u&&e<=f&&t>=p&&t<=g&&!(e>d&&e<m&&t>_&&t<x)}intersects(e,t){if(!t){const v=this.x<e.x?e.x:this.x;if((this.right>e.right?e.right:this.right)<=v)return!1;const E=this.y<e.y?e.y:this.y;return(this.bottom>e.bottom?e.bottom:this.bottom)>E}const i=this.left,n=this.right,r=this.top,a=this.bottom;if(n<=i||a<=r)return!1;const o=bn[0].set(e.left,e.top),c=bn[1].set(e.left,e.bottom),l=bn[2].set(e.right,e.top),h=bn[3].set(e.right,e.bottom);if(l.x<=o.x||c.y<=o.y)return!1;const u=Math.sign(t.a*t.d-t.b*t.c);if(u===0||(t.apply(o,o),t.apply(c,c),t.apply(l,l),t.apply(h,h),Math.max(o.x,c.x,l.x,h.x)<=i||Math.min(o.x,c.x,l.x,h.x)>=n||Math.max(o.y,c.y,l.y,h.y)<=r||Math.min(o.y,c.y,l.y,h.y)>=a))return!1;const f=u*(c.y-o.y),p=u*(o.x-c.x),g=f*i+p*r,d=f*n+p*r,m=f*i+p*a,_=f*n+p*a;if(Math.max(g,d,m,_)<=f*o.x+p*o.y||Math.min(g,d,m,_)>=f*h.x+p*h.y)return!1;const x=u*(o.y-l.y),b=u*(l.x-o.x),T=x*i+b*r,k=x*n+b*r,C=x*i+b*a,y=x*n+b*a;return!(Math.max(T,k,C,y)<=x*o.x+b*o.y||Math.min(T,k,C,y)>=x*h.x+b*h.y)}pad(e=0,t=e){return this.x-=e,this.y-=t,this.width+=e*2,this.height+=t*2,this}fit(e){const t=Math.max(this.x,e.x),i=Math.min(this.x+this.width,e.x+e.width),n=Math.max(this.y,e.y),r=Math.min(this.y+this.height,e.y+e.height);return this.x=t,this.width=Math.max(i-t,0),this.y=n,this.height=Math.max(r-n,0),this}ceil(e=1,t=.001){const i=Math.ceil((this.x+this.width-t)*e)/e,n=Math.ceil((this.y+this.height-t)*e)/e;return this.x=Math.floor((this.x+t)*e)/e,this.y=Math.floor((this.y+t)*e)/e,this.width=i-this.x,this.height=n-this.y,this}scale(e,t=e){return this.x*=e,this.y*=t,this.width*=e,this.height*=t,this}enlarge(e){const t=Math.min(this.x,e.x),i=Math.max(this.x+this.width,e.x+e.width),n=Math.min(this.y,e.y),r=Math.max(this.y+this.height,e.y+e.height);return this.x=t,this.width=i-t,this.y=n,this.height=r-n,this}getBounds(e){return e||(e=new ot),e.copyFrom(this),e}containsRect(e){if(this.width<=0||this.height<=0)return!1;const t=e.x,i=e.y,n=e.x+e.width,r=e.y+e.height;return t>=this.x&&t<this.x+this.width&&i>=this.y&&i<this.y+this.height&&n>=this.x&&n<this.x+this.width&&r>=this.y&&r<this.y+this.height}set(e,t,i,n){return this.x=e,this.y=t,this.width=i,this.height=n,this}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const fs={default:-1};function _t(s="default"){return fs[s]===void 0&&(fs[s]=-1),++fs[s]}const qr={},xt="8.0.0",S_="8.3.4";function Je(s,e,t=3){if(qr[e])return;let i=new Error().stack;typeof i>"u"?console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`):(i=i.split(`
`).splice(t).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${e}
Deprecated since v${s}`),console.warn(i),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`),console.warn(i))),qr[e]=!0}const no=()=>{};function zr(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function Gr(s){return!(s&s-1)&&!!s}function gh(s){const e={};for(const t in s)s[t]!==void 0&&(e[t]=s[t]);return e}const Yr=Object.create(null);function yh(s){const e=Yr[s];return e===void 0&&(Yr[s]=_t("resource")),e}const so=class ro extends gn{constructor(e={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,e={...ro.defaultOptions,...e},this.addressMode=e.addressMode,this.addressModeU=e.addressModeU??this.addressModeU,this.addressModeV=e.addressModeV??this.addressModeV,this.addressModeW=e.addressModeW??this.addressModeW,this.scaleMode=e.scaleMode,this.magFilter=e.magFilter??this.magFilter,this.minFilter=e.minFilter??this.minFilter,this.mipmapFilter=e.mipmapFilter??this.mipmapFilter,this.lodMinClamp=e.lodMinClamp,this.lodMaxClamp=e.lodMaxClamp,this.compare=e.compare,this.maxAnisotropy=e.maxAnisotropy??1}set addressMode(e){this.addressModeU=e,this.addressModeV=e,this.addressModeW=e}get addressMode(){return this.addressModeU}set wrapMode(e){Je(xt,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=e}get wrapMode(){return this.addressMode}set scaleMode(e){this.magFilter=e,this.minFilter=e,this.mipmapFilter=e}get scaleMode(){return this.magFilter}set maxAnisotropy(e){this._maxAnisotropy=Math.min(e,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const e=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=yh(e),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};so.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let ao=so;const oo=class lo extends gn{constructor(e={}){super(),this.options=e,this.uid=_t("textureSource"),this._resourceType="textureSource",this._resourceId=_t("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,e={...lo.defaultOptions,...e},this.label=e.label??"",this.resource=e.resource,this.autoGarbageCollect=e.autoGarbageCollect,this._resolution=e.resolution,e.width?this.pixelWidth=e.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,e.height?this.pixelHeight=e.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=e.format,this.dimension=e.dimensions,this.mipLevelCount=e.mipLevelCount,this.autoGenerateMipmaps=e.autoGenerateMipmaps,this.sampleCount=e.sampleCount,this.antialias=e.antialias,this.alphaMode=e.alphaMode,this.style=new ao(gh(e)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(e){var t,i;this.style!==e&&((t=this._style)==null||t.off("change",this._onStyleChange,this),this._style=e,(i=this._style)==null||i.on("change",this._onStyleChange,this),this._onStyleChange())}set maxAnisotropy(e){this._style.maxAnisotropy=e}get maxAnisotropy(){return this._style.maxAnisotropy}get addressMode(){return this._style.addressMode}set addressMode(e){this._style.addressMode=e}get repeatMode(){return this._style.addressMode}set repeatMode(e){this._style.addressMode=e}get magFilter(){return this._style.magFilter}set magFilter(e){this._style.magFilter=e}get minFilter(){return this._style.minFilter}set minFilter(e){this._style.minFilter=e}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(e){this._style.mipmapFilter=e}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(e){this._style.lodMinClamp=e}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(e){this._style.lodMaxClamp=e}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const e=this._resolution;if(this.resize(this.resourceWidth/e,this.resourceHeight/e))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=_t("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:e}=this;return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}get resourceHeight(){const{resource:e}=this;return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}get resolution(){return this._resolution}set resolution(e){this._resolution!==e&&(this._resolution=e,this.width=this.pixelWidth/e,this.height=this.pixelHeight/e)}resize(e,t,i){i||(i=this._resolution),e||(e=this.width),t||(t=this.height);const n=Math.round(e*i),r=Math.round(t*i);return this.width=n/i,this.height=r/i,this._resolution=i,this.pixelWidth===n&&this.pixelHeight===r?!1:(this._refreshPOT(),this.pixelWidth=n,this.pixelHeight=r,this.emit("resize",this),this._resourceId=_t("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(e){this._style.wrapMode=e}get wrapMode(){return this._style.wrapMode}set scaleMode(e){this._style.scaleMode=e}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=Gr(this.pixelWidth)&&Gr(this.pixelHeight)}static test(e){throw new Error("Unimplemented")}};oo.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let it=oo;class _r extends it{constructor(e){const t=e.resource||new Float32Array(e.width*e.height*4);let i=e.format;i||(t instanceof Float32Array?i="rgba32float":t instanceof Int32Array||t instanceof Uint32Array?i="rgba32uint":t instanceof Int16Array||t instanceof Uint16Array?i="rgba16uint":(t instanceof Int8Array,i="bgra8unorm")),super({...e,resource:t,format:i}),this.uploadMethodId="buffer"}static test(e){return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array}}_r.extension=de.TextureSource;const Xr=new se;class vh{constructor(e,t){this.mapCoord=new se,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof t>"u"?this.clampMargin=e.width<10?0:.5:this.clampMargin=t,this.isSimple=!1,this.texture=e}get texture(){return this._texture}set texture(e){var t;this.texture!==e&&((t=this._texture)==null||t.removeListener("update",this.update,this),this._texture=e,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(e,t){t===void 0&&(t=e);const i=this.mapCoord;for(let n=0;n<e.length;n+=2){const r=e[n],a=e[n+1];t[n]=r*i.a+a*i.c+i.tx,t[n+1]=r*i.b+a*i.d+i.ty}return t}update(){const e=this._texture;this._updateID++;const t=e.uvs;this.mapCoord.set(t.x1-t.x0,t.y1-t.y0,t.x3-t.x0,t.y3-t.y0,t.x0,t.y0);const i=e.orig,n=e.trim;n&&(Xr.set(i.width/n.width,0,0,i.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(Xr));const r=e.source,a=this.uClampFrame,o=this.clampMargin/r._resolution,c=this.clampOffset/r._resolution;return a[0]=(e.frame.x+o+c)/r.width,a[1]=(e.frame.y+o+c)/r.height,a[2]=(e.frame.x+e.frame.width-o+c)/r.width,a[3]=(e.frame.y+e.frame.height-o+c)/r.height,this.uClampOffset[0]=this.clampOffset/r.pixelWidth,this.uClampOffset[1]=this.clampOffset/r.pixelHeight,this.isSimple=e.frame.width===r.width&&e.frame.height===r.height&&e.rotate===0,!0}}class me extends gn{constructor({source:e,label:t,frame:i,orig:n,trim:r,defaultAnchor:a,defaultBorders:o,rotate:c,dynamic:l}={}){if(super(),this.uid=_t("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new ot,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=t,this.source=(e==null?void 0:e.source)??new it,this.noFrame=!i,i)this.frame.copyFrom(i);else{const{width:h,height:u}=this._source;this.frame.width=h,this.frame.height=u}this.orig=n||this.frame,this.trim=r,this.rotate=c??0,this.defaultAnchor=a,this.defaultBorders=o,this.destroyed=!1,this.dynamic=l||!1,this.updateUvs()}set source(e){this._source&&this._source.off("resize",this.update,this),this._source=e,e.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new vh(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:e,frame:t}=this,{width:i,height:n}=this._source,r=t.x/i,a=t.y/n,o=t.width/i,c=t.height/n;let l=this.rotate;if(l){const h=o/2,u=c/2,f=r+h,p=a+u;l=ee.add(l,ee.NW),e.x0=f+h*ee.uX(l),e.y0=p+u*ee.uY(l),l=ee.add(l,2),e.x1=f+h*ee.uX(l),e.y1=p+u*ee.uY(l),l=ee.add(l,2),e.x2=f+h*ee.uX(l),e.y2=p+u*ee.uY(l),l=ee.add(l,2),e.x3=f+h*ee.uX(l),e.y3=p+u*ee.uY(l)}else e.x0=r,e.y0=a,e.x1=r+o,e.y1=a,e.x2=r+o,e.y2=a+c,e.x3=r,e.y3=a+c}destroy(e=!1){this._source&&e&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return Je(xt,"Texture.baseTexture is now Texture.source"),this._source}}me.EMPTY=new me({label:"EMPTY",source:new it({label:"EMPTY"})});me.EMPTY.destroy=no;me.WHITE=new me({source:new _r({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});me.WHITE.destroy=no;function xh(s,e,t){const{width:i,height:n}=t.orig,r=t.trim;if(r){const a=r.width,o=r.height;s.minX=r.x-e._x*i,s.maxX=s.minX+a,s.minY=r.y-e._y*n,s.maxY=s.minY+o}else s.minX=-e._x*i,s.maxX=s.minX+i,s.minY=-e._y*n,s.maxY=s.minY+n}const jr=new se;class It{constructor(e=1/0,t=1/0,i=-1/0,n=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=jr,this.minX=e,this.minY=t,this.maxX=i,this.maxY=n}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new ot);const e=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(e.x=0,e.y=0,e.width=0,e.height=0):e.copyFromBounds(this),e}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=jr,this}set(e,t,i,n){this.minX=e,this.minY=t,this.maxX=i,this.maxY=n}addFrame(e,t,i,n,r){r||(r=this.matrix);const a=r.a,o=r.b,c=r.c,l=r.d,h=r.tx,u=r.ty;let f=this.minX,p=this.minY,g=this.maxX,d=this.maxY,m=a*e+c*t+h,_=o*e+l*t+u;m<f&&(f=m),_<p&&(p=_),m>g&&(g=m),_>d&&(d=_),m=a*i+c*t+h,_=o*i+l*t+u,m<f&&(f=m),_<p&&(p=_),m>g&&(g=m),_>d&&(d=_),m=a*e+c*n+h,_=o*e+l*n+u,m<f&&(f=m),_<p&&(p=_),m>g&&(g=m),_>d&&(d=_),m=a*i+c*n+h,_=o*i+l*n+u,m<f&&(f=m),_<p&&(p=_),m>g&&(g=m),_>d&&(d=_),this.minX=f,this.minY=p,this.maxX=g,this.maxY=d}addRect(e,t){this.addFrame(e.x,e.y,e.x+e.width,e.y+e.height,t)}addBounds(e,t){this.addFrame(e.minX,e.minY,e.maxX,e.maxY,t)}addBoundsMask(e){this.minX=this.minX>e.minX?this.minX:e.minX,this.minY=this.minY>e.minY?this.minY:e.minY,this.maxX=this.maxX<e.maxX?this.maxX:e.maxX,this.maxY=this.maxY<e.maxY?this.maxY:e.maxY}applyMatrix(e){const t=this.minX,i=this.minY,n=this.maxX,r=this.maxY,{a,b:o,c,d:l,tx:h,ty:u}=e;let f=a*t+c*i+h,p=o*t+l*i+u;this.minX=f,this.minY=p,this.maxX=f,this.maxY=p,f=a*n+c*i+h,p=o*n+l*i+u,this.minX=f<this.minX?f:this.minX,this.minY=p<this.minY?p:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=p>this.maxY?p:this.maxY,f=a*t+c*r+h,p=o*t+l*r+u,this.minX=f<this.minX?f:this.minX,this.minY=p<this.minY?p:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=p>this.maxY?p:this.maxY,f=a*n+c*r+h,p=o*n+l*r+u,this.minX=f<this.minX?f:this.minX,this.minY=p<this.minY?p:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=p>this.maxY?p:this.maxY}fit(e){return this.minX<e.left&&(this.minX=e.left),this.maxX>e.right&&(this.maxX=e.right),this.minY<e.top&&(this.minY=e.top),this.maxY>e.bottom&&(this.maxY=e.bottom),this}fitBounds(e,t,i,n){return this.minX<e&&(this.minX=e),this.maxX>t&&(this.maxX=t),this.minY<i&&(this.minY=i),this.maxY>n&&(this.maxY=n),this}pad(e,t=e){return this.minX-=e,this.maxX+=e,this.minY-=t,this.maxY+=t,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new It(this.minX,this.minY,this.maxX,this.maxY)}scale(e,t=e){return this.minX*=e,this.minY*=t,this.maxX*=e,this.maxY*=t,this}get x(){return this.minX}set x(e){const t=this.maxX-this.minX;this.minX=e,this.maxX=e+t}get y(){return this.minY}set y(e){const t=this.maxY-this.minY;this.minY=e,this.maxY=e+t}get width(){return this.maxX-this.minX}set width(e){this.maxX=this.minX+e}get height(){return this.maxY-this.minY}set height(e){this.maxY=this.minY+e}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(e,t,i,n){let r=this.minX,a=this.minY,o=this.maxX,c=this.maxY;n||(n=this.matrix);const l=n.a,h=n.b,u=n.c,f=n.d,p=n.tx,g=n.ty;for(let d=t;d<i;d+=2){const m=e[d],_=e[d+1],x=l*m+u*_+p,b=h*m+f*_+g;r=x<r?x:r,a=b<a?b:a,o=x>o?x:o,c=b>c?b:c}this.minX=r,this.minY=a,this.maxX=o,this.maxY=c}containsPoint(e,t){return this.minX<=e&&this.minY<=t&&this.maxX>=e&&this.maxY>=t}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(e){return this.minX=e.minX,this.minY=e.minY,this.maxX=e.maxX,this.maxY=e.maxY,this}}var bh={grad:.9,turn:360,rad:360/(2*Math.PI)},ut=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},pe=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=Math.pow(10,e)),Math.round(t*s)/t+0},je=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=1),s>t?t:s>e?s:e},co=function(s){return(s=isFinite(s)?s%360:0)>0?s:s+360},Hr=function(s){return{r:je(s.r,0,255),g:je(s.g,0,255),b:je(s.b,0,255),a:je(s.a)}},ms=function(s){return{r:pe(s.r),g:pe(s.g),b:pe(s.b),a:pe(s.a,3)}},wh=/^#([0-9a-f]{3,8})$/i,wn=function(s){var e=s.toString(16);return e.length<2?"0"+e:e},ho=function(s){var e=s.r,t=s.g,i=s.b,n=s.a,r=Math.max(e,t,i),a=r-Math.min(e,t,i),o=a?r===e?(t-i)/a:r===t?2+(i-e)/a:4+(e-t)/a:0;return{h:60*(o<0?o+6:o),s:r?a/r*100:0,v:r/255*100,a:n}},uo=function(s){var e=s.h,t=s.s,i=s.v,n=s.a;e=e/360*6,t/=100,i/=100;var r=Math.floor(e),a=i*(1-t),o=i*(1-(e-r)*t),c=i*(1-(1-e+r)*t),l=r%6;return{r:255*[i,o,a,a,c,i][l],g:255*[c,i,i,o,a,a][l],b:255*[a,a,c,i,i,o][l],a:n}},Wr=function(s){return{h:co(s.h),s:je(s.s,0,100),l:je(s.l,0,100),a:je(s.a)}},Qr=function(s){return{h:pe(s.h),s:pe(s.s),l:pe(s.l),a:pe(s.a,3)}},Kr=function(s){return uo((t=(e=s).s,{h:e.h,s:(t*=((i=e.l)<50?i:100-i)/100)>0?2*t/(i+t)*100:0,v:i+t,a:e.a}));var e,t,i},Wi=function(s){return{h:(e=ho(s)).h,s:(n=(200-(t=e.s))*(i=e.v)/100)>0&&n<200?t*i/100/(n<=100?n:200-n)*100:0,l:n/2,a:e.a};var e,t,i,n},Ah=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Ch=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Th=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,kh=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Bs={string:[[function(s){var e=wh.exec(s);return e?(s=e[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?pe(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?pe(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var e=Th.exec(s)||kh.exec(s);return e?e[2]!==e[4]||e[4]!==e[6]?null:Hr({r:Number(e[1])/(e[2]?100/255:1),g:Number(e[3])/(e[4]?100/255:1),b:Number(e[5])/(e[6]?100/255:1),a:e[7]===void 0?1:Number(e[7])/(e[8]?100:1)}):null},"rgb"],[function(s){var e=Ah.exec(s)||Ch.exec(s);if(!e)return null;var t,i,n=Wr({h:(t=e[1],i=e[2],i===void 0&&(i="deg"),Number(t)*(bh[i]||1)),s:Number(e[3]),l:Number(e[4]),a:e[5]===void 0?1:Number(e[5])/(e[6]?100:1)});return Kr(n)},"hsl"]],object:[[function(s){var e=s.r,t=s.g,i=s.b,n=s.a,r=n===void 0?1:n;return ut(e)&&ut(t)&&ut(i)?Hr({r:Number(e),g:Number(t),b:Number(i),a:Number(r)}):null},"rgb"],[function(s){var e=s.h,t=s.s,i=s.l,n=s.a,r=n===void 0?1:n;if(!ut(e)||!ut(t)||!ut(i))return null;var a=Wr({h:Number(e),s:Number(t),l:Number(i),a:Number(r)});return Kr(a)},"hsl"],[function(s){var e=s.h,t=s.s,i=s.v,n=s.a,r=n===void 0?1:n;if(!ut(e)||!ut(t)||!ut(i))return null;var a=function(o){return{h:co(o.h),s:je(o.s,0,100),v:je(o.v,0,100),a:je(o.a)}}({h:Number(e),s:Number(t),v:Number(i),a:Number(r)});return uo(a)},"hsv"]]},Jr=function(s,e){for(var t=0;t<e.length;t++){var i=e[t][0](s);if(i)return[i,e[t][1]]}return[null,void 0]},Ph=function(s){return typeof s=="string"?Jr(s.trim(),Bs.string):typeof s=="object"&&s!==null?Jr(s,Bs.object):[null,void 0]},ps=function(s,e){var t=Wi(s);return{h:t.h,s:je(t.s+100*e,0,100),l:t.l,a:t.a}},_s=function(s){return(299*s.r+587*s.g+114*s.b)/1e3/255},Zr=function(s,e){var t=Wi(s);return{h:t.h,s:t.s,l:je(t.l+100*e,0,100),a:t.a}},Ns=function(){function s(e){this.parsed=Ph(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return pe(_s(this.rgba),2)},s.prototype.isDark=function(){return _s(this.rgba)<.5},s.prototype.isLight=function(){return _s(this.rgba)>=.5},s.prototype.toHex=function(){return e=ms(this.rgba),t=e.r,i=e.g,n=e.b,a=(r=e.a)<1?wn(pe(255*r)):"","#"+wn(t)+wn(i)+wn(n)+a;var e,t,i,n,r,a},s.prototype.toRgb=function(){return ms(this.rgba)},s.prototype.toRgbString=function(){return e=ms(this.rgba),t=e.r,i=e.g,n=e.b,(r=e.a)<1?"rgba("+t+", "+i+", "+n+", "+r+")":"rgb("+t+", "+i+", "+n+")";var e,t,i,n,r},s.prototype.toHsl=function(){return Qr(Wi(this.rgba))},s.prototype.toHslString=function(){return e=Qr(Wi(this.rgba)),t=e.h,i=e.s,n=e.l,(r=e.a)<1?"hsla("+t+", "+i+"%, "+n+"%, "+r+")":"hsl("+t+", "+i+"%, "+n+"%)";var e,t,i,n,r},s.prototype.toHsv=function(){return e=ho(this.rgba),{h:pe(e.h),s:pe(e.s),v:pe(e.v),a:pe(e.a,3)};var e},s.prototype.invert=function(){return nt({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},s.prototype.saturate=function(e){return e===void 0&&(e=.1),nt(ps(this.rgba,e))},s.prototype.desaturate=function(e){return e===void 0&&(e=.1),nt(ps(this.rgba,-e))},s.prototype.grayscale=function(){return nt(ps(this.rgba,-1))},s.prototype.lighten=function(e){return e===void 0&&(e=.1),nt(Zr(this.rgba,e))},s.prototype.darken=function(e){return e===void 0&&(e=.1),nt(Zr(this.rgba,-e))},s.prototype.rotate=function(e){return e===void 0&&(e=15),this.hue(this.hue()+e)},s.prototype.alpha=function(e){return typeof e=="number"?nt({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):pe(this.rgba.a,3);var t},s.prototype.hue=function(e){var t=Wi(this.rgba);return typeof e=="number"?nt({h:e,s:t.s,l:t.l,a:t.a}):pe(t.h)},s.prototype.isEqual=function(e){return this.toHex()===nt(e).toHex()},s}(),nt=function(s){return s instanceof Ns?s:new Ns(s)},ea=[],Mh=function(s){s.forEach(function(e){ea.indexOf(e)<0&&(e(Ns,Bs),ea.push(e))})};function Sh(s,e){var t={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},i={};for(var n in t)i[t[n]]=n;var r={};s.prototype.toName=function(a){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var o,c,l=i[this.toHex()];if(l)return l;if(a!=null&&a.closest){var h=this.toRgb(),u=1/0,f="black";if(!r.length)for(var p in t)r[p]=new s(t[p]).toRgb();for(var g in t){var d=(o=h,c=r[g],Math.pow(o.r-c.r,2)+Math.pow(o.g-c.g,2)+Math.pow(o.b-c.b,2));d<u&&(u=d,f=g)}return f}},e.string.push([function(a){var o=a.toLowerCase(),c=o==="transparent"?"#0000":t[o];return c?new s(c).toRgb():null},"name"])}Mh([Sh]);const wi=class qi{constructor(e=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=e}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(e){return this.value=e,this}set value(e){if(e instanceof qi)this._value=this._cloneSource(e._value),this._int=e._int,this._components.set(e._components);else{if(e===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,e))&&(this._value=this._cloneSource(e),this._normalize(this._value))}}get value(){return this._value}_cloneSource(e){return typeof e=="string"||typeof e=="number"||e instanceof Number||e===null?e:Array.isArray(e)||ArrayBuffer.isView(e)?e.slice(0):typeof e=="object"&&e!==null?{...e}:e}_isSourceEqual(e,t){const i=typeof e;if(i!==typeof t)return!1;if(i==="number"||i==="string"||e instanceof Number)return e===t;if(Array.isArray(e)&&Array.isArray(t)||ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return e.length!==t.length?!1:e.every((r,a)=>r===t[a]);if(e!==null&&t!==null){const r=Object.keys(e),a=Object.keys(t);return r.length!==a.length?!1:r.every(o=>e[o]===t[o])}return e===t}toRgba(){const[e,t,i,n]=this._components;return{r:e,g:t,b:i,a:n}}toRgb(){const[e,t,i]=this._components;return{r:e,g:t,b:i}}toRgbaString(){const[e,t,i]=this.toUint8RgbArray();return`rgba(${e},${t},${i},${this.alpha})`}toUint8RgbArray(e){const[t,i,n]=this._components;return this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb),e[0]=Math.round(t*255),e[1]=Math.round(i*255),e[2]=Math.round(n*255),e}toArray(e){this._arrayRgba||(this._arrayRgba=[]),e||(e=this._arrayRgba);const[t,i,n,r]=this._components;return e[0]=t,e[1]=i,e[2]=n,e[3]=r,e}toRgbArray(e){this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb);const[t,i,n]=this._components;return e[0]=t,e[1]=i,e[2]=n,e}toNumber(){return this._int}toBgrNumber(){const[e,t,i]=this.toUint8RgbArray();return(i<<16)+(t<<8)+e}toLittleEndianNumber(){const e=this._int;return(e>>16)+(e&65280)+((e&255)<<16)}multiply(e){const[t,i,n,r]=qi._temp.setValue(e)._components;return this._components[0]*=t,this._components[1]*=i,this._components[2]*=n,this._components[3]*=r,this._refreshInt(),this._value=null,this}premultiply(e,t=!0){return t&&(this._components[0]*=e,this._components[1]*=e,this._components[2]*=e),this._components[3]=e,this._refreshInt(),this._value=null,this}toPremultiplied(e,t=!0){if(e===1)return(255<<24)+this._int;if(e===0)return t?0:this._int;let i=this._int>>16&255,n=this._int>>8&255,r=this._int&255;return t&&(i=i*e+.5|0,n=n*e+.5|0,r=r*e+.5|0),(e*255<<24)+(i<<16)+(n<<8)+r}toHex(){const e=this._int.toString(16);return`#${"000000".substring(0,6-e.length)+e}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(e){return this._components[3]=this._clamp(e),this}_normalize(e){let t,i,n,r;if((typeof e=="number"||e instanceof Number)&&e>=0&&e<=16777215){const a=e;t=(a>>16&255)/255,i=(a>>8&255)/255,n=(a&255)/255,r=1}else if((Array.isArray(e)||e instanceof Float32Array)&&e.length>=3&&e.length<=4)e=this._clamp(e),[t,i,n,r=1]=e;else if((e instanceof Uint8Array||e instanceof Uint8ClampedArray)&&e.length>=3&&e.length<=4)e=this._clamp(e,0,255),[t,i,n,r=255]=e,t/=255,i/=255,n/=255,r/=255;else if(typeof e=="string"||typeof e=="object"){if(typeof e=="string"){const o=qi.HEX_PATTERN.exec(e);o&&(e=`#${o[2]}`)}const a=nt(e);a.isValid()&&({r:t,g:i,b:n,a:r}=a.rgba,t/=255,i/=255,n/=255)}if(t!==void 0)this._components[0]=t,this._components[1]=i,this._components[2]=n,this._components[3]=r,this._refreshInt();else throw new Error(`Unable to convert color ${e}`)}_refreshInt(){this._clamp(this._components);const[e,t,i]=this._components;this._int=(e*255<<16)+(t*255<<8)+(i*255|0)}_clamp(e,t=0,i=1){return typeof e=="number"?Math.min(Math.max(e,t),i):(e.forEach((n,r)=>{e[r]=Math.min(Math.max(n,t),i)}),e)}static isColorLike(e){return typeof e=="number"||typeof e=="string"||e instanceof Number||e instanceof qi||Array.isArray(e)||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Float32Array||e.r!==void 0&&e.g!==void 0&&e.b!==void 0||e.r!==void 0&&e.g!==void 0&&e.b!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0&&e.a!==void 0}};wi.shared=new wi;wi._temp=new wi;wi.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let Dn=wi;const Eh={cullArea:null,cullable:!1,cullableChildren:!0};let gs=0;const ta=500;function St(...s){gs!==ta&&(gs++,gs===ta?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s))}class gr{constructor(e,t){this._pool=[],this._count=0,this._index=0,this._classType=e,t&&this.prepopulate(t)}prepopulate(e){for(let t=0;t<e;t++)this._pool[this._index++]=new this._classType;this._count+=e}get(e){var i;let t;return this._index>0?t=this._pool[--this._index]:t=new this._classType,(i=t.init)==null||i.call(t,e),t}return(e){var t;(t=e.reset)==null||t.call(e),this._pool[this._index++]=e}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class Rh{constructor(){this._poolsByClass=new Map}prepopulate(e,t){this.getPool(e).prepopulate(t)}get(e,t){return this.getPool(e).get(t)}return(e){this.getPool(e.constructor).return(e)}getPool(e){return this._poolsByClass.has(e)||this._poolsByClass.set(e,new gr(e)),this._poolsByClass.get(e)}stats(){const e={};return this._poolsByClass.forEach(t=>{const i=e[t._classType.name]?t._classType.name+t._classType.ID:t._classType.name;e[i]={free:t.totalFree,used:t.totalUsed,size:t.totalSize}}),e}}const zn=new Rh,Ih={get isCachedAsTexture(){var s;return!!((s=this.renderGroup)!=null&&s.isCachedAsTexture)},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s))},updateCacheTexture(){var s;(s=this.renderGroup)==null||s.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){Je("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s)}};function Dh(s,e,t){const i=s.length;let n;if(e>=i||t===0)return;t=e+t>i?i-e:t;const r=i-t;for(n=e;n<r;++n)s[n]=s[n+t];s.length=r}const Oh={allowChildren:!0,removeChildren(s=0,e){var r;const t=e??this.children.length,i=t-s,n=[];if(i>0&&i<=t){for(let o=t-1;o>=s;o--){const c=this.children[o];c&&(n.push(c),c.parent=null)}Dh(this.children,s,t);const a=this.renderGroup||this.parentRenderGroup;a&&a.removeChildren(n);for(let o=0;o<n.length;++o){const c=n[o];(r=c.parentRenderLayer)==null||r.detach(c),this.emit("childRemoved",c,this,o),n[o].emit("removed",this)}return n.length>0&&this._didViewChangeTick++,n}else if(i===0&&this.children.length===0)return n;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const e=this.getChildAt(s);return this.removeChild(e)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,e)},getChildIndex(s){const e=this.children.indexOf(s);if(e===-1)throw new Error("The supplied Container must be a child of the caller");return e},addChildAt(s,e){this.allowChildren||Je(xt,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:t}=this;if(e<0||e>t.length)throw new Error(`${s}addChildAt: The index ${e} supplied is out of bounds ${t.length}`);if(s.parent){const n=s.parent.children.indexOf(s);if(s.parent===this&&n===e)return s;n!==-1&&s.parent.children.splice(n,1)}e===t.length?t.push(s):t.splice(e,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const i=this.renderGroup||this.parentRenderGroup;return i&&i.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,e),s.emit("added",this),s},swapChildren(s,e){if(s===e)return;const t=this.getChildIndex(s),i=this.getChildIndex(e);this.children[t]=e,this.children[i]=s;const n=this.renderGroup||this.parentRenderGroup;n&&(n.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){var s;(s=this.parent)==null||s.removeChild(this)},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(e=>this.reparentChildAt(e,this.children.length)),s[0])},reparentChildAt(s,e){if(s.parent===this)return this.setChildIndex(s,e),s;const t=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,e);const i=this.worldTransform.clone();return i.invert(),t.prepend(i),s.setFromMatrix(t),s},replaceChild(s,e){s.updateLocalTransform(),this.addChildAt(e,this.getChildIndex(s)),e.setFromMatrix(s.localTransform),e.updateLocalTransform(),this.removeChild(s)}},$h={collectRenderables(s,e,t){this.parentRenderLayer&&this.parentRenderLayer!==t||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,e,t):this.renderGroup?e.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,e,t))},collectRenderablesSimple(s,e,t){const i=this.children,n=i.length;for(let r=0;r<n;r++)i[r].collectRenderables(s,e,t)},collectRenderablesWithEffects(s,e,t){const{renderPipes:i}=e;for(let n=0;n<this.effects.length;n++){const r=this.effects[n];i[r.pipe].push(r,this,s)}this.collectRenderablesSimple(s,e,t);for(let n=this.effects.length-1;n>=0;n--){const r=this.effects[n];i[r.pipe].pop(r,this,s)}}};class ia{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let e=0;e<this.filters.length;e++)this.filters[e].destroy();this.filters=null,this.filterArea=null}}class Vh{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(e=>{this.add({test:e.test,maskClass:e})}))}add(e){this._tests.push(e)}getMaskEffect(e){this._initialized||this.init();for(let t=0;t<this._tests.length;t++){const i=this._tests[t];if(i.test(e))return zn.get(i.maskClass,e)}return e}returnMaskEffect(e){zn.return(e)}}const qs=new Vh;ht.handleByList(de.MaskEffect,qs._effectClasses);const Fh={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0)},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((t,i)=>t.priority-i.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(s){const e=this.effects.indexOf(s);e!==-1&&(this.effects.splice(e,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(s){const e=this._maskEffect;(e==null?void 0:e.mask)!==s&&(e&&(this.removeEffect(e),qs.returnMaskEffect(e),this._maskEffect=null),s!=null&&(this._maskEffect=qs.getMaskEffect(s),this.addEffect(this._maskEffect)))},get mask(){var s;return(s=this._maskEffect)==null?void 0:s.mask},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged()},set filters(s){var r;!Array.isArray(s)&&s&&(s=[s]);const e=this._filterEffect||(this._filterEffect=new ia);s=s;const t=(s==null?void 0:s.length)>0,i=((r=e.filters)==null?void 0:r.length)>0,n=t!==i;s=Array.isArray(s)?s.slice(0):s,e.filters=Object.freeze(s),n&&(t?this.addEffect(e):(this.removeEffect(e),e.filters=s??null))},get filters(){var s;return(s=this._filterEffect)==null?void 0:s.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new ia),this._filterEffect.filterArea=s},get filterArea(){var s;return(s=this._filterEffect)==null?void 0:s.filterArea}},Lh={label:null,get name(){return Je(xt,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){Je(xt,"Container.name property has been removed, use Container.label instead"),this.label=s},getChildByName(s,e=!1){return this.getChildByLabel(s,e)},getChildByLabel(s,e=!1){const t=this.children;for(let i=0;i<t.length;i++){const n=t[i];if(n.label===s||s instanceof RegExp&&s.test(n.label))return n}if(e)for(let i=0;i<t.length;i++){const r=t[i].getChildByLabel(s,!0);if(r)return r}return null},getChildrenByLabel(s,e=!1,t=[]){const i=this.children;for(let n=0;n<i.length;n++){const r=i[n];(r.label===s||s instanceof RegExp&&s.test(r.label))&&t.push(r)}if(e)for(let n=0;n<i.length;n++)i[n].getChildrenByLabel(s,!0,t);return t}},Te=new gr(se),yt=new gr(It),Uh=new se,Bh={getFastGlobalBounds(s,e){e||(e=new It),e.clear(),this._getGlobalBoundsRecursive(!!s,e,this.parentRenderLayer),e.isValid||e.set(0,0,0,0);const t=this.renderGroup||this.parentRenderGroup;return e.applyMatrix(t.worldTransform),e},_getGlobalBoundsRecursive(s,e,t){let i=e;if(s&&this.parentRenderLayer&&this.parentRenderLayer!==t||this.localDisplayStatus!==7||!this.measurable)return;const n=!!this.effects.length;if((this.renderGroup||n)&&(i=yt.get().clear()),this.boundsArea)e.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const a=this.bounds;i.addFrame(a.minX,a.minY,a.maxX,a.maxY,this.groupTransform)}const r=this.children;for(let a=0;a<r.length;a++)r[a]._getGlobalBoundsRecursive(s,i,t)}if(n){let r=!1;const a=this.renderGroup||this.parentRenderGroup;for(let o=0;o<this.effects.length;o++)this.effects[o].addBounds&&(r||(r=!0,i.applyMatrix(a.worldTransform)),this.effects[o].addBounds(i,!0));r&&i.applyMatrix(a.worldTransform.copyTo(Uh).invert()),e.addBounds(i),yt.return(i)}else this.renderGroup&&(e.addBounds(i,this.relativeGroupTransform),yt.return(i))}};function fo(s,e,t){t.clear();let i,n;return s.parent?e?i=s.parent.worldTransform:(n=Te.get().identity(),i=yr(s,n)):i=se.IDENTITY,mo(s,t,i,e),n&&Te.return(n),t.isValid||t.set(0,0,0,0),t}function mo(s,e,t,i){var o,c;if(!s.visible||!s.measurable)return;let n;i?n=s.worldTransform:(s.updateLocalTransform(),n=Te.get(),n.appendFrom(s.localTransform,t));const r=e,a=!!s.effects.length;if(a&&(e=yt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,n);else{s.bounds&&(e.matrix=n,e.addBounds(s.bounds));for(let l=0;l<s.children.length;l++)mo(s.children[l],e,n,i)}if(a){for(let l=0;l<s.effects.length;l++)(c=(o=s.effects[l]).addBounds)==null||c.call(o,e);r.addBounds(e,se.IDENTITY),yt.return(e)}i||Te.return(n)}function yr(s,e){const t=s.parent;return t&&(yr(t,e),t.updateLocalTransform(),e.append(t.localTransform)),e}function Nh(s,e){if(s===16777215||!e)return e;if(e===16777215||!s)return s;const t=s>>16&255,i=s>>8&255,n=s&255,r=e>>16&255,a=e>>8&255,o=e&255,c=t*r/255|0,l=i*a/255|0,h=n*o/255|0;return(c<<16)+(l<<8)+h}const na=16777215;function sa(s,e){return s===na?e:e===na?s:Nh(s,e)}function On(s){return((s&255)<<16)+(s&65280)+(s>>16&255)}const qh={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let e=this.alpha,t=this.parent;for(;t;)e*=t.alpha,t=t.parent;return e},getGlobalTransform(s=new se,e){if(e)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const t=yr(this,Te.get().identity());return s.appendFrom(this.localTransform,t),Te.return(t),s},getGlobalTint(s){if(s)return this.renderGroup?On(this.renderGroup.worldColor):this.parentRenderGroup?On(sa(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let e=this.localColor,t=this.parent;for(;t;)e=sa(e,t.localColor),t=t.parent;return On(e)}};function po(s,e,t){return e.clear(),t||(t=se.IDENTITY),_o(s,e,t,s,!0),e.isValid||e.set(0,0,0,0),e}function _o(s,e,t,i,n){var c,l;let r;if(n)r=Te.get(),r=t.copyTo(r);else{if(!s.visible||!s.measurable)return;s.updateLocalTransform();const h=s.localTransform;r=Te.get(),r.appendFrom(h,t)}const a=e,o=!!s.effects.length;if(o&&(e=yt.get().clear()),s.boundsArea)e.addRect(s.boundsArea,r);else{s.renderPipeId&&(e.matrix=r,e.addBounds(s.bounds));const h=s.children;for(let u=0;u<h.length;u++)_o(h[u],e,r,i,!1)}if(o){for(let h=0;h<s.effects.length;h++)(l=(c=s.effects[h]).addLocalBounds)==null||l.call(c,e,i);a.addBounds(e,se.IDENTITY),yt.return(e)}Te.return(r)}function go(s,e){const t=s.children;for(let i=0;i<t.length;i++){const n=t[i],r=n.uid,a=(n._didViewChangeTick&65535)<<16|n._didContainerChangeTick&65535,o=e.index;(e.data[o]!==r||e.data[o+1]!==a)&&(e.data[e.index]=r,e.data[e.index+1]=a,e.didChange=!0),e.index=o+2,n.children.length&&go(n,e)}return e.didChange}const zh=new se,Gh={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,e){const t=Math.sign(this.scale.x)||1;e!==0?this.scale.x=s/e*t:this.scale.x=t},_setHeight(s,e){const t=Math.sign(this.scale.y)||1;e!==0?this.scale.y=s/e*t:this.scale.y=t},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new It});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),go(this,s),s.didChange&&po(this,s.localBounds,zh),s.localBounds},getBounds(s,e){return fo(this,s,e||new It)}},Yh={_onRender:null,set onRender(s){const e=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&(e==null||e.removeOnRender(this)),this._onRender=null;return}this._onRender||e==null||e.addOnRender(this),this._onRender=s},get onRender(){return this._onRender}},Xh={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(jh))}};function jh(s,e){return s._zIndex-e._zIndex}const Hh={getGlobalPosition(s=new at,e=!1){return this.parent?this.parent.toGlobal(this._position,s,e):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,e,t=!1){const i=this.getGlobalTransform(Te.get(),t);return e=i.apply(s,e),Te.return(i),e},toLocal(s,e,t,i){e&&(s=e.toGlobal(s,t,i));const n=this.getGlobalTransform(Te.get(),i);return t=n.applyInverse(s,t),Te.return(n),t}};class Wh{constructor(){this.uid=_t("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(e){this.instructions[this.instructionSize++]=e}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let Qh=0;class Kh{constructor(e){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1,this.textureStyle=new ao(this.textureOptions)}createTexture(e,t,i){const n=new it({...this.textureOptions,width:e,height:t,resolution:1,antialias:i,autoGarbageCollect:!1});return new me({source:n,label:`texturePool_${Qh++}`})}getOptimalTexture(e,t,i=1,n){let r=Math.ceil(e*i-1e-6),a=Math.ceil(t*i-1e-6);r=zr(r),a=zr(a);const o=(r<<17)+(a<<1)+(n?1:0);this._texturePool[o]||(this._texturePool[o]=[]);let c=this._texturePool[o].pop();return c||(c=this.createTexture(r,a,n)),c.source._resolution=i,c.source.width=r/i,c.source.height=a/i,c.source.pixelWidth=r,c.source.pixelHeight=a,c.frame.x=0,c.frame.y=0,c.frame.width=e,c.frame.height=t,c.updateUvs(),this._poolKeyHash[c.uid]=o,c}getSameSizeTexture(e,t=!1){const i=e.source;return this.getOptimalTexture(e.width,e.height,i._resolution,t)}returnTexture(e,t=!1){const i=this._poolKeyHash[e.uid];t&&(e.source.style=this.textureStyle),this._texturePool[i].push(e)}clear(e){if(e=e!==!1,e)for(const t in this._texturePool){const i=this._texturePool[t];if(i)for(let n=0;n<i.length;n++)i[n].destroy(!0)}this._texturePool={}}}const Jh=new Kh;class Zh{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new se,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new Wh,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(e){this.root=e,e._onRender&&this.addOnRender(e),e.didChange=!0;const t=e.children;for(let i=0;i<t.length;i++){const n=t[i];n._updateFlags=15,this.addChild(n)}}enableCacheAsTexture(e={}){this.textureOptions=e,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(Jh.returnTexture(this.texture,!0),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const e in this.childrenToUpdate){const t=this.childrenToUpdate[e];t.list.fill(null),t.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(e){e.renderGroupParent&&e.renderGroupParent._removeRenderGroupChild(e),e.renderGroupParent=this,this.renderGroupChildren.push(e)}_removeRenderGroupChild(e){const t=this.renderGroupChildren.indexOf(e);t>-1&&this.renderGroupChildren.splice(t,1),e.renderGroupParent=null}addChild(e){if(this.structureDidChange=!0,e.parentRenderGroup=this,e.updateTick=-1,e.parent===this.root?e.relativeRenderGroupDepth=1:e.relativeRenderGroupDepth=e.parent.relativeRenderGroupDepth+1,e.didChange=!0,this.onChildUpdate(e),e.renderGroup){this.addRenderGroupChild(e.renderGroup);return}e._onRender&&this.addOnRender(e);const t=e.children;for(let i=0;i<t.length;i++)this.addChild(t[i])}removeChild(e){if(this.structureDidChange=!0,e._onRender&&(e.renderGroup||this.removeOnRender(e)),e.parentRenderGroup=null,e.renderGroup){this._removeRenderGroupChild(e.renderGroup);return}const t=e.children;for(let i=0;i<t.length;i++)this.removeChild(t[i])}removeChildren(e){for(let t=0;t<e.length;t++)this.removeChild(e[t])}onChildUpdate(e){let t=this.childrenToUpdate[e.relativeRenderGroupDepth];t||(t=this.childrenToUpdate[e.relativeRenderGroupDepth]={index:0,list:[]}),t.list[t.index++]=e}updateRenderable(e){e.globalDisplayStatus<7||(this.instructionSet.renderPipes[e.renderPipeId].updateRenderable(e),e.didViewUpdate=!1)}onChildViewUpdate(e){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=e}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(e){this._onRenderContainers.push(e)}removeOnRender(e){this._onRenderContainers.splice(this._onRenderContainers.indexOf(e),1)}runOnRender(e){for(let t=0;t<this._onRenderContainers.length;t++)this._onRenderContainers[t]._onRender(e)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(e=[]){const t=this.root.children;for(let i=0;i<t.length;i++)this._getChildren(t[i],e);return e}_getChildren(e,t=[]){if(t.push(e),e.renderGroup)return t;const i=e.children;for(let n=0;n<i.length;n++)this._getChildren(i[n],t);return t}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return this._matrixDirty&1?(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new se),this._inverseWorldTransform.copyFrom(this.worldTransform).invert()):this._inverseWorldTransform}get textureOffsetInverseTransform(){return this._matrixDirty&2?(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new se),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y)):this._textureOffsetInverseTransform}get inverseParentTextureTransform(){if(!(this._matrixDirty&4))return this._inverseParentTextureTransform;this._matrixDirty&=-5;const e=this._parentCacheAsTextureRenderGroup;return e?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new se),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(e.inverseWorldTransform).translate(-e._textureBounds.x,-e._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function eu(s,e,t={}){for(const i in e)!t[i]&&e[i]!==void 0&&(s[i]=e[i])}const ys=new ve(null),An=new ve(null),vs=new ve(null,1,1),Cn=new ve(null),ra=1,tu=2,xs=4;class si extends gn{constructor(e={}){var t,i;super(),this.uid=_t("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new se,this.relativeGroupTransform=new se,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new ve(this,0,0),this._scale=vs,this._pivot=An,this._origin=Cn,this._skew=ys,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],eu(this,e,{children:!0,parent:!0,effects:!0}),(t=e.children)==null||t.forEach(n=>this.addChild(n)),(i=e.parent)==null||i.addChild(this)}static mixin(e){Je("8.8.0","Container.mixin is deprecated, please use extensions.mixin instead."),ht.mixin(si,e)}set _didChangeId(e){this._didViewChangeTick=e>>12&4095,this._didContainerChangeTick=e&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...e){if(this.allowChildren||Je(xt,"addChild: Only Containers will be allowed to add children in v8.0.0"),e.length>1){for(let n=0;n<e.length;n++)this.addChild(e[n]);return e[0]}const t=e[0],i=this.renderGroup||this.parentRenderGroup;return t.parent===this?(this.children.splice(this.children.indexOf(t),1),this.children.push(t),i&&(i.structureDidChange=!0),t):(t.parent&&t.parent.removeChild(t),this.children.push(t),this.sortableChildren&&(this.sortDirty=!0),t.parent=this,t.didChange=!0,t._updateFlags=15,i&&i.addChild(t),this.emit("childAdded",t,this,this.children.length-1),t.emit("added",this),this._didViewChangeTick++,t._zIndex!==0&&t.depthOfChildModified(),t)}removeChild(...e){if(e.length>1){for(let n=0;n<e.length;n++)this.removeChild(e[n]);return e[0]}const t=e[0],i=this.children.indexOf(t);return i>-1&&(this._didViewChangeTick++,this.children.splice(i,1),this.renderGroup?this.renderGroup.removeChild(t):this.parentRenderGroup&&this.parentRenderGroup.removeChild(t),t.parentRenderLayer&&t.parentRenderLayer.detach(t),t.parent=null,this.emit("childRemoved",t,this,i),t.emit("removed",this)),t}_onUpdate(e){e&&e===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(e){!!this.renderGroup!==e&&(e?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),this.renderGroup=zn.get(Zh,this),this.groupTransform=se.IDENTITY,e==null||e.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),zn.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,e==null||e.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new se),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(e){this._position.x=e}get y(){return this._position.y}set y(e){this._position.y=e}get position(){return this._position}set position(e){this._position.copyFrom(e)}get rotation(){return this._rotation}set rotation(e){this._rotation!==e&&(this._rotation=e,this._onUpdate(this._skew))}get angle(){return this.rotation*dh}set angle(e){this.rotation=e*fh}get pivot(){return this._pivot===An&&(this._pivot=new ve(this,0,0)),this._pivot}set pivot(e){this._pivot===An&&(this._pivot=new ve(this,0,0),this._origin!==Cn&&St("Setting both a pivot and origin on a Container is not recommended. This can lead to unexpected behavior if not handled carefully.")),typeof e=="number"?this._pivot.set(e):this._pivot.copyFrom(e)}get skew(){return this._skew===ys&&(this._skew=new ve(this,0,0)),this._skew}set skew(e){this._skew===ys&&(this._skew=new ve(this,0,0)),this._skew.copyFrom(e)}get scale(){return this._scale===vs&&(this._scale=new ve(this,1,1)),this._scale}set scale(e){this._scale===vs&&(this._scale=new ve(this,0,0)),typeof e=="string"&&(e=parseFloat(e)),typeof e=="number"?this._scale.set(e):this._scale.copyFrom(e)}get origin(){return this._origin===Cn&&(this._origin=new ve(this,0,0)),this._origin}set origin(e){this._origin===Cn&&(this._origin=new ve(this,0,0),this._pivot!==An&&St("Setting both a pivot and origin on a Container is not recommended. This can lead to unexpected behavior if not handled carefully.")),typeof e=="number"?this._origin.set(e):this._origin.copyFrom(e)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(e){const t=this.getLocalBounds().width;this._setWidth(e,t)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(e){const t=this.getLocalBounds().height;this._setHeight(e,t)}getSize(e){e||(e={});const t=this.getLocalBounds();return e.width=Math.abs(this.scale.x*t.width),e.height=Math.abs(this.scale.y*t.height),e}setSize(e,t){const i=this.getLocalBounds();typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,i.width),t!==void 0&&this._setHeight(t,i.height)}_updateSkew(){const e=this._rotation,t=this._skew;this._cx=Math.cos(e+t._y),this._sx=Math.sin(e+t._y),this._cy=-Math.sin(e-t._x),this._sy=Math.cos(e-t._x)}updateTransform(e){return this.position.set(typeof e.x=="number"?e.x:this.position.x,typeof e.y=="number"?e.y:this.position.y),this.scale.set(typeof e.scaleX=="number"?e.scaleX||1:this.scale.x,typeof e.scaleY=="number"?e.scaleY||1:this.scale.y),this.rotation=typeof e.rotation=="number"?e.rotation:this.rotation,this.skew.set(typeof e.skewX=="number"?e.skewX:this.skew.x,typeof e.skewY=="number"?e.skewY:this.skew.y),this.pivot.set(typeof e.pivotX=="number"?e.pivotX:this.pivot.x,typeof e.pivotY=="number"?e.pivotY:this.pivot.y),this.origin.set(typeof e.originX=="number"?e.originX:this.origin.x,typeof e.originY=="number"?e.originY:this.origin.y),this}setFromMatrix(e){e.decompose(this)}updateLocalTransform(){const e=this._didContainerChangeTick;if(this._didLocalTransformChangeId===e)return;this._didLocalTransformChangeId=e;const t=this.localTransform,i=this._scale,n=this._pivot,r=this._origin,a=this._position,o=i._x,c=i._y,l=n._x,h=n._y,u=-r._x,f=-r._y;t.a=this._cx*o,t.b=this._sx*o,t.c=this._cy*c,t.d=this._sy*c,t.tx=a._x-(l*t.a+h*t.c)+(u*t.a+f*t.c)-u,t.ty=a._y-(l*t.b+h*t.d)+(u*t.b+f*t.d)-f}set alpha(e){e!==this.localAlpha&&(this.localAlpha=e,this._updateFlags|=ra,this._onUpdate())}get alpha(){return this.localAlpha}set tint(e){const i=Dn.shared.setValue(e??16777215).toBgrNumber();i!==this.localColor&&(this.localColor=i,this._updateFlags|=ra,this._onUpdate())}get tint(){return On(this.localColor)}set blendMode(e){this.localBlendMode!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=tu,this.localBlendMode=e,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(e){const t=e?2:0;(this.localDisplayStatus&2)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=xs,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(e){const t=e?0:4;(this.localDisplayStatus&4)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=xs,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(e){const t=e?1:0;(this.localDisplayStatus&1)!==t&&(this._updateFlags|=xs,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(e=!1){var n;if(this.destroyed)return;this.destroyed=!0;let t;if(this.children.length&&(t=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._origin=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof e=="boolean"?e:e==null?void 0:e.children)&&t)for(let r=0;r<t.length;++r)t[r].destroy(e);(n=this.renderGroup)==null||n.destroy(),this.renderGroup=null}}ht.mixin(si,Oh,Bh,Hh,Yh,Gh,Fh,Lh,Xh,Eh,Ih,qh,$h);class iu extends si{constructor(e){super(e),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._gpuData=Object.create(null),this._bounds=new It(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(e){this._roundPixels=e?1:0}containsPoint(e){const t=this.bounds,{x:i,y:n}=e;return i>=t.minX&&i<=t.maxX&&n>=t.minY&&n<=t.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const e=this.renderGroup||this.parentRenderGroup;e&&e.onChildViewUpdate(this)}destroy(e){var t,i;super.destroy(e),this._bounds=null;for(const n in this._gpuData)(i=(t=this._gpuData[n]).destroy)==null||i.call(t);this._gpuData=null}collectRenderablesSimple(e,t,i){const{renderPipes:n}=t;n.blendMode.setBlendMode(this,this.groupBlendMode,e),n[this.renderPipeId].addRenderable(this,e),this.didViewUpdate=!1;const a=this.children,o=a.length;for(let c=0;c<o;c++)a[c].collectRenderables(e,t,i)}}class nn extends iu{constructor(e=me.EMPTY){e instanceof me&&(e={texture:e});const{texture:t=me.EMPTY,anchor:i,roundPixels:n,width:r,height:a,...o}=e;super({label:"Sprite",...o}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new ve({_onUpdate:()=>{this.onViewUpdate()}}),i?this.anchor=i:t.defaultAnchor&&(this.anchor=t.defaultAnchor),this.texture=t,this.allowChildren=!1,this.roundPixels=n??!1,r!==void 0&&(this.width=r),a!==void 0&&(this.height=a)}static from(e,t=!1){return e instanceof me?new nn(e):new nn(me.from(e,t))}set texture(e){e||(e=me.EMPTY);const t=this._texture;t!==e&&(t&&t.dynamic&&t.off("update",this.onViewUpdate,this),e.dynamic&&e.on("update",this.onViewUpdate,this),this._texture=e,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return xh(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return Je("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const e=this._anchor,t=this._texture,i=this._bounds,{width:n,height:r}=t.orig;i.minX=-e._x*n,i.maxX=i.minX+n,i.minY=-e._y*r,i.maxY=i.minY+r}destroy(e=!1){if(super.destroy(e),typeof e=="boolean"?e:e==null?void 0:e.texture){const i=typeof e=="boolean"?e:e==null?void 0:e.textureSource;this._texture.destroy(i)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null,this._gpuData=null}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(e){this._setWidth(e,this._texture.orig.width),this._width=e}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(e){this._setHeight(e,this._texture.orig.height),this._height=e}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this._texture.orig.width,e.height=Math.abs(this.scale.y)*this._texture.orig.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this._texture.orig.width),t!==void 0&&this._setHeight(t,this._texture.orig.height)}}const nu=new It;function yo(s,e,t){const i=nu;s.measurable=!0,fo(s,t,i),e.addBoundsMask(i),s.measurable=!1}function vo(s,e,t){const i=yt.get();s.measurable=!0;const n=Te.get().identity(),r=xo(s,t,n);po(s,i,r),s.measurable=!1,e.addBoundsMask(i),Te.return(n),yt.return(i)}function xo(s,e,t){return s?(s!==e&&(xo(s.parent,e,t),s.updateLocalTransform(),t.append(s.localTransform)),t):(St("Mask bounds, renderable is not inside the root container"),t)}class bo{constructor(e){this.priority=0,this.inverse=!1,this.pipe="alphaMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.renderMaskToTexture=!(e instanceof nn),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(e,t){this.inverse||yo(this.mask,e,t)}addLocalBounds(e,t){vo(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof nn}}bo.extension=de.MaskEffect;class wo{constructor(e){this.priority=0,this.pipe="colorMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e}destroy(){}static test(e){return typeof e=="number"}}wo.extension=de.MaskEffect;class Ao{constructor(e){this.priority=0,this.pipe="stencilMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(e,t){yo(this.mask,e,t)}addLocalBounds(e,t){vo(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof si}}Ao.extension=de.MaskEffect;const su={createCanvas:(s,e)=>{const t=document.createElement("canvas");return t.width=s,t.height=e,t},createImage:()=>new Image,getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,e)=>fetch(s,e),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let aa=su;const Ai={get(){return aa},set(s){aa=s}};class Co extends it{constructor(e){e.resource||(e.resource=Ai.get().createCanvas()),e.width||(e.width=e.resource.width,e.autoDensity||(e.width/=e.resolution)),e.height||(e.height=e.resource.height,e.autoDensity||(e.height/=e.resolution)),super(e),this.uploadMethodId="image",this.autoDensity=e.autoDensity,this.resizeCanvas(),this.transparent=!!e.transparent}resizeCanvas(){this.autoDensity&&"style"in this.resource&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(e=this.width,t=this.height,i=this._resolution){const n=super.resize(e,t,i);return n&&this.resizeCanvas(),n}static test(e){return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&e instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}Co.extension=de.TextureSource;class To extends it{constructor(e){super(e),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(e){return globalThis.HTMLImageElement&&e instanceof HTMLImageElement||typeof ImageBitmap<"u"&&e instanceof ImageBitmap||globalThis.VideoFrame&&e instanceof VideoFrame}}To.extension=de.TextureSource;var zs=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(zs||{});class bs{constructor(e,t=null,i=0,n=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=e,this._context=t,this.priority=i,this._once=n}match(e,t=null){return this._fn===e&&this._context===t}emit(e){this._fn&&(this._context?this._fn.call(this._context,e):this._fn(e));const t=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),t}connect(e){this.previous=e,e.next&&(e.next.previous=this),this.next=e.next,e.next=this}destroy(e=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const t=this.next;return this.next=e?null:t,this.previous=null,t}}const ko=class De{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new bs(null,null,1/0),this.deltaMS=1/De.targetFPMS,this.elapsedMS=1/De.targetFPMS,this._tick=e=>{this._requestId=null,this.started&&(this.update(e),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(e,t,i=zs.NORMAL){return this._addListener(new bs(e,t,i))}addOnce(e,t,i=zs.NORMAL){return this._addListener(new bs(e,t,i,!0))}_addListener(e){let t=this._head.next,i=this._head;if(!t)e.connect(i);else{for(;t;){if(e.priority>t.priority){e.connect(i);break}i=t,t=t.next}e.previous||e.connect(i)}return this._startIfPossible(),this}remove(e,t){let i=this._head.next;for(;i;)i.match(e,t)?i=i.destroy():i=i.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let e=0,t=this._head;for(;t=t.next;)e++;return e}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let e=this._head.next;for(;e;)e=e.destroy(!0);this._head.destroy(),this._head=null}}update(e=performance.now()){let t;if(e>this.lastTime){if(t=this.elapsedMS=e-this.lastTime,t>this._maxElapsedMS&&(t=this._maxElapsedMS),t*=this.speed,this._minElapsedMS){const r=e-this._lastFrame|0;if(r<this._minElapsedMS)return;this._lastFrame=e-r%this._minElapsedMS}this.deltaMS=t,this.deltaTime=this.deltaMS*De.targetFPMS;const i=this._head;let n=i.next;for(;n;)n=n.emit(this);i.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=e}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(e){const t=Math.min(this.maxFPS,e),i=Math.min(Math.max(0,t)/1e3,De.targetFPMS);this._maxElapsedMS=1/i}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(e){if(e===0)this._minElapsedMS=0;else{const t=Math.max(this.minFPS,e);this._minElapsedMS=1/(t/1e3)}}static get shared(){if(!De._shared){const e=De._shared=new De;e.autoStart=!0,e._protected=!0}return De._shared}static get system(){if(!De._system){const e=De._system=new De;e.autoStart=!0,e._protected=!0}return De._system}};ko.targetFPMS=.06;let Tn=ko,ws;async function ru(){return ws??(ws=(async()=>{var a;const e=Ai.get().createCanvas(1,1).getContext("webgl");if(!e)return"premultiply-alpha-on-upload";const t=await new Promise(o=>{const c=document.createElement("video");c.onloadeddata=()=>o(c),c.onerror=()=>o(null),c.autoplay=!1,c.crossOrigin="anonymous",c.preload="auto",c.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",c.load()});if(!t)return"premultiply-alpha-on-upload";const i=e.createTexture();e.bindTexture(e.TEXTURE_2D,i);const n=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,n),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,i,0),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.NONE),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);const r=new Uint8Array(4);return e.readPixels(0,0,1,1,e.RGBA,e.UNSIGNED_BYTE,r),e.deleteFramebuffer(n),e.deleteTexture(i),(a=e.getExtension("WEBGL_lose_context"))==null||a.loseContext(),r[0]<=r[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),ws}const ns=class Po extends it{constructor(e){super(e),this.isReady=!1,this.uploadMethodId="video",e={...Po.defaultOptions,...e},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this.alphaMode=e.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const e=Tn.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const e=this.resource,t=this.options;return(e.readyState===e.HAVE_ENOUGH_DATA||e.readyState===e.HAVE_FUTURE_DATA)&&e.width&&e.height&&(e.complete=!0),e.addEventListener("play",this._onPlayStart),e.addEventListener("pause",this._onPlayStop),e.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(t.preload||e.addEventListener("canplay",this._onCanPlay),e.addEventListener("canplaythrough",this._onCanPlayThrough),e.addEventListener("error",this._onError,!0)),this.alphaMode=await ru(),this._load=new Promise((i,n)=>{this.isValid?i(this):(this._resolve=i,this._reject=n,t.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${t.preloadTimeoutMs}ms`))})),e.load())}),this._load}_onError(e){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",e),this._reject&&(this._reject(e),this._reject=null,this._resolve=null)}_isSourcePlaying(){const e=this.resource;return!e.paused&&!e.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const e=this.resource;this.isValid&&(this.isReady=!0,this.resize(e.videoWidth,e.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const e=this.resource;e&&(e.removeEventListener("play",this._onPlayStart),e.removeEventListener("pause",this._onPlayStop),e.removeEventListener("seeked",this._onSeeked),e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlayThrough),e.removeEventListener("error",this._onError,!0),e.pause(),e.src="",e.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(e){e!==this._autoUpdate&&(this._autoUpdate=e,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(e){e!==this._updateFPS&&(this._updateFPS=e,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(Tn.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(Tn.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(Tn.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(e){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement}};ns.extension=de.TextureSource;ns.defaultOptions={...it.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};ns.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let au=ns;const ci=(s,e,t=!1)=>(Array.isArray(s)||(s=[s]),e?s.map(i=>typeof i=="string"||t?e(i):i):s);class ou{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(e){return this._cache.has(e)}get(e){const t=this._cache.get(e);return t||St(`[Assets] Asset id ${e} was not found in the Cache`),t}set(e,t){const i=ci(e);let n;for(let c=0;c<this.parsers.length;c++){const l=this.parsers[c];if(l.test(t)){n=l.getCacheableAssets(i,t);break}}const r=new Map(Object.entries(n||{}));n||i.forEach(c=>{r.set(c,t)});const a=[...r.keys()],o={cacheKeys:a,keys:i};i.forEach(c=>{this._cacheMap.set(c,o)}),a.forEach(c=>{const l=n?n[c]:t;this._cache.has(c)&&this._cache.get(c)!==l&&St("[Cache] already has key:",c),this._cache.set(c,r.get(c))})}remove(e){if(!this._cacheMap.has(e)){St(`[Assets] Asset id ${e} was not found in the Cache`);return}const t=this._cacheMap.get(e);t.cacheKeys.forEach(n=>{this._cache.delete(n)}),t.keys.forEach(n=>{this._cacheMap.delete(n)})}get parsers(){return this._parsers}}const hi=new ou,Gs=[];ht.handleByList(de.TextureSource,Gs);function Mo(s={}){const e=s&&s.resource,t=e?s.resource:s,i=e?s:{resource:s};for(let n=0;n<Gs.length;n++){const r=Gs[n];if(r.test(t))return new r(i)}throw new Error(`Could not find a source type for resource: ${i.resource}`)}function lu(s={},e=!1){const t=s&&s.resource,i=t?s.resource:s,n=t?s:{resource:s};if(!e&&hi.has(i))return hi.get(i);const r=new me({source:Mo(n)});return r.on("destroy",()=>{hi.has(i)&&hi.remove(i)}),e||hi.set(i,r),r}function cu(s,e=!1){return typeof s=="string"?hi.get(s):s instanceof it?new me({source:s}):lu(s,e)}me.from=cu;it.from=Mo;ht.add(bo,wo,Ao,au,To,Co,_r);var So=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(So||{});function et(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function Vi(s){return s.split("?")[0].split("#")[0]}function hu(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function uu(s,e,t){return s.replace(new RegExp(hu(e),"g"),t)}function du(s,e){let t="",i=0,n=-1,r=0,a=-1;for(let o=0;o<=s.length;++o){if(o<s.length)a=s.charCodeAt(o);else{if(a===47)break;a=47}if(a===47){if(!(n===o-1||r===1))if(n!==o-1&&r===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){const c=t.lastIndexOf("/");if(c!==t.length-1){c===-1?(t="",i=0):(t=t.slice(0,c),i=t.length-1-t.lastIndexOf("/")),n=o,r=0;continue}}else if(t.length===2||t.length===1){t="",i=0,n=o,r=0;continue}}}else t.length>0?t+=`/${s.slice(n+1,o)}`:t=s.slice(n+1,o),i=o-n-1;n=o,r=0}else a===46&&r!==-1?++r:r=-1}return t}const sn={toPosix(s){return uu(s,"\\","/")},isUrl(s){return/^https?:/.test(this.toPosix(s))},isDataUrl(s){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return/^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){et(s),s=this.toPosix(s);const e=/^file:\/\/\//.exec(s);if(e)return e[0];const t=/^[^/:]+:\/{0,2}/.exec(s);return t?t[0]:""},toAbsolute(s,e,t){if(et(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const i=Vi(this.toPosix(e??Ai.get().getBaseUrl())),n=Vi(this.toPosix(t??this.rootname(i)));return s=this.toPosix(s),s.startsWith("/")?sn.join(n,s.slice(1)):this.isAbsolute(s)?s:this.join(i,s)},normalize(s){if(et(s),s.length===0)return".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let e="";const t=s.startsWith("/");this.hasProtocol(s)&&(e=this.rootname(s),s=s.slice(e.length));const i=s.endsWith("/");return s=du(s),s.length>0&&i&&(s+="/"),t?`/${s}`:e+s},isAbsolute(s){return et(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return".";let e;for(let t=0;t<s.length;++t){const i=s[t];if(et(i),i.length>0)if(e===void 0)e=i;else{const n=s[t-1]??"";this.joinExtensions.includes(this.extname(n).toLowerCase())?e+=`/../${i}`:e+=`/${i}`}}return e===void 0?".":this.normalize(e)},dirname(s){if(et(s),s.length===0)return".";s=this.toPosix(s);let e=s.charCodeAt(0);const t=e===47;let i=-1,n=!0;const r=this.getProtocol(s),a=s;s=s.slice(r.length);for(let o=s.length-1;o>=1;--o)if(e=s.charCodeAt(o),e===47){if(!n){i=o;break}}else n=!1;return i===-1?t?"/":this.isUrl(a)?r+s:r:t&&i===1?"//":r+s.slice(0,i)},rootname(s){et(s),s=this.toPosix(s);let e="";if(s.startsWith("/")?e="/":e=this.getProtocol(s),this.isUrl(s)){const t=s.indexOf("/",e.length);t!==-1?e=s.slice(0,t):e=s,e.endsWith("/")||(e+="/")}return e},basename(s,e){et(s),e&&et(e),s=Vi(this.toPosix(s));let t=0,i=-1,n=!0,r;if(e!==void 0&&e.length>0&&e.length<=s.length){if(e.length===s.length&&e===s)return"";let a=e.length-1,o=-1;for(r=s.length-1;r>=0;--r){const c=s.charCodeAt(r);if(c===47){if(!n){t=r+1;break}}else o===-1&&(n=!1,o=r+1),a>=0&&(c===e.charCodeAt(a)?--a===-1&&(i=r):(a=-1,i=o))}return t===i?i=o:i===-1&&(i=s.length),s.slice(t,i)}for(r=s.length-1;r>=0;--r)if(s.charCodeAt(r)===47){if(!n){t=r+1;break}}else i===-1&&(n=!1,i=r+1);return i===-1?"":s.slice(t,i)},extname(s){et(s),s=Vi(this.toPosix(s));let e=-1,t=0,i=-1,n=!0,r=0;for(let a=s.length-1;a>=0;--a){const o=s.charCodeAt(a);if(o===47){if(!n){t=a+1;break}continue}i===-1&&(n=!1,i=a+1),o===46?e===-1?e=a:r!==1&&(r=1):e!==-1&&(r=-1)}return e===-1||i===-1||r===0||r===1&&e===i-1&&e===t+1?"":s.slice(e,i)},parse(s){et(s);const e={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return e;s=Vi(this.toPosix(s));let t=s.charCodeAt(0);const i=this.isAbsolute(s);let n;e.root=this.rootname(s),i||this.hasProtocol(s)?n=1:n=0;let r=-1,a=0,o=-1,c=!0,l=s.length-1,h=0;for(;l>=n;--l){if(t=s.charCodeAt(l),t===47){if(!c){a=l+1;break}continue}o===-1&&(c=!1,o=l+1),t===46?r===-1?r=l:h!==1&&(h=1):r!==-1&&(h=-1)}return r===-1||o===-1||h===0||h===1&&r===o-1&&r===a+1?o!==-1&&(a===0&&i?e.base=e.name=s.slice(1,o):e.base=e.name=s.slice(a,o)):(a===0&&i?(e.name=s.slice(1,r),e.base=s.slice(1,o)):(e.name=s.slice(a,r),e.base=s.slice(a,o)),e.ext=s.slice(r,o)),e.dir=this.dirname(s),e},sep:"/",delimiter:":",joinExtensions:[".html"]};function Eo(s,e,t,i,n){const r=e[t];for(let a=0;a<r.length;a++){const o=r[a];t<e.length-1?Eo(s.replace(i[t],o),e,t+1,i,n):n.push(s.replace(i[t],o))}}function fu(s){const e=/\{(.*?)\}/g,t=s.match(e),i=[];if(t){const n=[];t.forEach(r=>{const a=r.substring(1,r.length-1).split(",");n.push(a)}),Eo(s,n,0,t,i)}else i.push(s);return i}const oa=s=>!Array.isArray(s);class Ro{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(e,t)=>`${e}${this._bundleIdConnector}${t}`,extractAssetIdFromBundle:(e,t)=>t.replace(`${e}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(e){if(this._bundleIdConnector=e.connector??this._bundleIdConnector,this._createBundleAssetId=e.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=e.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...e){e.forEach(t=>{this._preferredOrder.push(t),t.priority||(t.priority=Object.keys(t.params))}),this._resolverHash={}}set basePath(e){this._basePath=e}get basePath(){return this._basePath}set rootPath(e){this._rootPath=e}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(e){if(typeof e=="string")this._defaultSearchParams=e;else{const t=e;this._defaultSearchParams=Object.keys(t).map(i=>`${encodeURIComponent(i)}=${encodeURIComponent(t[i])}`).join("&")}}getAlias(e){const{alias:t,src:i}=e;return ci(t||i,r=>typeof r=="string"?r:Array.isArray(r)?r.map(a=>(a==null?void 0:a.src)??a):r!=null&&r.src?r.src:r,!0)}addManifest(e){this._manifest&&St("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=e,e.bundles.forEach(t=>{this.addBundle(t.name,t.assets)})}addBundle(e,t){const i=[];let n=t;Array.isArray(t)||(n=Object.entries(t).map(([r,a])=>typeof a=="string"||Array.isArray(a)?{alias:r,src:a}:{alias:r,...a})),n.forEach(r=>{const a=r.src,o=r.alias;let c;if(typeof o=="string"){const l=this._createBundleAssetId(e,o);i.push(l),c=[o,l]}else{const l=o.map(h=>this._createBundleAssetId(e,h));i.push(...l),c=[...o,...l]}this.add({...r,alias:c,src:a})}),this._bundles[e]=i}add(e){const t=[];Array.isArray(e)?t.push(...e):t.push(e);let i;i=r=>{this.hasKey(r)&&St(`[Resolver] already has key: ${r} overwriting`)},ci(t).forEach(r=>{const{src:a}=r;let{data:o,format:c,loadParser:l,parser:h}=r;const u=ci(a).map(g=>typeof g=="string"?fu(g):Array.isArray(g)?g:[g]),f=this.getAlias(r);Array.isArray(f)?f.forEach(i):i(f);const p=[];u.forEach(g=>{g.forEach(d=>{let m={};if(typeof d!="object"){m.src=d;for(let _=0;_<this._parsers.length;_++){const x=this._parsers[_];if(x.test(d)){m=x.parse(d);break}}}else o=d.data??o,c=d.format??c,(d.loadParser||d.parser)&&(l=d.loadParser??l,h=d.parser??h),m={...m,...d};if(!f)throw new Error(`[Resolver] alias is undefined for this asset: ${m.src}`);m=this._buildResolvedAsset(m,{aliases:f,data:o,format:c,loadParser:l,parser:h}),p.push(m)})}),f.forEach(g=>{this._assetMap[g]=p})})}resolveBundle(e){const t=oa(e);e=ci(e);const i={};return e.forEach(n=>{const r=this._bundles[n];if(r){const a=this.resolve(r),o={};for(const c in a){const l=a[c];o[this._extractAssetIdFromBundle(n,c)]=l}i[n]=o}}),t?i[e[0]]:i}resolveUrl(e){const t=this.resolve(e);if(typeof e!="string"){const i={};for(const n in t)i[n]=t[n].src;return i}return t.src}resolve(e){const t=oa(e);e=ci(e);const i={};return e.forEach(n=>{if(!this._resolverHash[n])if(this._assetMap[n]){let r=this._assetMap[n];const a=this._getPreferredOrder(r);a==null||a.priority.forEach(o=>{a.params[o].forEach(c=>{const l=r.filter(h=>h[o]?h[o]===c:!1);l.length&&(r=l)})}),this._resolverHash[n]=r[0]}else this._resolverHash[n]=this._buildResolvedAsset({alias:[n],src:n},{});i[n]=this._resolverHash[n]}),t?i[e[0]]:i}hasKey(e){return!!this._assetMap[e]}hasBundle(e){return!!this._bundles[e]}_getPreferredOrder(e){for(let t=0;t<e.length;t++){const i=e[t],n=this._preferredOrder.find(r=>r.params.format.includes(i.format));if(n)return n}return this._preferredOrder[0]}_appendDefaultSearchParams(e){if(!this._defaultSearchParams)return e;const t=/\?/.test(e)?"&":"?";return`${e}${t}${this._defaultSearchParams}`}_buildResolvedAsset(e,t){const{aliases:i,data:n,loadParser:r,parser:a,format:o}=t;return(this._basePath||this._rootPath)&&(e.src=sn.toAbsolute(e.src,this._basePath,this._rootPath)),e.alias=i??e.alias??[e.src],e.src=this._appendDefaultSearchParams(e.src),e.data={...n||{},...e.data},e.loadParser=r??e.loadParser,e.parser=a??e.parser,e.format=o??e.format??mu(e.src),e}}Ro.RETINA_PREFIX=/@([0-9\.]+)x/;function mu(s){return s.split(".").pop().split("?").shift().split("#").shift()}const la=(s,e)=>{const t=e.split("?")[1];return t&&(s+=`?${t}`),s},Io=class zi{constructor(e,t){this.linkedSheets=[];let i=e;(e==null?void 0:e.source)instanceof it&&(i={texture:e,data:t});const{texture:n,data:r,cachePrefix:a=""}=i;this.cachePrefix=a,this._texture=n instanceof me?n:null,this.textureSource=n.source,this.textures={},this.animations={},this.data=r;const o=parseFloat(r.meta.scale);o?(this.resolution=o,n.source.resolution=this.resolution):this.resolution=n.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(e=>{this._callback=e,this._batchIndex=0,this._frameKeys.length<=zi.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(e){let t=e;const i=zi.BATCH_SIZE;for(;t-e<i&&t<this._frameKeys.length;){const n=this._frameKeys[t],r=this._frames[n],a=r.frame;if(a){let o=null,c=null;const l=r.trimmed!==!1&&r.sourceSize?r.sourceSize:r.frame,h=new ot(0,0,Math.floor(l.w)/this.resolution,Math.floor(l.h)/this.resolution);r.rotated?o=new ot(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.h)/this.resolution,Math.floor(a.w)/this.resolution):o=new ot(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution),r.trimmed!==!1&&r.spriteSourceSize&&(c=new ot(Math.floor(r.spriteSourceSize.x)/this.resolution,Math.floor(r.spriteSourceSize.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution)),this.textures[n]=new me({source:this.textureSource,frame:o,orig:h,trim:c,rotate:r.rotated?2:0,defaultAnchor:r.anchor,defaultBorders:r.borders,label:n.toString()})}t++}}_processAnimations(){const e=this.data.animations||{};for(const t in e){this.animations[t]=[];for(let i=0;i<e[t].length;i++){const n=e[t][i];this.animations[t].push(this.textures[n])}}}_parseComplete(){const e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*zi.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*zi.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(e=!1){var t;for(const i in this.textures)this.textures[i].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&((t=this._texture)==null||t.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};Io.BATCH_SIZE=1e3;let ca=Io;const pu=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function Do(s,e,t){const i={};if(s.forEach(n=>{i[n]=e}),Object.keys(e.textures).forEach(n=>{i[`${e.cachePrefix}${n}`]=e.textures[n]}),!t){const n=sn.dirname(s[0]);e.linkedSheets.forEach((r,a)=>{const o=Do([`${n}/${e.data.meta.related_multi_packs[a]}`],r,!0);Object.assign(i,o)})}return i}const _u={extension:de.Asset,cache:{test:s=>s instanceof ca,getCacheableAssets:(s,e)=>Do(s,e,!1)},resolver:{extension:{type:de.ResolveParser,name:"resolveSpritesheet"},test:s=>{const t=s.split("?")[0].split("."),i=t.pop(),n=t.pop();return i==="json"&&pu.includes(n)},parse:s=>{var t;const e=s.split(".");return{resolution:parseFloat(((t=Ro.RETINA_PREFIX.exec(s))==null?void 0:t[1])??"1"),format:e[e.length-2],src:s}}},loader:{name:"spritesheetLoader",id:"spritesheet",extension:{type:de.LoadParser,priority:So.Normal,name:"spritesheetLoader"},async testParse(s,e){return sn.extname(e.src).toLowerCase()===".json"&&!!s.frames},async parse(s,e,t){var u,f;const{texture:i,imageFilename:n,textureOptions:r,cachePrefix:a}=(e==null?void 0:e.data)??{};let o=sn.dirname(e.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let c;if(i instanceof me)c=i;else{const p=la(o+(n??s.meta.image),e.src);c=(await t.load([{src:p,data:r}]))[p]}const l=new ca({texture:c.source,data:s,cachePrefix:a});await l.parse();const h=(u=s==null?void 0:s.meta)==null?void 0:u.related_multi_packs;if(Array.isArray(h)){const p=[];for(const d of h){if(typeof d!="string")continue;let m=o+d;(f=e.data)!=null&&f.ignoreMultiPack||(m=la(m,e.src),p.push(t.load({src:m,data:{textureOptions:r,ignoreMultiPack:!0}})))}const g=await Promise.all(p);l.linkedSheets=g,g.forEach(d=>{d.linkedSheets=[l].concat(l.linkedSheets.filter(m=>m!==d))})}return l},async unload(s,e,t){await t.unload(s.textureSource._sourceOrigin),s.destroy(!1)}}};ht.add(_u);const Ys=[];ht.handleByNamedList(de.Environment,Ys);async function gu(s){if(!s)for(let e=0;e<Ys.length;e++){const t=Ys[e];if(t.value.test()){await t.value.load();return}}}let Fi;function yu(){if(typeof Fi=="boolean")return Fi;try{Fi=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{Fi=!1}return Fi}var Oo=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(Oo||{});class vu{constructor(e){this.items=[],this._name=e}emit(e,t,i,n,r,a,o,c){const{name:l,items:h}=this;for(let u=0,f=h.length;u<f;u++)h[u][l](e,t,i,n,r,a,o,c);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const xu=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],$o=class Vo extends gn{constructor(e){super(),this.uid=_t("renderer"),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...xu,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await gu(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const i in this._systemsHash)e={...this._systemsHash[i].constructor.defaultOptions,...e};e={...Vo.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let i=0;i<this.runners.init.items.length;i++)await this.runners.init.items[i].init(e);this._initOptions=e}render(e,t){let i=e;if(i instanceof si&&(i={container:i},t&&(Je(xt,"passing a second argument is deprecated, please use render options instead"),i.target=t.renderTexture)),i.target||(i.target=this.view.renderTarget),i.target===this.view.renderTarget&&(this._lastObjectRendered=i.container,i.clearColor??(i.clearColor=this.background.colorRgba),i.clear??(i.clear=this.background.clearBeforeRender)),i.clearColor){const n=Array.isArray(i.clearColor)&&i.clearColor.length===4;i.clearColor=n?i.clearColor:Dn.shared.setValue(i.clearColor).toArray()}i.transform||(i.container.updateLocalTransform(),i.transform=i.container.localTransform),i.container.enableRenderGroup(),this.runners.prerender.emit(i),this.runners.renderStart.emit(i),this.runners.render.emit(i),this.runners.renderEnd.emit(i),this.runners.postrender.emit(i)}resize(e,t,i){const n=this.view.resolution;this.view.resize(e,t,i),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),i!==void 0&&i!==n&&this.runners.resolutionChange.emit(i)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=Oo.ALL);const{clear:i,clearColor:n,target:r}=e;Dn.shared.setValue(n??this.background.colorRgba),t.renderTarget.clear(r,i,Dn.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new vu(t)})}_addSystems(e){let t;for(t in e){const i=e[t];this._addSystem(i.value,i.name)}}_addSystem(e,t){const i=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=i,this._systemsHash[t]=i;for(const n in this.runners)this.runners[n].add(i);return this}_addPipes(e,t){const i=t.reduce((n,r)=>(n[r.name]=r.value,n),{});e.forEach(n=>{const r=n.value,a=n.name,o=i[a];this.renderPipes[a]=new r(this,o?new o:null)})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!yu())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};$o.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let Fo=$o,kn;function bu(s){return kn!==void 0||(kn=(()=>{var t;const e={stencil:!0,failIfMajorPerformanceCaveat:s??Fo.defaultOptions.failIfMajorPerformanceCaveat};try{if(!Ai.get().getWebGLRenderingContext())return!1;let n=Ai.get().createCanvas().getContext("webgl",e);const r=!!((t=n==null?void 0:n.getContextAttributes())!=null&&t.stencil);if(n){const a=n.getExtension("WEBGL_lose_context");a&&a.loseContext()}return n=null,r}catch{return!1}})()),kn}let Pn;async function wu(s={}){return Pn!==void 0||(Pn=await(async()=>{const e=Ai.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(s)).requestDevice(),!0}catch{return!1}})()),Pn}const ha=["webgl","webgpu","canvas"];async function Au(s){let e=[];s.preference?(e.push(s.preference),ha.forEach(r=>{r!==s.preference&&e.push(r)})):e=ha.slice();let t,i={};for(let r=0;r<e.length;r++){const a=e[r];if(a==="webgpu"&&await wu()){const{WebGPURenderer:o}=await Bn(async()=>{const{WebGPURenderer:c}=await import("./WebGPURenderer-BQmoXWXq.js");return{WebGPURenderer:c}},__vite__mapDeps([14,2,3,15,4,5,6,7,8,9,10,11,12,13]),import.meta.url);t=o,i={...s,...s.webgpu};break}else if(a==="webgl"&&bu(s.failIfMajorPerformanceCaveat??Fo.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:o}=await Bn(async()=>{const{WebGLRenderer:c}=await import("./WebGLRenderer-0icBlQOH.js");return{WebGLRenderer:c}},__vite__mapDeps([16,3,15,4,5,6,7,8,9,10,11,12,13]),import.meta.url);t=o,i={...s,...s.webgl};break}else if(a==="canvas")throw i={...s},new Error("CanvasRenderer is not yet implemented")}if(delete i.webgpu,delete i.webgl,!t)throw new Error("No available renderer for the current environment");const n=new t;return await n.init(i),n}const Lo="8.12.0";class Uo{static init(){var e;(e=globalThis.__PIXI_APP_INIT__)==null||e.call(globalThis,this,Lo)}static destroy(){}}Uo.extension=de.Application;class Cu{constructor(e){this._renderer=e}init(){var e;(e=globalThis.__PIXI_RENDERER_INIT__)==null||e.call(globalThis,this._renderer,Lo)}destroy(){this._renderer=null}}Cu.extension={type:[de.WebGLSystem,de.WebGPUSystem],name:"initHook",priority:-10};const Bo=class Xs{constructor(...e){this.stage=new si,e[0]!==void 0&&Je(xt,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await Au(e),Xs._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return Je(xt,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const i=Xs._plugins.slice(0);i.reverse(),i.forEach(n=>{n.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};Bo._plugins=[];let No=Bo;ht.handleByList(de.Application,No._plugins);ht.add(Uo);ht.add(lh,ch);function Tu(s){const e=vt(),t=U(null),i=U(null),n=()=>{try{t.value=new No({width:window.innerWidth,height:window.innerHeight,background:"#f5f5f5",resolution:window.devicePixelRatio||1,autoDensity:!0}),i.value=t.value.stage;const o=document.getElementById(s);o&&t.value.view&&o.appendChild(t.value.view),e.setPixiApp(t.value),window.addEventListener("resize",r),console.log("Pixi应用初始化成功")}catch(o){console.error("Pixi应用初始化失败:",o)}},r=()=>{t.value&&(t.value.resizeTo=window)};return{pixiApp:t,stage:i,initPixi:n,cleanup:()=>{t.value&&(t.value.destroy(!0),t.value=null,i.value=null,e.clearInstances(),window.removeEventListener("resize",r))}}}const ku=Ze({__name:"PixiCanvas",setup(s){const e=U(null);let t=null;return ti(()=>{if(e.value){const i=`pixi-container-${Date.now()}`;e.value.id=i,t=Tu(i),t.initPixi()}}),fn(()=>{t&&(t.cleanup(),t=null)}),(i,n)=>(I(),O("div",{ref_key:"canvasContainer",ref:e,class:"pixi-canvas-container",style:{width:"100%",height:"100%"}},null,512))}}),Pu=Be(ku,[["__scopeId","data-v-932171ff"]]),Mu={class:"property-panel"},Su=Ze({__name:"PropertyPanel",setup(s){const e=["#ff4500","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585"],t=ni(),i=gi({name:"",background:void 0,grid:!1,gridSize:10,gridColor:void 0,rule:!0});let n;ti(()=>{const a=meta2d.data(),o=meta2d.getOptions();i.name=t.currentProject.name||a.name||"未命名项目",i.background=o.background,i.grid=o.grid??!1,i.gridSize=o.gridSize??10,i.gridColor=o.gridColor,i.rule=o.rule??!1,n=o.gridColor});const r=()=>{const a={...i};(i.gridColor===null||i.gridColor===void 0||i.gridColor==="")&&(n?(a.gridColor=n,i.gridColor=n):(delete a.gridColor,i.gridColor=void 0)),meta2d.setOptions(a),meta2d.store.patchFlagsTop=!0,meta2d.store.patchFlagsBackground=!0,t.currentProject.name=i.name||"未命名项目",t.currentProject.variables={...t.currentProject.variables,color:i.background,background:i.background,gridSize:i.gridSize,gridColor:i.gridColor,rule:i.rule},t.currentProject.updatedAt=new Date().toISOString(),meta2d.render()};return(a,o)=>{const c=mn,l=Ri,h=Ya,u=mr,f=pn,p=Xa,g=Ei;return I(),O("div",Mu,[w(g,{"label-align":"left"},{default:P(()=>[w(l,{label:"图纸名称",prop:"name"},{default:P(()=>[w(c,{modelValue:i.name,"onUpdate:modelValue":o[0]||(o[0]=d=>i.name=d),onChange:r},null,8,["modelValue"])]),_:1}),w(h),w(l,{label:"网格",prop:"grid"},{default:P(()=>[w(u,{modelValue:i.grid,"onUpdate:modelValue":o[1]||(o[1]=d=>i.grid=d),onChange:r},null,8,["modelValue"])]),_:1}),w(l,{label:"网格大小",prop:"gridSize"},{default:P(()=>[w(f,{modelValue:i.gridSize,"onUpdate:modelValue":o[2]||(o[2]=d=>i.gridSize=d),onChange:r},null,8,["modelValue"])]),_:1}),w(l,{label:"网格颜色",prop:"gridColor"},{default:P(()=>[w(p,{class:"w-full",modelValue:i.gridColor,"onUpdate:modelValue":o[3]||(o[3]=d=>i.gridColor=d),"show-alpha":!0,predefine:e,onChange:r},null,8,["modelValue"])]),_:1}),w(h),w(l,{label:"标尺",prop:"rule"},{default:P(()=>[w(u,{modelValue:i.rule,"onUpdate:modelValue":o[4]||(o[4]=d=>i.rule=d),onChange:r},null,8,["modelValue"])]),_:1}),w(h),w(l,{label:"背景颜色",prop:"background"},{default:P(()=>[w(p,{class:"w-full",modelValue:i.background,"onUpdate:modelValue":o[5]||(o[5]=d=>i.background=d),"show-alpha":!0,predefine:e,onChange:r},null,8,["modelValue"])]),_:1})]),_:1})])}}}),Eu=Be(Su,[["__scopeId","data-v-43207d24"]]),Ru={class:"data-panel"},Iu={class:"id-display"},Du={class:"panel-content"},Ou={key:0,class:"empty-state"},$u={key:1,class:"data-list"},Vu={class:"data-actions"},Fu={class:"property-selection"},Lu={class:"bind-variable-content"},Uu={class:"current-binding"},Bu={class:"variable-result"},Nu={key:0,class:"no-variable"},qu=Ze({__name:"DataPanel",setup(s){vt();const{selections:e}=Oi(),t=U([]),i=U(!1),n=U(!1),r=U(""),a=U(""),o=U(""),c=U(""),l=U(""),h=[{name:"背景颜色",key:"background",type:"string",display:"color"},{name:"画笔颜色",key:"color",type:"string",display:"color"},{name:"文字",key:"text",type:"string",display:"text"},{name:"x坐标",key:"x",type:"number",display:"number"},{name:"y坐标",key:"y",type:"number",display:"number"},{name:"宽度",key:"width",type:"number",display:"number"},{name:"高度",key:"height",type:"number",display:"number"},{name:"显示",key:"visible",type:"boolean",display:"boolean"},{name:"进度值",key:"progress",type:"number",display:"number"},{name:"进度颜色",key:"progressColor",type:"string",display:"color"},{name:"旋转",key:"rotate",type:"number",display:"number"},{name:"禁用",key:"disabled",type:"boolean",display:"boolean"}],u=["temperature","pressure","flow_rate","alarm"],f=Xe(()=>e.pens&&e.pens.length>0&&e.pens[0].id||""),p=Xe(()=>{if(!a.value)return h;const C=a.value.toLowerCase();return h.filter(y=>y.name.toLowerCase().includes(C)||y.key.toLowerCase().includes(C)||y.type.toLowerCase().includes(C))}),g=Xe(()=>l.value?u.filter(C=>C.toLowerCase().includes(l.value.toLowerCase())):u),d=C=>{const y=t.value.find(v=>v.name===C);return(y==null?void 0:y.binding)||""},m=()=>{if(!r.value)return;const C=h.find(v=>v.key===r.value);if(!C)return;if(t.value.some(v=>v.name===C.name)){j.warning(`数据项"${C.name}"已存在`);return}let y="";if(e.pens&&e.pens.length>0){const v=e.pens[0];console.log("选中的pen",v.background),C.key in v?y=v[C.key]:y=String(_(C.type))}else y=String(_(C.type));t.value.push({name:C.name,value:y}),r.value="",a.value="",i.value=!1,j.success("数据项添加成功")},_=C=>{switch(C){case"string":return"";case"number":return 0;case"boolean":return!0;default:return"默认值"}},x=(C,y)=>{C==="delete"&&(t.value.splice(y,1),j.success("数据项已删除"))},b=C=>{o.value=C,c.value=d(C),n.value=!0},T=()=>{o.value="",c.value="",l.value="",n.value=!1},k=()=>{const C=t.value.find(y=>y.name===o.value);C&&(C.binding=c.value,j.success(`已将"${o.value}"绑定到变量"${c.value}"`)),T()};return(C,y)=>{const v=ii,A=Tt,E=Yt,S=Gt,R=mn,V=Wa,X=dc,q=uc,B=Ha,G=fc,$=Qa;return I(),O("div",Ru,[M("div",Iu," ID: "+z(f.value),1),M("div",Du,[t.value.length===0?(I(),O("div",Ou,[y[10]||(y[10]=cc('<div class="empty-illustration" data-v-7c601fd1><div class="box-icon" data-v-7c601fd1><svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-7c601fd1><rect x="12" y="28" width="40" height="20" rx="2" fill="#3a8ee6" data-v-7c601fd1></rect><rect x="16" y="12" width="32" height="16" rx="2" fill="#409eff" stroke="#66b1ff" stroke-width="2" data-v-7c601fd1></rect><circle cx="40" cy="20" r="2" fill="white" data-v-7c601fd1></circle></svg></div><div class="clouds" data-v-7c601fd1><div class="cloud cloud-1" data-v-7c601fd1></div><div class="cloud cloud-2" data-v-7c601fd1></div><div class="cloud cloud-3" data-v-7c601fd1></div></div></div><p class="empty-text" data-v-7c601fd1>还没有动态数据</p>',2)),w(v,{type:"primary",onClick:y[0]||(y[0]=Y=>i.value=!0)},{default:P(()=>y[9]||(y[9]=[N("添加动态数据",-1)])),_:1,__:[9]})])):(I(),O("div",$u,[y[13]||(y[13]=M("div",{class:"data-list-header"},[M("span",{class:"header-cell"},"数据名"),M("span",{class:"header-cell"},"值"),M("span",{style:{"text-align":"center"}},"操作")],-1)),(I(!0),O(J,null,ie(t.value,(Y,L)=>(I(),O("div",{key:L,class:"data-item"},[M("span",null,z(Y.name),1),M("div",null,z(Y.value),1),M("div",Vu,[w(v,{link:"",icon:D(hc),onClick:ce=>b(Y.name),title:"绑定变量"},null,8,["icon","onClick"]),w(S,{onCommand:ce=>x(ce,L)},{dropdown:P(()=>[w(E,null,{default:P(()=>[w(A,{command:"delete"},{default:P(()=>y[11]||(y[11]=[N("删除",-1)])),_:1,__:[11]})]),_:1})]),default:P(()=>[w(v,{link:"",icon:D(ja)},null,8,["icon"])]),_:2},1032,["onCommand"])])]))),128)),w(v,{type:"primary",class:"add-data-btn",onClick:y[1]||(y[1]=Y=>i.value=!0)},{default:P(()=>y[12]||(y[12]=[N(" 添加动态数据 ",-1)])),_:1,__:[12]})]))]),w(B,{modelValue:i.value,"onUpdate:modelValue":y[5]||(y[5]=Y=>i.value=Y),title:"添加动态数据",width:"600px"},{footer:P(()=>[w(v,{onClick:y[4]||(y[4]=Y=>i.value=!1)},{default:P(()=>y[15]||(y[15]=[N("取消",-1)])),_:1,__:[15]}),w(v,{type:"primary",onClick:m,disabled:!r.value},{default:P(()=>y[16]||(y[16]=[N(" 确定 ",-1)])),_:1,__:[16]},8,["disabled"])]),default:P(()=>[M("div",Fu,[w(R,{modelValue:a.value,"onUpdate:modelValue":y[2]||(y[2]=Y=>a.value=Y),placeholder:"搜索属性",class:"search-input","prefix-icon":"el-icon-search"},null,8,["modelValue"]),w(q,{data:p.value,class:"property-selection-table",border:""},{default:P(()=>[w(X,{width:"80"},{default:P(({row:Y})=>[w(V,{modelValue:r.value,"onUpdate:modelValue":y[3]||(y[3]=L=>r.value=L),value:Y.key},{default:P(()=>y[14]||(y[14]=[M("span",{style:{display:"none"}},null,-1)])),_:2,__:[14]},1032,["modelValue","value"])]),_:1}),w(X,{prop:"name",label:"名称",width:"120"}),w(X,{prop:"key",label:"key",width:"120"}),w(X,{prop:"type",label:"类型"})]),_:1},8,["data"])])]),_:1},8,["modelValue"]),w(B,{modelValue:n.value,"onUpdate:modelValue":y[8]||(y[8]=Y=>n.value=Y),title:"绑定变量",width:"500px"},{footer:P(()=>[w(v,{onClick:T},{default:P(()=>y[17]||(y[17]=[N("取消",-1)])),_:1,__:[17]}),w(v,{type:"primary",onClick:k},{default:P(()=>y[18]||(y[18]=[N("确定",-1)])),_:1,__:[18]})]),default:P(()=>[M("div",Lu,[M("div",Uu," 当前绑定: "+z(d(o.value)||"无"),1),w(R,{modelValue:l.value,"onUpdate:modelValue":y[6]||(y[6]=Y=>l.value=Y),placeholder:"搜索绑定变量",class:"search-variable-input","prefix-icon":"el-icon-search"},null,8,["modelValue"]),M("div",Bu,[g.value.length===0?(I(),O("div",Nu," 暂无数据 ")):Q("",!0),w($,{modelValue:c.value,"onUpdate:modelValue":y[7]||(y[7]=Y=>c.value=Y)},{default:P(()=>[(I(!0),O(J,null,ie(g.value,Y=>(I(),W(G,{key:Y,label:Y,class:"variable-option"},{default:P(()=>[N(z(Y),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}}),zu=Be(qu,[["__scopeId","data-v-7c601fd1"]]),Gu={class:"animation-manager"},Yu={class:"manager-header"},Xu={class:"manager-content"},ju={key:0,class:"node-animations"},Hu={class:"section-header"},Wu={class:"animation-list"},Qu={class:"animation-info"},Ku={key:0,class:"animation-params"},Ju={class:"global-animations"},Zu={class:"preset-animations"},ed={class:"preset-list"},td=Ze({__name:"AnimationPanel",setup(s){const e=ni(),t=U(!1),i=U(1),n=U(!0),r=U("ease-in-out"),a=[{id:"blink",name:"闪烁",description:"节点闪烁效果"},{id:"move",name:"移动",description:"节点移动效果"},{id:"scale",name:"缩放",description:"节点缩放效果"},{id:"rotate",name:"旋转",description:"节点旋转效果"},{id:"fade",name:"淡入淡出",description:"透明度变化"},{id:"shake",name:"抖动",description:"节点抖动效果"}],o=[{name:"报警闪烁",animations:[{type:"blink",duration:500,delay:0,loop:!0,blinkColor:"#ff0000"}]},{name:"运行指示",animations:[{type:"rotate",duration:2e3,delay:0,loop:!0,rotateAngle:360}]},{name:"呼吸效果",animations:[{type:"scale",duration:1500,delay:0,loop:!0,scaleRatio:1.2}]}],c=Xe(()=>e.selectedNodes.length===1?e.currentProject.nodes.find(y=>y.id===e.selectedNodes[0]):null),l=U([]);jt(c,y=>{y&&y.animations?l.value=[...y.animations]:l.value=[]},{immediate:!0});const h=()=>{t.value=!t.value,t.value?u():f()},u=()=>{e.currentProject.nodes.forEach(y=>{y.animations&&y.animations.length>0&&p(y)})},f=()=>{document.querySelectorAll(".canvas-node").forEach(y=>{y.style.animation=""})},p=y=>{const v=document.querySelector(`[data-node-id="${y.id}"]`);v&&y.animations.forEach(A=>{setTimeout(()=>{g(v,A)},A.delay||0)})},g=(y,v)=>{const A=(v.duration||1e3)/i.value,E=n.value?r.value:"linear",S=v.loop?"infinite":"1";let R="";switch(v.type){case"blink":R=`blink-${v.blinkColor||"#ff0000"} ${A}ms ${E} ${S}`;break;case"move":R=`move-${v.moveX||10}-${v.moveY||0} ${A}ms ${E} ${S}`;break;case"scale":R=`scale-${v.scaleRatio||1.2} ${A}ms ${E} ${S}`;break;case"rotate":R=`rotate-${v.rotateAngle||360} ${A}ms ${E} ${S}`;break;case"fade":R=`fade ${A}ms ${E} ${S}`;break;case"shake":R=`shake ${A}ms ${E} ${S}`;break}y.style.animation=R},d=()=>{l.value.push({type:"",duration:1e3,delay:0,loop:!1})},m=y=>{l.value.splice(y,1),x()},_=(y,v,A)=>{l.value[y][v]=A,x()},x=()=>{c.value&&e.updateNode(c.value.id,{animations:[...l.value]})},b=y=>{c.value&&(l.value=[...y.animations],x())},T=()=>{t.value&&(f(),u())},k=()=>{t.value&&(f(),u())},C=()=>{t.value&&(f(),u())};return(y,v)=>{const A=ii,E=Di,S=Ii,R=pn,V=Ri,X=mr,q=Xa,B=pr,G=Ei;return I(),O("div",Gu,[M("div",Yu,[v[3]||(v[3]=M("h3",null,"动画管理",-1)),w(A,{icon:D(mc),size:"small",type:"primary",onClick:h},{default:P(()=>[N(z(t.value?"停止":"播放")+"动画 ",1)]),_:1},8,["icon"])]),M("div",Xu,[c.value?(I(),O("div",ju,[M("div",Hu,[M("span",null,z(c.value.name)+" - 动画设置",1)]),M("div",Wu,[(I(!0),O(J,null,ie(l.value,($,Y)=>(I(),O("div",{key:Y,class:"animation-item"},[M("div",Qu,[w(S,{modelValue:$.type,"onUpdate:modelValue":L=>$.type=L,placeholder:"选择动画类型",onChange:L=>_(Y,"type",L)},{default:P(()=>[(I(),O(J,null,ie(a,L=>w(E,{key:L.id,label:L.name,value:L.id},null,8,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),w(A,{icon:D(pc),size:"small",type:"danger",text:"",onClick:L=>m(Y)},null,8,["icon","onClick"])]),$.type?(I(),O("div",Ku,[w(V,{label:"持续时间(ms)"},{default:P(()=>[w(R,{modelValue:$.duration,"onUpdate:modelValue":L=>$.duration=L,min:100,max:1e4,step:100,onChange:L=>_(Y,"duration",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024),w(V,{label:"延迟(ms)"},{default:P(()=>[w(R,{modelValue:$.delay,"onUpdate:modelValue":L=>$.delay=L,min:0,max:5e3,step:100,onChange:L=>_(Y,"delay",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024),w(V,{label:"循环"},{default:P(()=>[w(X,{modelValue:$.loop,"onUpdate:modelValue":L=>$.loop=L,onChange:L=>_(Y,"loop",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024),$.type==="blink"?(I(),W(V,{key:0,label:"闪烁颜色"},{default:P(()=>[w(q,{modelValue:$.blinkColor,"onUpdate:modelValue":L=>$.blinkColor=L,onChange:L=>_(Y,"blinkColor",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)):Q("",!0),$.type==="move"?(I(),O(J,{key:1},[w(V,{label:"移动距离X"},{default:P(()=>[w(R,{modelValue:$.moveX,"onUpdate:modelValue":L=>$.moveX=L,onChange:L=>_(Y,"moveX",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024),w(V,{label:"移动距离Y"},{default:P(()=>[w(R,{modelValue:$.moveY,"onUpdate:modelValue":L=>$.moveY=L,onChange:L=>_(Y,"moveY",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)],64)):Q("",!0),$.type==="scale"?(I(),W(V,{key:2,label:"缩放比例"},{default:P(()=>[w(B,{modelValue:$.scaleRatio,"onUpdate:modelValue":L=>$.scaleRatio=L,min:.5,max:2,step:.1,onChange:L=>_(Y,"scaleRatio",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)):Q("",!0),$.type==="rotate"?(I(),W(V,{key:3,label:"旋转角度"},{default:P(()=>[w(R,{modelValue:$.rotateAngle,"onUpdate:modelValue":L=>$.rotateAngle=L,min:0,max:360,onChange:L=>_(Y,"rotateAngle",L)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)):Q("",!0)])):Q("",!0)]))),128)),w(A,{icon:D(_c),onClick:d,block:"",dashed:""},{default:P(()=>v[4]||(v[4]=[N(" 添加动画 ",-1)])),_:1,__:[4]},8,["icon"])])])):Q("",!0),M("div",Ju,[v[5]||(v[5]=M("div",{class:"section-header"},[M("span",null,"全局动画设置")],-1)),w(G,{"label-width":"100px",size:"small"},{default:P(()=>[w(V,{label:"动画速度"},{default:P(()=>[w(B,{modelValue:i.value,"onUpdate:modelValue":v[0]||(v[0]=$=>i.value=$),min:.1,max:3,step:.1,onChange:T},null,8,["modelValue"])]),_:1}),w(V,{label:"启用缓动"},{default:P(()=>[w(X,{modelValue:n.value,"onUpdate:modelValue":v[1]||(v[1]=$=>n.value=$),onChange:k},null,8,["modelValue"])]),_:1}),w(V,{label:"缓动类型"},{default:P(()=>[w(S,{modelValue:r.value,"onUpdate:modelValue":v[2]||(v[2]=$=>r.value=$),disabled:!n.value,onChange:C},{default:P(()=>[w(E,{label:"线性",value:"linear"}),w(E,{label:"缓入",value:"ease-in"}),w(E,{label:"缓出",value:"ease-out"}),w(E,{label:"缓入缓出",value:"ease-in-out"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),M("div",Zu,[v[6]||(v[6]=M("div",{class:"section-header"},[M("span",null,"预设动画")],-1)),M("div",ed,[(I(),O(J,null,ie(o,$=>w(A,{key:$.name,size:"small",onClick:Y=>b($)},{default:P(()=>[N(z($.name),1)]),_:2},1032,["onClick"])),64))])])])])}}}),id=Be(td,[["__scopeId","data-v-c9772b26"]]),nd={class:"communication-manager"},sd={class:"manager-header"},rd={class:"manager-content"},ad={class:"connection-overview"},od={class:"overview-item"},ld={class:"overview-text"},cd={class:"count"},hd={class:"connection-list"},ud={class:"section-header"},dd={class:"connection-header"},fd={class:"connection-info"},md={class:"connection-details"},pd={class:"connection-name"},_d={class:"connection-url"},gd={class:"connection-actions"},yd={key:0,class:"empty-connections"},vd={key:0,class:"data-monitor"},xd={class:"section-header"},bd={key:0,class:"monitor-content"},wd={class:"message-time"},Ad={class:"message-source"},Cd={class:"message-data"},Td=Ze({__name:"CommunicationPanel",setup(s){let e={};vt(),ni();const t=U([]),i=U(!1),n=U(null),r=U(!1),a=U([]),o=y=>{y&&(l.value.config.mqttClientId=`client_${Date.now()}`)},c=y=>{switch(y){case"websocket":return{websocketUrl:"ws://localhost:8080/ws",websocketProtocols:""};case"mqtt":return{mqttUrl:"ws://localhost:1883",mqttCustomClientId:!0,mqttClientId:`client_${Date.now()}`,mqttUsername:"",mqttPassword:"",mqttTopics:"",mqttQos:0};case"http":return{httpUrl:"http://localhost:8080/api/data",httpMethod:"GET",httpTimeInterval:5e3,httpTimeout:5e3,httpHeaders:""};default:return{}}},l=U({name:"",type:"websocket",config:c("websocket")}),h={type:[{required:!0,message:"请选择通信类型",trigger:"change"}],"config.websocketUrl":[{required:!0,message:"请输入地址",trigger:"blur"},{validator:(y,v,A)=>{v&&!/^(ws|wss):\/\//.test(v)&&A(new Error("请检查协议标识符是否为ws或wss"));try{new URL(v),A()}catch{A(new Error("请输入合法的URL地址"))}},trigger:"blur"}],"config.mqttUrl":[{required:!0,message:"请输入地址",trigger:"blur"},{validator:(y,v,A)=>{v&&!/^(ws|wss):\/\//.test(v)&&A(new Error("请检查协议标识符是否为ws或wss"));try{new URL(v),A()}catch{A(new Error("请输入合法的URL地址"))}},trigger:"blur"}],"config.httpUrl":[{required:!0,message:"请输入地址",trigger:"blur"},{validator:(y,v,A)=>{v&&!/^(http|https):\/\//.test(v)&&A(new Error("请检查协议标识符是否为http或https"));try{new URL(v),A()}catch{A(new Error("请输入合法的URL地址"))}},trigger:"blur"}]},u=U();ti(()=>{C()}),fn(()=>{});const f=y=>{switch(y){case"websocket":return"Connection";case"mqtt":return"Message";case"http":return"Link";default:return"Connection"}},p=y=>new Date(y).toLocaleTimeString(),g=y=>{l.value.config=c(y)},d=()=>{l.value={name:"",type:"websocket",config:c("websocket")},n.value=null},m=async()=>{var y;try{if(await u.value.validate(),l.value.type==="mqtt"&&t.value.find(E=>{var S;return E.type==="mqtt"&&E.id!==((S=n.value)==null?void 0:S.id)})){await fi.alert("系统中已存在一个 MQTT 连接。只允许配置一个 MQTT 连接。","连接限制",{type:"warning",confirmButtonText:"确定"}),i.value=!1;return}if(l.value.type==="websocket"&&t.value.find(E=>{var S;return E.type==="websocket"&&E.id!==((S=n.value)==null?void 0:S.id)})){await fi.alert("系统中已存在一个 websocket 连接。只允许配置一个 websocket 连接。","连接限制",{type:"warning",confirmButtonText:"确定"}),i.value=!1;return}if(l.value.type==="http"&&t.value.filter(E=>{var S;return E.type==="http"&&E.id!==((S=n.value)==null?void 0:S.id)}).length>=50){await fi.alert("系统中已存在50个HTTP连接，已达到最大连接数限制。","连接限制",{type:"warning",confirmButtonText:"确定"}),i.value=!1;return}const v={id:((y=n.value)==null?void 0:y.id)||`conn_${Date.now()}`,...l.value,status:"disconnected",messageCount:0,lastConnected:null,expanded:!1};if(n.value){const A=t.value.findIndex(E=>E.id===n.value.id);t.value[A]=v}else t.value.push(v);k(),i.value=!1,j.success("连接配置保存成功"),T()}catch(v){console.error("表单验证失败:",v)}},_=y=>{const[v,A]=y.split("-"),E=parseInt(A),S=t.value[E];switch(v){case"edit":x(S);break;case"delete":b(E);break}},x=y=>{n.value=y,l.value={...y},y.type==="mqtt"&&l.value.config.mqttCustomClientId===void 0&&(l.value.config.mqttCustomClientId=!l.value.config.mqttClientId),i.value=!0},b=async y=>{try{await fi.confirm("确定要删除这个连接吗？","确认删除",{type:"warning"}),t.value.splice(y,1),k(),j.success("连接删除成功")}catch{}},T=()=>{C(),k(),j.success("已刷新所有连接")},k=()=>{const y=t.value.find(R=>R.type==="websocket"),v=t.value.find(R=>R.type==="mqtt"),A=t.value.filter(R=>R.type==="http");e||(e={}),e.mqttOptions||(e.mqttOptions={}),e.https||(e.https=[]),y?(e.websocket=y.config.websocketUrl,e.websocketProtocols=y.config.websocketProtocols):(e.websocket=void 0,e.websocketProtocols=void 0),v?(e.mqtt=v.config.mqttUrl,e.mqttTopics=v.config.mqttTopics,e.mqttOptions.username=v.config.mqttUsername,e.mqttOptions.password=v.config.mqttPassword,e.mqttOptions.clientId=v.config.mqttClientId,e.mqttOptions.customClientId=v.config.mqttCustomClientId):(e.mqtt=void 0,e.mqttTopics=void 0,e.mqttOptions=void 0),A.length>0?(e.https=[],A.forEach(R=>{const V={http:R.config.httpUrl,httpTimeInterval:R.config.httpTimeInterval,httpHeaders:R.config.httpHeaders,method:R.config.httpMethod,body:R.config.httpBody,times:R.config.httpTimeout};e.https.push(V)})):e.https=void 0;const S=vt().meta2dApp;S&&(S.websocket=e.websocket,S.websocketProtocols=e.websocketProtocols,S.mqtt=e.mqtt,S.mqttTopics=e.mqttTopics,S.mqttOptions=e.mqttOptions,S.https=e.https),tn.save(e)},C=()=>{var v,A,E,S;const y=localStorage.getItem("meta2d");e=y?JSON.parse(y):{},t.value=[],e&&(e.websocket&&typeof e.websocket=="string"&&t.value.push({name:"WebSocket连接",id:`ws_${Date.now()}`,type:"websocket",url:e.websocket,config:{websocketUrl:e.websocket,websocketProtocols:e.websocketProtocols||void 0}}),e.mqtt&&typeof e.mqtt=="string"&&t.value.push({name:"MQTT连接",id:`mqtt_${Date.now()}`,type:"mqtt",url:e.mqtt,config:{mqttUrl:e.mqtt,mqttClientId:((v=e.mqttOptions)==null?void 0:v.clientId)||"",mqttUsername:((A=e.mqttOptions)==null?void 0:A.username)||"",mqttPassword:((E=e.mqttOptions)==null?void 0:E.password)||"",mqttCustomClientId:((S=e.mqttOptions)==null?void 0:S.customClientId)||!1,mqttTopics:e.mqttTopics||"",mqttQos:0}}),e.https&&Array.isArray(e.https)&&e.https.forEach((R,V)=>{R.http&&t.value.push({name:`HTTP连接${V+1}`,id:`http_${Date.now()}_${V}`,type:"http",url:R.http,config:{httpUrl:R.http,httpMethod:R.method||"GET",httpHeaders:R.httpHeaders?JSON.stringify(R.httpHeaders):"",httpBody:R.body?String(R.body):"",httpTimeInterval:R.httpTimeInterval||0,httpTimeout:R.times||0}})}))};return(y,v)=>{const A=ii,E=_n,S=Tt,R=Yt,V=Gt,X=mr,q=Di,B=Ii,G=Ri,$=mn,Y=Ja,L=pn,ce=Ei,Ne=Ha;return I(),O("div",nd,[M("div",sd,[v[21]||(v[21]=M("h3",null,"通信配置",-1)),w(A,{icon:D(cs),size:"small",type:"primary",onClick:v[0]||(v[0]=F=>i.value=!0)},{default:P(()=>v[20]||(v[20]=[N(" 添加连接 ",-1)])),_:1,__:[20]},8,["icon"])]),M("div",rd,[M("div",ad,[M("div",od,[w(E,{class:"status-icon"},{default:P(()=>[w(D(cs))]),_:1}),M("div",ld,[M("div",cd,"已有连接数量："+z(t.value.length),1)])])]),M("div",hd,[M("div",ud,[v[23]||(v[23]=M("span",null,"连接列表",-1)),w(A,{icon:D(gc),size:"small",onClick:T},{default:P(()=>v[22]||(v[22]=[N(" 刷新 ",-1)])),_:1,__:[22]},8,["icon"])]),(I(!0),O(J,null,ie(t.value,(F,ge)=>(I(),O("div",{key:F.id,class:Ge(["connection-item",{connected:F.status==="connected"}])},[M("div",dd,[M("div",fd,[w(E,{class:"type-icon"},{default:P(()=>[(I(),W(Ka(f(F.type))))]),_:2},1024),M("div",md,[M("div",pd,z(F.name),1),M("div",_d,z(F.url),1)])]),M("div",gd,[w(V,{onCommand:_},{dropdown:P(()=>[w(R,null,{default:P(()=>[w(S,{command:`edit-${ge}`},{default:P(()=>v[24]||(v[24]=[N("编辑",-1)])),_:2,__:[24]},1032,["command"]),w(S,{command:`delete-${ge}`,divided:""},{default:P(()=>v[25]||(v[25]=[N("删除",-1)])),_:2,__:[25]},1032,["command"])]),_:2},1024)]),default:P(()=>[w(A,{icon:D(ja),size:"small"},null,8,["icon"])]),_:2},1024)])])],2))),128)),t.value.length===0?(I(),O("div",yd,[w(E,{size:48,color:"#c0c4cc"},{default:P(()=>[w(D(cs))]),_:1}),v[26]||(v[26]=M("p",null,"暂无通信连接",-1)),v[27]||(v[27]=M("p",null,'点击"添加连接"创建新的通信配置',-1))])):Q("",!0)]),t.value.some(F=>F.status==="connected")?(I(),O("div",vd,[M("div",xd,[v[28]||(v[28]=M("span",null,"实时数据",-1)),w(X,{modelValue:r.value,"onUpdate:modelValue":v[1]||(v[1]=F=>r.value=F),"active-text":"监控",onChange:y.toggleMonitor},null,8,["modelValue","onChange"])]),r.value?(I(),O("div",bd,[(I(!0),O(J,null,ie(a.value,F=>(I(),O("div",{key:F.id,class:"message-item"},[M("div",wd,z(p(F.timestamp)),1),M("div",Ad,z(F.source),1),M("div",Cd,z(F.data),1)]))),128))])):Q("",!0)])):Q("",!0)]),w(Ne,{modelValue:i.value,"onUpdate:modelValue":v[19]||(v[19]=F=>i.value=F),title:n.value?"编辑连接":"添加连接",width:"600px",onClose:d},{footer:P(()=>[w(A,{onClick:v[17]||(v[17]=F=>i.value=!1)},{default:P(()=>v[30]||(v[30]=[N("取消",-1)])),_:1,__:[30]}),w(A,{type:"primary",onClick:v[18]||(v[18]=F=>m())},{default:P(()=>v[31]||(v[31]=[N("保存",-1)])),_:1,__:[31]})]),default:P(()=>[w(ce,{model:l.value,rules:h,ref_key:"formRef",ref:u,"label-width":"100px"},{default:P(()=>[w(G,{label:"通信类型",prop:"type"},{default:P(()=>[w(B,{modelValue:l.value.type,"onUpdate:modelValue":v[2]||(v[2]=F=>l.value.type=F),onChange:g},{default:P(()=>[w(q,{label:"WebSocket",value:"websocket"}),w(q,{label:"MQTT",value:"mqtt"}),w(q,{label:"HTTP",value:"http"})]),_:1},8,["modelValue"])]),_:1}),l.value.type==="websocket"?(I(),O(J,{key:0},[w(G,{label:"服务器地址",prop:"config.websocketUrl"},{default:P(()=>[w($,{modelValue:l.value.config.websocketUrl,"onUpdate:modelValue":v[3]||(v[3]=F=>l.value.config.websocketUrl=F),placeholder:"ws://localhost:8080/ws"},null,8,["modelValue"])]),_:1}),w(G,{label:"协议"},{default:P(()=>[w($,{modelValue:l.value.config.websocketProtocols,"onUpdate:modelValue":v[4]||(v[4]=F=>l.value.config.websocketProtocols=F),placeholder:"可选，多个协议用逗号分隔"},null,8,["modelValue"])]),_:1})],64)):Q("",!0),l.value.type==="mqtt"?(I(),O(J,{key:1},[w(G,{label:"mqtt地址",prop:"config.mqttUrl"},{default:P(()=>[w($,{modelValue:l.value.config.mqttUrl,"onUpdate:modelValue":v[5]||(v[5]=F=>l.value.config.mqttUrl=F),placeholder:"ws://localhost:8083"},null,8,["modelValue"])]),_:1}),w(G,{label:"客户端ID"},{default:P(()=>[w($,{modelValue:l.value.config.mqttClientId,"onUpdate:modelValue":v[6]||(v[6]=F=>l.value.config.mqttClientId=F),disabled:l.value.config.mqttCustomClientId},null,8,["modelValue","disabled"])]),_:1}),w(G,{label:"ID自动生成",prop:"connectionForm.config.mqttCustomClientId"},{default:P(()=>[w(Y,{modelValue:l.value.config.mqttCustomClientId,"onUpdate:modelValue":v[7]||(v[7]=F=>l.value.config.mqttCustomClientId=F),onChange:o},{default:P(()=>v[29]||(v[29]=[N(" 使用系统生成的默认客户端ID ",-1)])),_:1,__:[29]},8,["modelValue"])]),_:1}),w(G,{label:"用户名"},{default:P(()=>[w($,{modelValue:l.value.config.mqttUsername,"onUpdate:modelValue":v[8]||(v[8]=F=>l.value.config.mqttUsername=F)},null,8,["modelValue"])]),_:1}),w(G,{label:"密码"},{default:P(()=>[w($,{modelValue:l.value.config.mqttPassword,"onUpdate:modelValue":v[9]||(v[9]=F=>l.value.config.mqttPassword=F),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),w(G,{label:"订阅主题"},{default:P(()=>[w($,{modelValue:l.value.config.mqttTopics,"onUpdate:modelValue":v[10]||(v[10]=F=>l.value.config.mqttTopics=F),placeholder:"多个主题用逗号分隔"},null,8,["modelValue"])]),_:1})],64)):Q("",!0),l.value.type==="http"?(I(),O(J,{key:2},[w(G,{label:"请求方式"},{default:P(()=>[w(B,{modelValue:l.value.config.httpMethod,"onUpdate:modelValue":v[11]||(v[11]=F=>l.value.config.httpMethod=F)},{default:P(()=>[w(q,{label:"GET",value:"GET"}),w(q,{label:"POST",value:"POST"})]),_:1},8,["modelValue"])]),_:1}),w(G,{label:"请求地址",prop:"config.httpUrl"},{default:P(()=>[w($,{modelValue:l.value.config.httpUrl,"onUpdate:modelValue":v[12]||(v[12]=F=>l.value.config.httpUrl=F),placeholder:"http://localhost:3001/api/data"},null,8,["modelValue"])]),_:1}),w(G,{label:"请求头"},{default:P(()=>[w($,{modelValue:l.value.config.httpHeaders,"onUpdate:modelValue":v[13]||(v[13]=F=>l.value.config.httpHeaders=F),type:"textarea",rows:3,placeholder:'{"Content-Type": "application/json"}'},null,8,["modelValue"])]),_:1}),l.value.config.httpMethod==="POST"?(I(),W(G,{key:0,label:"请求体"},{default:P(()=>[w($,{modelValue:l.value.config.httpBody,"onUpdate:modelValue":v[14]||(v[14]=F=>l.value.config.httpBody=F),type:"textarea",rows:"4",placeholder:"JSON/Form数据"},null,8,["modelValue"])]),_:1})):Q("",!0),w(G,{label:"请求间隔"},{default:P(()=>[w(L,{modelValue:l.value.config.httpTimeInterval,"onUpdate:modelValue":v[15]||(v[15]=F=>l.value.config.httpTimeInterval=F),min:1e3,max:3e5,step:1e3,placeholder:"毫秒"},null,8,["modelValue"])]),_:1}),w(G,{label:"超时时间"},{default:P(()=>[w(L,{modelValue:l.value.config.httpTimeout,"onUpdate:modelValue":v[16]||(v[16]=F=>l.value.config.httpTimeout=F),min:1e3,max:3e4,step:1e3,placeholder:"毫秒"},null,8,["modelValue"])]),_:1})],64)):Q("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),kd=Be(Td,[["__scopeId","data-v-2940ace9"]]);class Pd{constructor(){Ie(this,"messageQueue",[]);Ie(this,"vehicleStatus",new Map);Ie(this,"messageListeners",[]);Ie(this,"statusListeners",[]);this.vehicleStatus.set("AMR_001",{vehicleId:"AMR_001",currentTrack:null,status:"idle",progress:0}),console.log("🚗 MockMqttService 初始化完成")}sendMessage(e){const t={...e,timestamp:Date.now()};console.log("📤 发送模拟MQTT消息:",t),this.messageQueue.push(t),this.messageListeners.forEach(i=>{try{i(t)}catch(n){console.error("消息监听器错误:",n)}})}onMessage(e){return this.messageListeners.push(e),()=>{const t=this.messageListeners.indexOf(e);t>-1&&this.messageListeners.splice(t,1)}}updateVehicleStatus(e,t){const i=this.vehicleStatus.get(e);if(!i){console.warn(`车辆 ${e} 不存在`);return}const n={...i,...t};this.vehicleStatus.set(e,n),console.log(`🚗 车辆状态更新: ${e}`,n),this.statusListeners.forEach(r=>{try{r(n)}catch(a){console.error("状态监听器错误:",a)}})}onStatusChange(e){return this.statusListeners.push(e),()=>{const t=this.statusListeners.indexOf(e);t>-1&&this.statusListeners.splice(t,1)}}getVehicleStatus(e){return this.vehicleStatus.get(e)||null}getAllVehicleStatus(){return Array.from(this.vehicleStatus.values())}getTestCommands(){return[{name:"去轨道1",command:{vehicleId:"AMR_001",nextTrack:1,action:"move",speed:1}},{name:"去轨道2",command:{vehicleId:"AMR_001",nextTrack:2,action:"move",speed:1}},{name:"去轨道3",command:{vehicleId:"AMR_001",nextTrack:3,action:"move",speed:.8}},{name:"停止",command:{vehicleId:"AMR_001",nextTrack:0,action:"stop"}},{name:"等待",command:{vehicleId:"AMR_001",nextTrack:0,action:"wait"}}]}startAutoTest(){console.log("🔄 开始自动测试序列..."),[{delay:1e3,track:1},{delay:5e3,track:2},{delay:5e3,track:3},{delay:5e3,track:1}].forEach((t,i)=>{setTimeout(()=>{this.sendMessage({vehicleId:"AMR_001",nextTrack:t.track,action:"move",speed:1})},t.delay)})}clearMessages(){this.messageQueue=[],console.log("🗑️ 消息队列已清空")}getMessageHistory(){return[...this.messageQueue]}resetVehicle(e){this.vehicleStatus.set(e,{vehicleId:e,currentTrack:null,status:"idle",progress:0}),console.log(`🔄 车辆 ${e} 状态已重置`)}}const Ae=new Pd;class Md{constructor(){Ie(this,"trackRegistry",{})}scanAndBuildTrackMapping(){console.log("🔍 开始扫描画布轨道元素..."),this.trackRegistry={},meta2d.store.data.pens.forEach(t=>{var i;if((i=t.hcmsData)!=null&&i.trackId&&this.isTrackElement(t)){const n=this.createTrackElement(t);n&&(this.trackRegistry[n.trackId]=n,console.log(`📍 注册轨道: ${n.mqttId} (图元: ${t.id})`))}}),console.log(`✅ 轨道扫描完成，共注册 ${Object.keys(this.trackRegistry).length} 个轨道`),this.printTrackSummary()}isTrackElement(e){return!!(e.lineName||e.name==="circle"||e.name==="ellipse")}createTrackElement(e){var r,a;const t=(r=e.hcmsData)==null?void 0:r.trackId;if(!t)return null;const i=((a=e.hcmsData)==null?void 0:a.trackType)||"normal",n=this.extractCoordinates(e);return{elementId:e.id,trackId:t,trackType:i,mqttId:`track_${t}`,element:e,coordinates:n}}extractCoordinates(e){var a;const t=e.worldAnchors||((a=e.calculative)==null?void 0:a.worldAnchors)||e.anchors||[];if(t.length>=2)return{start:{x:t[0].x,y:t[0].y},end:{x:t[t.length-1].x,y:t[t.length-1].y}};const i=meta2d.getPenRect(e),n=i.x+i.width/2,r=i.y+i.height/2;return{start:{x:n,y:r},end:{x:n,y:r}}}generateMqttId(e,t){return`${{straight:"TRACK_",curve:"CURVE_",elevator:"ELEVATOR_",connector:"CONN_",waiting:"WAIT_",normal:"ELEM_"}[t]||"ELEM_"}${String(e).padStart(3,"0")}`}getTrackElement(e){return this.trackRegistry[e]||null}getTrackElementByMqttId(e){return Object.values(this.trackRegistry).find(t=>t.mqttId===e)||null}getAllTracks(){return Object.values(this.trackRegistry)}getTracksByType(e){return Object.values(this.trackRegistry).filter(t=>t.trackType===e)}getTrackConnections(){const e={};return this.getAllTracks().forEach(i=>{e[i.trackId]=this.findConnectedTracks(i)}),e}findConnectedTracks(e){const t=[];return Object.values(this.trackRegistry).forEach(n=>{n.trackId!==e.trackId&&this.areTracksConnected(e,n,50)&&t.push(n.trackId)}),t}areTracksConnected(e,t,i){const{start:n,end:r}=e.coordinates,{start:a,end:o}=t.coordinates;return[{p1:r,p2:a},{p1:r,p2:o},{p1:n,p2:a},{p1:n,p2:o}].some(l=>Math.sqrt(Math.pow(l.p1.x-l.p2.x,2)+Math.pow(l.p1.y-l.p2.y,2))<i)}printTrackSummary(){const e=this.getAllTracks(),t=e.reduce((i,n)=>(i[n.trackType]=(i[n.trackType]||0)+1,i),{});console.log("📊 轨道统计:"),Object.entries(t).forEach(([i,n])=>{console.log(`  ${i}: ${n} 个`)}),console.log("🔗 轨道列表:"),e.forEach(i=>{console.log(`  ${i.mqttId} (ID: ${i.trackId}, 类型: ${i.trackType})`)})}validateTrackConfiguration(){const e=[],i=this.getAllTracks().map(o=>o.trackId),n=i.filter((o,c)=>i.indexOf(o)!==c);n.length>0&&e.push(`轨道ID重复: ${n.join(", ")}`);const r=this.getTrackConnections(),a=Object.entries(r).filter(([o,c])=>c.length===0).map(([o,c])=>o);return a.length>0&&e.push(`孤立轨道: ${a.join(", ")}`),{valid:e.length===0,issues:e}}}const Gn=new Md,Sd={class:"mqtt-test-panel"},Ed={class:"test-commands"},Rd={class:"command-buttons"},Id={class:"custom-command"},Dd={class:"message-history"},Od={class:"messages"},$d={class:"timestamp"},Vd={class:"content"},Fd={class:"vehicle-status"},Ld={key:0},Ud={class:"progress"},Bd={class:"debug-info"},Nd={class:"debug-output"},qd=Ze({__name:"MqttTestPanel",setup(s){const e=U([]),t=U([]),i=U(Ae.getTestCommands()),n=U([]),r=U({vehicleId:"AMR_001",nextTrack:1,action:"move",speed:1});let a=null,o=null;const c=m=>{Ae.sendMessage(m)},l=()=>{Ae.sendMessage({vehicleId:r.value.vehicleId,nextTrack:r.value.nextTrack,action:r.value.action,speed:r.value.speed})},h=()=>{e.value=[],Ae.clearMessages()},u=m=>new Date(m).toLocaleTimeString(),f=m=>`${m.vehicleId} ${m.action} -> 轨道${m.nextTrack} (速度:${m.speed||1})`,p=m=>{n.value.push(`[${new Date().toLocaleTimeString()}] ${m}`),n.value.length>10&&n.value.shift()},g=()=>{Gn.scanAndBuildTrackMapping();const m=Gn.getAllTracks();p(`扫描到 ${m.length} 个轨道: ${m.map(_=>_.trackId).join(", ")}`)},d=()=>{var x,b,T;const _=(((T=(b=(x=window.meta2d)==null?void 0:x.store)==null?void 0:b.data)==null?void 0:T.pens)||[]).filter(k=>{var A,E,S;if((A=k.hcmsData)!=null&&A.vehicleId)return!0;if(k.name==="circle"&&((E=k.hcmsData)!=null&&E.trackId))return!1;const C=((S=k.name)==null?void 0:S.toLowerCase())||"",v=["vehicle","car","amr","robot"].some(R=>C.includes(R));return C==="square"||v});p(`扫描到 ${_.length} 个车辆图元: ${_.map(k=>k.name||k.id).join(", ")}`)};return ti(()=>{a=Ae.onMessage(m=>{e.value.push(m),e.value.length>20&&e.value.shift()}),o=Ae.onStatusChange(m=>{const _=t.value.findIndex(x=>x.vehicleId===m.vehicleId);_>=0?t.value[_]=m:t.value.push(m)}),t.value=Ae.getAllVehicleStatus(),console.log("🎛️ MQTT测试面板已初始化")}),fn(()=>{a&&a(),o&&o()}),(m,_)=>{const x=ii,b=mn,T=Ri,k=pn,C=Di,y=Ii,v=Ei;return I(),O("div",Sd,[_[13]||(_[13]=M("h3",null,"MQTT模拟器测试",-1)),M("div",Ed,[_[4]||(_[4]=M("h4",null,"预设指令",-1)),M("div",Rd,[(I(!0),O(J,null,ie(i.value,A=>(I(),W(x,{key:A.name,onClick:E=>c(A.command),type:"primary",size:"small"},{default:P(()=>[N(z(A.name),1)]),_:2},1032,["onClick"]))),128))])]),M("div",Id,[_[6]||(_[6]=M("h4",null,"自定义指令",-1)),w(v,{model:r.value,"label-width":"80px",size:"small"},{default:P(()=>[w(T,{label:"车辆ID"},{default:P(()=>[w(b,{modelValue:r.value.vehicleId,"onUpdate:modelValue":_[0]||(_[0]=A=>r.value.vehicleId=A),placeholder:"AMR_001"},null,8,["modelValue"])]),_:1}),w(T,{label:"目标轨道"},{default:P(()=>[w(k,{modelValue:r.value.nextTrack,"onUpdate:modelValue":_[1]||(_[1]=A=>r.value.nextTrack=A),min:1,max:10},null,8,["modelValue"])]),_:1}),w(T,{label:"动作"},{default:P(()=>[w(y,{modelValue:r.value.action,"onUpdate:modelValue":_[2]||(_[2]=A=>r.value.action=A)},{default:P(()=>[w(C,{label:"移动",value:"move"}),w(C,{label:"停止",value:"stop"}),w(C,{label:"等待",value:"wait"})]),_:1},8,["modelValue"])]),_:1}),w(T,{label:"速度"},{default:P(()=>[w(k,{modelValue:r.value.speed,"onUpdate:modelValue":_[3]||(_[3]=A=>r.value.speed=A),min:.1,max:2,step:.1},null,8,["modelValue"])]),_:1}),w(T,null,{default:P(()=>[w(x,{onClick:l,type:"primary"},{default:P(()=>_[5]||(_[5]=[N("发送指令",-1)])),_:1,__:[5]})]),_:1})]),_:1},8,["model"])]),M("div",Dd,[_[8]||(_[8]=M("h4",null,"消息历史",-1)),M("div",Od,[(I(!0),O(J,null,ie(e.value,(A,E)=>(I(),O("div",{key:E,class:"message-item"},[M("span",$d,z(u(A.timestamp)),1),M("span",Vd,z(f(A)),1)]))),128))]),w(x,{onClick:h,size:"small",type:"danger"},{default:P(()=>_[7]||(_[7]=[N("清空历史",-1)])),_:1,__:[7]})]),M("div",Fd,[_[9]||(_[9]=M("h4",null,"车辆状态",-1)),(I(!0),O(J,null,ie(t.value,A=>(I(),O("div",{key:A.vehicleId,class:"status-item"},[M("strong",null,z(A.vehicleId),1),N(": "+z(A.status)+" ",1),A.currentTrack?(I(),O("span",Ld,"(轨道"+z(A.currentTrack)+")",1)):Q("",!0),M("span",Ud,"["+z(Math.round(A.progress*100))+"%]",1)]))),128))]),M("div",Bd,[_[12]||(_[12]=M("h4",null,"调试信息",-1)),w(x,{onClick:g,size:"small"},{default:P(()=>_[10]||(_[10]=[N("扫描轨道",-1)])),_:1,__:[10]}),w(x,{onClick:d,size:"small"},{default:P(()=>_[11]||(_[11]=[N("扫描车辆",-1)])),_:1,__:[11]}),M("div",Nd,[(I(!0),O(J,null,ie(n.value,(A,E)=>(I(),O("div",{key:E,class:"debug-log"},z(A),1))),128))])])])}}}),zd=Be(qd,[["__scopeId","data-v-a95a8a05"]]),Gd={key:0,class:"track-info"},Yd=Ze({__name:"SinglePenProperties",props:{pen:{}},setup(s){const e=s,t=U(["position","style","track"]),i=gi({x:0,y:0,width:0,height:0}),n=U(null),r=U("normal"),a=Xe(()=>e.pen?!!(e.pen.lineName||e.pen.name==="circle"||e.pen.name==="ellipse"):!1),o=d=>{const m=meta2d.getPenRect(d);m&&(i.x=Math.floor(m.x),i.y=Math.floor(m.y),i.width=Math.floor(m.width),i.height=Math.floor(m.height))},c=d=>{var m,_;n.value=((m=d.hcmsData)==null?void 0:m.trackId)||null,r.value=((_=d.hcmsData)==null?void 0:_.trackType)||"normal",console.log(`初始化轨道配置: 图元${d.id}, ID=${n.value}, 类型=${r.value}`)};jt(()=>e.pen,d=>{d&&(o(d),c(d),d.globalAlpha===void 0&&(d.globalAlpha=1),d.rotate===void 0&&(d.rotate=0))},{immediate:!0,deep:!0});const l=()=>{const d={id:e.pen.id,x:i.x,y:i.y,width:i.width,height:i.height,globalAlpha:e.pen.globalAlpha,rotate:e.pen.rotate};meta2d.update(d),meta2d.render()},h=d=>{console.log(`轨道序号变更: ${d}`),e.pen.data||(e.pen.data={}),e.pen.hcmsData||(e.pen.hcmsData={}),e.pen.hcmsData.trackId=d,meta2d.setValue({id:e.pen.id,hcmsData:{...e.pen.hcmsData,trackId:d}}),meta2d.render(),tn.save(meta2d.data()),console.log(`已保存轨道序号: ${d} 到图元 ${e.pen.id}`),console.log("更新后的图元数据:",e.pen.data)},u=d=>{console.log(`轨道类型变更: ${d}`),e.pen.data||(e.pen.data={}),e.pen.hcmsData||(e.pen.hcmsData={}),e.pen.hcmsData.trackType=d,meta2d.setValue({id:e.pen.id,hcmsData:{...e.pen.hcmsData,trackType:d}}),meta2d.render(),tn.save(meta2d.data()),console.log(`已保存轨道类型: ${d} 到图元 ${e.pen.id}`),console.log("更新后的图元数据:",e.pen.data)},f=()=>n.value?`${{straight:"直线轨道",curve:"弯道轨道",elevator:"提升机",connector:"连接线",waiting:"等候点",normal:"图元"}[r.value]||"图元"} ${n.value}`:"未设置",p=()=>({straight:"直线轨道",curve:"弯道轨道",elevator:"提升机",connector:"连接线",waiting:"等候点",normal:"普通图元"})[r.value]||"普通图元",g=()=>n.value?`${{straight:"TRACK_",curve:"CURVE_",elevator:"ELEVATOR_",connector:"CONN_",waiting:"WAIT_",normal:"ELEM_"}[r.value]||"ELEM_"}${String(n.value).padStart(3,"0")}`:"无";return(d,m)=>{const _=pn,x=Ri,b=vc,T=yc,k=Ga,C=pr,y=Di,v=Ii,A=Za,E=za,S=Ei;return I(),W(S,{"label-position":"top",class:"property-form"},{default:P(()=>[w(E,{modelValue:t.value,"onUpdate:modelValue":m[8]||(m[8]=R=>t.value=R)},{default:P(()=>[w(k,{title:"位置和尺寸",name:"position"},{default:P(()=>[w(T,{gutter:16},{default:P(()=>[w(b,{span:12},{default:P(()=>[w(x,{label:"X"},{default:P(()=>[w(_,{modelValue:i.x,"onUpdate:modelValue":m[0]||(m[0]=R=>i.x=R),onChange:l},null,8,["modelValue"])]),_:1})]),_:1}),w(b,{span:12},{default:P(()=>[w(x,{label:"Y"},{default:P(()=>[w(_,{modelValue:i.y,"onUpdate:modelValue":m[1]||(m[1]=R=>i.y=R),onChange:l},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),w(T,{gutter:16},{default:P(()=>[w(b,{span:12},{default:P(()=>[w(x,{label:"宽度"},{default:P(()=>[w(_,{modelValue:i.width,"onUpdate:modelValue":m[2]||(m[2]=R=>i.width=R),min:1,onChange:l},null,8,["modelValue"])]),_:1})]),_:1}),w(b,{span:12},{default:P(()=>[w(x,{label:"高度"},{default:P(()=>[w(_,{modelValue:i.height,"onUpdate:modelValue":m[3]||(m[3]=R=>i.height=R),min:1,onChange:l},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1}),w(k,{title:"样式",name:"style"},{default:P(()=>[w(x,{label:"不透明度"},{default:P(()=>[w(C,{modelValue:d.pen.globalAlpha,"onUpdate:modelValue":m[4]||(m[4]=R=>d.pen.globalAlpha=R),min:0,max:1,step:.1,onChange:l},null,8,["modelValue"])]),_:1}),w(x,{label:"旋转"},{default:P(()=>[w(C,{modelValue:d.pen.rotate,"onUpdate:modelValue":m[5]||(m[5]=R=>d.pen.rotate=R),min:0,max:360,onChange:l},null,8,["modelValue"])]),_:1})]),_:1}),a.value?(I(),W(k,{key:0,title:"轨道配置属性",name:"track"},{default:P(()=>[w(x,{label:"轨道序号"},{default:P(()=>[w(_,{modelValue:n.value,"onUpdate:modelValue":m[6]||(m[6]=R=>n.value=R),min:0,max:999,placeholder:"输入轨道序号 (如: 1, 2, 3)",onChange:h,style:{width:"100%"}},null,8,["modelValue"]),m[9]||(m[9]=M("div",{class:"track-hint"},[M("p",null,"为轨道元素设置序号，用于MQTT控制"),M("p",null,[M("strong",null,"轨道:"),N(" 1, 2, 3... | "),M("strong",null,"提升机:"),N(" 101, 102... | "),M("strong",null,"连接线:"),N(" 201, 202...")])],-1))]),_:1,__:[9]}),w(x,{label:"轨道类型"},{default:P(()=>[w(v,{modelValue:r.value,"onUpdate:modelValue":m[7]||(m[7]=R=>r.value=R),onChange:u,style:{width:"100%"}},{default:P(()=>[w(y,{label:"直线轨道",value:"straight"}),w(y,{label:"弯道轨道",value:"curve"}),w(y,{label:"提升机",value:"elevator"}),w(y,{label:"连接线",value:"connector"}),w(y,{label:"等候点",value:"waiting"}),w(y,{label:"普通图元",value:"normal"})]),_:1},8,["modelValue"]),m[10]||(m[10]=M("div",{class:"track-type-hint"},[M("p",null,"选择图元的轨道类型，影响MQTT控制逻辑")],-1))]),_:1,__:[10]}),n.value!==null&&n.value>0?(I(),O("div",Gd,[w(A,{title:f(),type:"success",closable:!1,"show-icon":""},{default:P(()=>[M("p",null,"类型: "+z(p()),1),M("p",null,"MQTT标识: "+z(g()),1)]),_:1},8,["title"])])):Q("",!0)]),_:1})):Q("",!0)]),_:1},8,["modelValue"])]),_:1})}}}),Xd=Be(Yd,[["__scopeId","data-v-6140a617"]]);function ft(s){if(s===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s}function qo(s,e){s.prototype=Object.create(e.prototype),s.prototype.constructor=s,s.__proto__=e}/*!
 * GSAP 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Fe={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},Ci={duration:.5,overwrite:!1,delay:0},vr,ye,ne,He=1e8,te=1/He,js=Math.PI*2,jd=js/4,Hd=0,zo=Math.sqrt,Wd=Math.cos,Qd=Math.sin,_e=function(e){return typeof e=="string"},oe=function(e){return typeof e=="function"},bt=function(e){return typeof e=="number"},xr=function(e){return typeof e>"u"},ct=function(e){return typeof e=="object"},ke=function(e){return e!==!1},br=function(){return typeof window<"u"},Mn=function(e){return oe(e)||_e(e)},Go=typeof ArrayBuffer=="function"&&ArrayBuffer.isView||function(){},be=Array.isArray,Hs=/(?:-?\.?\d|\.)+/gi,Yo=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,mi=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,As=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,Xo=/[+-]=-?[.\d]+/,jo=/[^,'"\[\]\s]+/gi,Kd=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,re,st,Ws,wr,Le={},Yn={},Ho,Wo=function(e){return(Yn=Ti(e,Le))&&Ee},Ar=function(e,t){return console.warn("Invalid property",e,"set to",t,"Missing plugin? gsap.registerPlugin()")},rn=function(e,t){return!t&&console.warn(e)},Qo=function(e,t){return e&&(Le[e]=t)&&Yn&&(Yn[e]=t)||Le},an=function(){return 0},Jd={suppressEvents:!0,isStart:!0,kill:!1},$n={suppressEvents:!0,kill:!1},Zd={suppressEvents:!0},Cr={},Et=[],Qs={},Ko,Oe={},Cs={},ua=30,Vn=[],Tr="",kr=function(e){var t=e[0],i,n;if(ct(t)||oe(t)||(e=[e]),!(i=(t._gsap||{}).harness)){for(n=Vn.length;n--&&!Vn[n].targetTest(t););i=Vn[n]}for(n=e.length;n--;)e[n]&&(e[n]._gsap||(e[n]._gsap=new bl(e[n],i)))||e.splice(n,1);return e},Ht=function(e){return e._gsap||kr(We(e))[0]._gsap},Jo=function(e,t,i){return(i=e[t])&&oe(i)?e[t]():xr(i)&&e.getAttribute&&e.getAttribute(t)||i},Pe=function(e,t){return(e=e.split(",")).forEach(t)||e},le=function(e){return Math.round(e*1e5)/1e5||0},ue=function(e){return Math.round(e*1e7)/1e7||0},yi=function(e,t){var i=t.charAt(0),n=parseFloat(t.substr(2));return e=parseFloat(e),i==="+"?e+n:i==="-"?e-n:i==="*"?e*n:e/n},ef=function(e,t){for(var i=t.length,n=0;e.indexOf(t[n])<0&&++n<i;);return n<i},Xn=function(){var e=Et.length,t=Et.slice(0),i,n;for(Qs={},Et.length=0,i=0;i<e;i++)n=t[i],n&&n._lazy&&(n.render(n._lazy[0],n._lazy[1],!0)._lazy=0)},Pr=function(e){return!!(e._initted||e._startAt||e.add)},Zo=function(e,t,i,n){Et.length&&!ye&&Xn(),e.render(t,i,!!(ye&&t<0&&Pr(e))),Et.length&&!ye&&Xn()},el=function(e){var t=parseFloat(e);return(t||t===0)&&(e+"").match(jo).length<2?t:_e(e)?e.trim():e},tl=function(e){return e},Ue=function(e,t){for(var i in t)i in e||(e[i]=t[i]);return e},tf=function(e){return function(t,i){for(var n in i)n in t||n==="duration"&&e||n==="ease"||(t[n]=i[n])}},Ti=function(e,t){for(var i in t)e[i]=t[i];return e},da=function s(e,t){for(var i in t)i!=="__proto__"&&i!=="constructor"&&i!=="prototype"&&(e[i]=ct(t[i])?s(e[i]||(e[i]={}),t[i]):t[i]);return e},jn=function(e,t){var i={},n;for(n in e)n in t||(i[n]=e[n]);return i},Qi=function(e){var t=e.parent||re,i=e.keyframes?tf(be(e.keyframes)):Ue;if(ke(e.inherit))for(;t;)i(e,t.vars.defaults),t=t.parent||t._dp;return e},nf=function(e,t){for(var i=e.length,n=i===t.length;n&&i--&&e[i]===t[i];);return i<0},il=function(e,t,i,n,r){var a=e[n],o;if(r)for(o=t[r];a&&a[r]>o;)a=a._prev;return a?(t._next=a._next,a._next=t):(t._next=e[i],e[i]=t),t._next?t._next._prev=t:e[n]=t,t._prev=a,t.parent=t._dp=e,t},ss=function(e,t,i,n){i===void 0&&(i="_first"),n===void 0&&(n="_last");var r=t._prev,a=t._next;r?r._next=a:e[i]===t&&(e[i]=a),a?a._prev=r:e[n]===t&&(e[n]=r),t._next=t._prev=t.parent=null},Dt=function(e,t){e.parent&&(!t||e.parent.autoRemoveChildren)&&e.parent.remove&&e.parent.remove(e),e._act=0},Wt=function(e,t){if(e&&(!t||t._end>e._dur||t._start<0))for(var i=e;i;)i._dirty=1,i=i.parent;return e},sf=function(e){for(var t=e.parent;t&&t.parent;)t._dirty=1,t.totalDuration(),t=t.parent;return e},Ks=function(e,t,i,n){return e._startAt&&(ye?e._startAt.revert($n):e.vars.immediateRender&&!e.vars.autoRevert||e._startAt.render(t,!0,n))},rf=function s(e){return!e||e._ts&&s(e.parent)},fa=function(e){return e._repeat?ki(e._tTime,e=e.duration()+e._rDelay)*e:0},ki=function(e,t){var i=Math.floor(e=ue(e/t));return e&&i===e?i-1:i},Hn=function(e,t){return(e-t._start)*t._ts+(t._ts>=0?0:t._dirty?t.totalDuration():t._tDur)},rs=function(e){return e._end=ue(e._start+(e._tDur/Math.abs(e._ts||e._rts||te)||0))},as=function(e,t){var i=e._dp;return i&&i.smoothChildTiming&&e._ts&&(e._start=ue(i._time-(e._ts>0?t/e._ts:((e._dirty?e.totalDuration():e._tDur)-t)/-e._ts)),rs(e),i._dirty||Wt(i,e)),e},nl=function(e,t){var i;if((t._time||!t._dur&&t._initted||t._start<e._time&&(t._dur||!t.add))&&(i=Hn(e.rawTime(),t),(!t._dur||yn(0,t.totalDuration(),i)-t._tTime>te)&&t.render(i,!0)),Wt(e,t)._dp&&e._initted&&e._time>=e._dur&&e._ts){if(e._dur<e.duration())for(i=e;i._dp;)i.rawTime()>=0&&i.totalTime(i._tTime),i=i._dp;e._zTime=-te}},rt=function(e,t,i,n){return t.parent&&Dt(t),t._start=ue((bt(i)?i:i||e!==re?ze(e,i,t):e._time)+t._delay),t._end=ue(t._start+(t.totalDuration()/Math.abs(t.timeScale())||0)),il(e,t,"_first","_last",e._sort?"_start":0),Js(t)||(e._recent=t),n||nl(e,t),e._ts<0&&as(e,e._tTime),e},sl=function(e,t){return(Le.ScrollTrigger||Ar("scrollTrigger",t))&&Le.ScrollTrigger.create(t,e)},rl=function(e,t,i,n,r){if(Sr(e,t,r),!e._initted)return 1;if(!i&&e._pt&&!ye&&(e._dur&&e.vars.lazy!==!1||!e._dur&&e.vars.lazy)&&Ko!==$e.frame)return Et.push(e),e._lazy=[r,n],1},af=function s(e){var t=e.parent;return t&&t._ts&&t._initted&&!t._lock&&(t.rawTime()<0||s(t))},Js=function(e){var t=e.data;return t==="isFromStart"||t==="isStart"},of=function(e,t,i,n){var r=e.ratio,a=t<0||!t&&(!e._start&&af(e)&&!(!e._initted&&Js(e))||(e._ts<0||e._dp._ts<0)&&!Js(e))?0:1,o=e._rDelay,c=0,l,h,u;if(o&&e._repeat&&(c=yn(0,e._tDur,t),h=ki(c,o),e._yoyo&&h&1&&(a=1-a),h!==ki(e._tTime,o)&&(r=1-a,e.vars.repeatRefresh&&e._initted&&e.invalidate())),a!==r||ye||n||e._zTime===te||!t&&e._zTime){if(!e._initted&&rl(e,t,n,i,c))return;for(u=e._zTime,e._zTime=t||(i?te:0),i||(i=t&&!u),e.ratio=a,e._from&&(a=1-a),e._time=0,e._tTime=c,l=e._pt;l;)l.r(a,l.d),l=l._next;t<0&&Ks(e,t,i,!0),e._onUpdate&&!i&&Ve(e,"onUpdate"),c&&e._repeat&&!i&&e.parent&&Ve(e,"onRepeat"),(t>=e._tDur||t<0)&&e.ratio===a&&(a&&Dt(e,1),!i&&!ye&&(Ve(e,a?"onComplete":"onReverseComplete",!0),e._prom&&e._prom()))}else e._zTime||(e._zTime=t)},lf=function(e,t,i){var n;if(i>t)for(n=e._first;n&&n._start<=i;){if(n.data==="isPause"&&n._start>t)return n;n=n._next}else for(n=e._last;n&&n._start>=i;){if(n.data==="isPause"&&n._start<t)return n;n=n._prev}},Pi=function(e,t,i,n){var r=e._repeat,a=ue(t)||0,o=e._tTime/e._tDur;return o&&!n&&(e._time*=a/e._dur),e._dur=a,e._tDur=r?r<0?1e10:ue(a*(r+1)+e._rDelay*r):a,o>0&&!n&&as(e,e._tTime=e._tDur*o),e.parent&&rs(e),i||Wt(e.parent,e),e},ma=function(e){return e instanceof Ce?Wt(e):Pi(e,e._dur)},cf={_start:0,endTime:an,totalDuration:an},ze=function s(e,t,i){var n=e.labels,r=e._recent||cf,a=e.duration()>=He?r.endTime(!1):e._dur,o,c,l;return _e(t)&&(isNaN(t)||t in n)?(c=t.charAt(0),l=t.substr(-1)==="%",o=t.indexOf("="),c==="<"||c===">"?(o>=0&&(t=t.replace(/=/,"")),(c==="<"?r._start:r.endTime(r._repeat>=0))+(parseFloat(t.substr(1))||0)*(l?(o<0?r:i).totalDuration()/100:1)):o<0?(t in n||(n[t]=a),n[t]):(c=parseFloat(t.charAt(o-1)+t.substr(o+1)),l&&i&&(c=c/100*(be(i)?i[0]:i).totalDuration()),o>1?s(e,t.substr(0,o-1),i)+c:a+c)):t==null?a:+t},Ki=function(e,t,i){var n=bt(t[1]),r=(n?2:1)+(e<2?0:1),a=t[r],o,c;if(n&&(a.duration=t[1]),a.parent=i,e){for(o=a,c=i;c&&!("immediateRender"in o);)o=c.vars.defaults||{},c=ke(c.vars.inherit)&&c.parent;a.immediateRender=ke(o.immediateRender),e<2?a.runBackwards=1:a.startAt=t[r-1]}return new he(t[0],a,t[r+1])},$t=function(e,t){return e||e===0?t(e):t},yn=function(e,t,i){return i<e?e:i>t?t:i},xe=function(e,t){return!_e(e)||!(t=Kd.exec(e))?"":t[1]},hf=function(e,t,i){return $t(i,function(n){return yn(e,t,n)})},Zs=[].slice,al=function(e,t){return e&&ct(e)&&"length"in e&&(!t&&!e.length||e.length-1 in e&&ct(e[0]))&&!e.nodeType&&e!==st},uf=function(e,t,i){return i===void 0&&(i=[]),e.forEach(function(n){var r;return _e(n)&&!t||al(n,1)?(r=i).push.apply(r,We(n)):i.push(n)})||i},We=function(e,t,i){return ne&&!t&&ne.selector?ne.selector(e):_e(e)&&!i&&(Ws||!Mi())?Zs.call((t||wr).querySelectorAll(e),0):be(e)?uf(e,i):al(e)?Zs.call(e,0):e?[e]:[]},er=function(e){return e=We(e)[0]||rn("Invalid scope")||{},function(t){var i=e.current||e.nativeElement||e;return We(t,i.querySelectorAll?i:i===e?rn("Invalid scope")||wr.createElement("div"):e)}},ol=function(e){return e.sort(function(){return .5-Math.random()})},ll=function(e){if(oe(e))return e;var t=ct(e)?e:{each:e},i=Qt(t.ease),n=t.from||0,r=parseFloat(t.base)||0,a={},o=n>0&&n<1,c=isNaN(n)||o,l=t.axis,h=n,u=n;return _e(n)?h=u={center:.5,edges:.5,end:1}[n]||0:!o&&c&&(h=n[0],u=n[1]),function(f,p,g){var d=(g||t).length,m=a[d],_,x,b,T,k,C,y,v,A;if(!m){if(A=t.grid==="auto"?0:(t.grid||[1,He])[1],!A){for(y=-He;y<(y=g[A++].getBoundingClientRect().left)&&A<d;);A<d&&A--}for(m=a[d]=[],_=c?Math.min(A,d)*h-.5:n%A,x=A===He?0:c?d*u/A-.5:n/A|0,y=0,v=He,C=0;C<d;C++)b=C%A-_,T=x-(C/A|0),m[C]=k=l?Math.abs(l==="y"?T:b):zo(b*b+T*T),k>y&&(y=k),k<v&&(v=k);n==="random"&&ol(m),m.max=y-v,m.min=v,m.v=d=(parseFloat(t.amount)||parseFloat(t.each)*(A>d?d-1:l?l==="y"?d/A:A:Math.max(A,d/A))||0)*(n==="edges"?-1:1),m.b=d<0?r-d:r,m.u=xe(t.amount||t.each)||0,i=i&&d<0?yl(i):i}return d=(m[f]-m.min)/m.max||0,ue(m.b+(i?i(d):d)*m.v)+m.u}},tr=function(e){var t=Math.pow(10,((e+"").split(".")[1]||"").length);return function(i){var n=ue(Math.round(parseFloat(i)/e)*e*t);return(n-n%1)/t+(bt(i)?0:xe(i))}},cl=function(e,t){var i=be(e),n,r;return!i&&ct(e)&&(n=i=e.radius||He,e.values?(e=We(e.values),(r=!bt(e[0]))&&(n*=n)):e=tr(e.increment)),$t(t,i?oe(e)?function(a){return r=e(a),Math.abs(r-a)<=n?r:a}:function(a){for(var o=parseFloat(r?a.x:a),c=parseFloat(r?a.y:0),l=He,h=0,u=e.length,f,p;u--;)r?(f=e[u].x-o,p=e[u].y-c,f=f*f+p*p):f=Math.abs(e[u]-o),f<l&&(l=f,h=u);return h=!n||l<=n?e[h]:a,r||h===a||bt(a)?h:h+xe(a)}:tr(e))},hl=function(e,t,i,n){return $t(be(e)?!t:i===!0?!!(i=0):!n,function(){return be(e)?e[~~(Math.random()*e.length)]:(i=i||1e-5)&&(n=i<1?Math.pow(10,(i+"").length-2):1)&&Math.floor(Math.round((e-i/2+Math.random()*(t-e+i*.99))/i)*i*n)/n})},df=function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return function(n){return t.reduce(function(r,a){return a(r)},n)}},ff=function(e,t){return function(i){return e(parseFloat(i))+(t||xe(i))}},mf=function(e,t,i){return dl(e,t,0,1,i)},ul=function(e,t,i){return $t(i,function(n){return e[~~t(n)]})},pf=function s(e,t,i){var n=t-e;return be(e)?ul(e,s(0,e.length),t):$t(i,function(r){return(n+(r-e)%n)%n+e})},_f=function s(e,t,i){var n=t-e,r=n*2;return be(e)?ul(e,s(0,e.length-1),t):$t(i,function(a){return a=(r+(a-e)%r)%r||0,e+(a>n?r-a:a)})},on=function(e){for(var t=0,i="",n,r,a,o;~(n=e.indexOf("random(",t));)a=e.indexOf(")",n),o=e.charAt(n+7)==="[",r=e.substr(n+7,a-n-7).match(o?jo:Hs),i+=e.substr(t,n-t)+hl(o?r:+r[0],o?0:+r[1],+r[2]||1e-5),t=a+1;return i+e.substr(t,e.length-t)},dl=function(e,t,i,n,r){var a=t-e,o=n-i;return $t(r,function(c){return i+((c-e)/a*o||0)})},gf=function s(e,t,i,n){var r=isNaN(e+t)?0:function(p){return(1-p)*e+p*t};if(!r){var a=_e(e),o={},c,l,h,u,f;if(i===!0&&(n=1)&&(i=null),a)e={p:e},t={p:t};else if(be(e)&&!be(t)){for(h=[],u=e.length,f=u-2,l=1;l<u;l++)h.push(s(e[l-1],e[l]));u--,r=function(g){g*=u;var d=Math.min(f,~~g);return h[d](g-d)},i=t}else n||(e=Ti(be(e)?[]:{},e));if(!h){for(c in t)Mr.call(o,e,c,"get",t[c]);r=function(g){return Ir(g,o)||(a?e.p:e)}}}return $t(i,r)},pa=function(e,t,i){var n=e.labels,r=He,a,o,c;for(a in n)o=n[a]-t,o<0==!!i&&o&&r>(o=Math.abs(o))&&(c=a,r=o);return c},Ve=function(e,t,i){var n=e.vars,r=n[t],a=ne,o=e._ctx,c,l,h;if(r)return c=n[t+"Params"],l=n.callbackScope||e,i&&Et.length&&Xn(),o&&(ne=o),h=c?r.apply(l,c):r.call(l),ne=a,h},Gi=function(e){return Dt(e),e.scrollTrigger&&e.scrollTrigger.kill(!!ye),e.progress()<1&&Ve(e,"onInterrupt"),e},pi,fl=[],ml=function(e){if(e)if(e=!e.name&&e.default||e,br()||e.headless){var t=e.name,i=oe(e),n=t&&!i&&e.init?function(){this._props=[]}:e,r={init:an,render:Ir,add:Mr,kill:Df,modifier:If,rawVars:0},a={targetTest:0,get:0,getSetter:Rr,aliases:{},register:0};if(Mi(),e!==n){if(Oe[t])return;Ue(n,Ue(jn(e,r),a)),Ti(n.prototype,Ti(r,jn(e,a))),Oe[n.prop=t]=n,e.targetTest&&(Vn.push(n),Cr[t]=1),t=(t==="css"?"CSS":t.charAt(0).toUpperCase()+t.substr(1))+"Plugin"}Qo(t,n),e.register&&e.register(Ee,n,Me)}else fl.push(e)},Z=255,Yi={aqua:[0,Z,Z],lime:[0,Z,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,Z],navy:[0,0,128],white:[Z,Z,Z],olive:[128,128,0],yellow:[Z,Z,0],orange:[Z,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[Z,0,0],pink:[Z,192,203],cyan:[0,Z,Z],transparent:[Z,Z,Z,0]},Ts=function(e,t,i){return e+=e<0?1:e>1?-1:0,(e*6<1?t+(i-t)*e*6:e<.5?i:e*3<2?t+(i-t)*(2/3-e)*6:t)*Z+.5|0},pl=function(e,t,i){var n=e?bt(e)?[e>>16,e>>8&Z,e&Z]:0:Yi.black,r,a,o,c,l,h,u,f,p,g;if(!n){if(e.substr(-1)===","&&(e=e.substr(0,e.length-1)),Yi[e])n=Yi[e];else if(e.charAt(0)==="#"){if(e.length<6&&(r=e.charAt(1),a=e.charAt(2),o=e.charAt(3),e="#"+r+r+a+a+o+o+(e.length===5?e.charAt(4)+e.charAt(4):"")),e.length===9)return n=parseInt(e.substr(1,6),16),[n>>16,n>>8&Z,n&Z,parseInt(e.substr(7),16)/255];e=parseInt(e.substr(1),16),n=[e>>16,e>>8&Z,e&Z]}else if(e.substr(0,3)==="hsl"){if(n=g=e.match(Hs),!t)c=+n[0]%360/360,l=+n[1]/100,h=+n[2]/100,a=h<=.5?h*(l+1):h+l-h*l,r=h*2-a,n.length>3&&(n[3]*=1),n[0]=Ts(c+1/3,r,a),n[1]=Ts(c,r,a),n[2]=Ts(c-1/3,r,a);else if(~e.indexOf("="))return n=e.match(Yo),i&&n.length<4&&(n[3]=1),n}else n=e.match(Hs)||Yi.transparent;n=n.map(Number)}return t&&!g&&(r=n[0]/Z,a=n[1]/Z,o=n[2]/Z,u=Math.max(r,a,o),f=Math.min(r,a,o),h=(u+f)/2,u===f?c=l=0:(p=u-f,l=h>.5?p/(2-u-f):p/(u+f),c=u===r?(a-o)/p+(a<o?6:0):u===a?(o-r)/p+2:(r-a)/p+4,c*=60),n[0]=~~(c+.5),n[1]=~~(l*100+.5),n[2]=~~(h*100+.5)),i&&n.length<4&&(n[3]=1),n},_l=function(e){var t=[],i=[],n=-1;return e.split(Rt).forEach(function(r){var a=r.match(mi)||[];t.push.apply(t,a),i.push(n+=a.length+1)}),t.c=i,t},_a=function(e,t,i){var n="",r=(e+n).match(Rt),a=t?"hsla(":"rgba(",o=0,c,l,h,u;if(!r)return e;if(r=r.map(function(f){return(f=pl(f,t,1))&&a+(t?f[0]+","+f[1]+"%,"+f[2]+"%,"+f[3]:f.join(","))+")"}),i&&(h=_l(e),c=i.c,c.join(n)!==h.c.join(n)))for(l=e.replace(Rt,"1").split(mi),u=l.length-1;o<u;o++)n+=l[o]+(~c.indexOf(o)?r.shift()||a+"0,0,0,0)":(h.length?h:r.length?r:i).shift());if(!l)for(l=e.split(Rt),u=l.length-1;o<u;o++)n+=l[o]+r[o];return n+l[u]},Rt=function(){var s="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b",e;for(e in Yi)s+="|"+e+"\\b";return new RegExp(s+")","gi")}(),yf=/hsl[a]?\(/,gl=function(e){var t=e.join(" "),i;if(Rt.lastIndex=0,Rt.test(t))return i=yf.test(t),e[1]=_a(e[1],i),e[0]=_a(e[0],i,_l(e[1])),!0},ln,$e=function(){var s=Date.now,e=500,t=33,i=s(),n=i,r=1e3/240,a=r,o=[],c,l,h,u,f,p,g=function d(m){var _=s()-n,x=m===!0,b,T,k,C;if((_>e||_<0)&&(i+=_-t),n+=_,k=n-i,b=k-a,(b>0||x)&&(C=++u.frame,f=k-u.time*1e3,u.time=k=k/1e3,a+=b+(b>=r?4:r-b),T=1),x||(c=l(d)),T)for(p=0;p<o.length;p++)o[p](k,f,C,m)};return u={time:0,frame:0,tick:function(){g(!0)},deltaRatio:function(m){return f/(1e3/(m||60))},wake:function(){Ho&&(!Ws&&br()&&(st=Ws=window,wr=st.document||{},Le.gsap=Ee,(st.gsapVersions||(st.gsapVersions=[])).push(Ee.version),Wo(Yn||st.GreenSockGlobals||!st.gsap&&st||{}),fl.forEach(ml)),h=typeof requestAnimationFrame<"u"&&requestAnimationFrame,c&&u.sleep(),l=h||function(m){return setTimeout(m,a-u.time*1e3+1|0)},ln=1,g(2))},sleep:function(){(h?cancelAnimationFrame:clearTimeout)(c),ln=0,l=an},lagSmoothing:function(m,_){e=m||1/0,t=Math.min(_||33,e)},fps:function(m){r=1e3/(m||240),a=u.time*1e3+r},add:function(m,_,x){var b=_?function(T,k,C,y){m(T,k,C,y),u.remove(b)}:m;return u.remove(m),o[x?"unshift":"push"](b),Mi(),b},remove:function(m,_){~(_=o.indexOf(m))&&o.splice(_,1)&&p>=_&&p--},_listeners:o},u}(),Mi=function(){return!ln&&$e.wake()},H={},vf=/^[\d.\-M][\d.\-,\s]/,xf=/["']/g,bf=function(e){for(var t={},i=e.substr(1,e.length-3).split(":"),n=i[0],r=1,a=i.length,o,c,l;r<a;r++)c=i[r],o=r!==a-1?c.lastIndexOf(","):c.length,l=c.substr(0,o),t[n]=isNaN(l)?l.replace(xf,"").trim():+l,n=c.substr(o+1).trim();return t},wf=function(e){var t=e.indexOf("(")+1,i=e.indexOf(")"),n=e.indexOf("(",t);return e.substring(t,~n&&n<i?e.indexOf(")",i+1):i)},Af=function(e){var t=(e+"").split("("),i=H[t[0]];return i&&t.length>1&&i.config?i.config.apply(null,~e.indexOf("{")?[bf(t[1])]:wf(e).split(",").map(el)):H._CE&&vf.test(e)?H._CE("",e):i},yl=function(e){return function(t){return 1-e(1-t)}},vl=function s(e,t){for(var i=e._first,n;i;)i instanceof Ce?s(i,t):i.vars.yoyoEase&&(!i._yoyo||!i._repeat)&&i._yoyo!==t&&(i.timeline?s(i.timeline,t):(n=i._ease,i._ease=i._yEase,i._yEase=n,i._yoyo=t)),i=i._next},Qt=function(e,t){return e&&(oe(e)?e:H[e]||Af(e))||t},ri=function(e,t,i,n){i===void 0&&(i=function(c){return 1-t(1-c)}),n===void 0&&(n=function(c){return c<.5?t(c*2)/2:1-t((1-c)*2)/2});var r={easeIn:t,easeOut:i,easeInOut:n},a;return Pe(e,function(o){H[o]=Le[o]=r,H[a=o.toLowerCase()]=i;for(var c in r)H[a+(c==="easeIn"?".in":c==="easeOut"?".out":".inOut")]=H[o+"."+c]=r[c]}),r},xl=function(e){return function(t){return t<.5?(1-e(1-t*2))/2:.5+e((t-.5)*2)/2}},ks=function s(e,t,i){var n=t>=1?t:1,r=(i||(e?.3:.45))/(t<1?t:1),a=r/js*(Math.asin(1/n)||0),o=function(h){return h===1?1:n*Math.pow(2,-10*h)*Qd((h-a)*r)+1},c=e==="out"?o:e==="in"?function(l){return 1-o(1-l)}:xl(o);return r=js/r,c.config=function(l,h){return s(e,l,h)},c},Ps=function s(e,t){t===void 0&&(t=1.70158);var i=function(a){return a?--a*a*((t+1)*a+t)+1:0},n=e==="out"?i:e==="in"?function(r){return 1-i(1-r)}:xl(i);return n.config=function(r){return s(e,r)},n};Pe("Linear,Quad,Cubic,Quart,Quint,Strong",function(s,e){var t=e<5?e+1:e;ri(s+",Power"+(t-1),e?function(i){return Math.pow(i,t)}:function(i){return i},function(i){return 1-Math.pow(1-i,t)},function(i){return i<.5?Math.pow(i*2,t)/2:1-Math.pow((1-i)*2,t)/2})});H.Linear.easeNone=H.none=H.Linear.easeIn;ri("Elastic",ks("in"),ks("out"),ks());(function(s,e){var t=1/e,i=2*t,n=2.5*t,r=function(o){return o<t?s*o*o:o<i?s*Math.pow(o-1.5/e,2)+.75:o<n?s*(o-=2.25/e)*o+.9375:s*Math.pow(o-2.625/e,2)+.984375};ri("Bounce",function(a){return 1-r(1-a)},r)})(7.5625,2.75);ri("Expo",function(s){return Math.pow(2,10*(s-1))*s+s*s*s*s*s*s*(1-s)});ri("Circ",function(s){return-(zo(1-s*s)-1)});ri("Sine",function(s){return s===1?1:-Wd(s*jd)+1});ri("Back",Ps("in"),Ps("out"),Ps());H.SteppedEase=H.steps=Le.SteppedEase={config:function(e,t){e===void 0&&(e=1);var i=1/e,n=e+(t?0:1),r=t?1:0,a=1-te;return function(o){return((n*yn(0,a,o)|0)+r)*i}}};Ci.ease=H["quad.out"];Pe("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(s){return Tr+=s+","+s+"Params,"});var bl=function(e,t){this.id=Hd++,e._gsap=this,this.target=e,this.harness=t,this.get=t?t.get:Jo,this.set=t?t.getSetter:Rr},cn=function(){function s(t){this.vars=t,this._delay=+t.delay||0,(this._repeat=t.repeat===1/0?-2:t.repeat||0)&&(this._rDelay=t.repeatDelay||0,this._yoyo=!!t.yoyo||!!t.yoyoEase),this._ts=1,Pi(this,+t.duration,1,1),this.data=t.data,ne&&(this._ctx=ne,ne.data.push(this)),ln||$e.wake()}var e=s.prototype;return e.delay=function(i){return i||i===0?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+i-this._delay),this._delay=i,this):this._delay},e.duration=function(i){return arguments.length?this.totalDuration(this._repeat>0?i+(i+this._rDelay)*this._repeat:i):this.totalDuration()&&this._dur},e.totalDuration=function(i){return arguments.length?(this._dirty=0,Pi(this,this._repeat<0?i:(i-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(i,n){if(Mi(),!arguments.length)return this._tTime;var r=this._dp;if(r&&r.smoothChildTiming&&this._ts){for(as(this,i),!r._dp||r.parent||nl(r,this);r&&r.parent;)r.parent._time!==r._start+(r._ts>=0?r._tTime/r._ts:(r.totalDuration()-r._tTime)/-r._ts)&&r.totalTime(r._tTime,!0),r=r.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&i<this._tDur||this._ts<0&&i>0||!this._tDur&&!i)&&rt(this._dp,this,this._start-this._delay)}return(this._tTime!==i||!this._dur&&!n||this._initted&&Math.abs(this._zTime)===te||!i&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=i),Zo(this,i,n)),this},e.time=function(i,n){return arguments.length?this.totalTime(Math.min(this.totalDuration(),i+fa(this))%(this._dur+this._rDelay)||(i?this._dur:0),n):this._time},e.totalProgress=function(i,n){return arguments.length?this.totalTime(this.totalDuration()*i,n):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},e.progress=function(i,n){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(this.iteration()&1)?1-i:i)+fa(this),n):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(i,n){var r=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(i-1)*r,n):this._repeat?ki(this._tTime,r)+1:1},e.timeScale=function(i,n){if(!arguments.length)return this._rts===-te?0:this._rts;if(this._rts===i)return this;var r=this.parent&&this._ts?Hn(this.parent._time,this):this._tTime;return this._rts=+i||0,this._ts=this._ps||i===-te?0:this._rts,this.totalTime(yn(-Math.abs(this._delay),this.totalDuration(),r),n!==!1),rs(this),sf(this)},e.paused=function(i){return arguments.length?(this._ps!==i&&(this._ps=i,i?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Mi(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,this.progress()===1&&Math.abs(this._zTime)!==te&&(this._tTime-=te)))),this):this._ps},e.startTime=function(i){if(arguments.length){this._start=i;var n=this.parent||this._dp;return n&&(n._sort||!this.parent)&&rt(n,this,i-this._delay),this}return this._start},e.endTime=function(i){return this._start+(ke(i)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(i){var n=this.parent||this._dp;return n?i&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?Hn(n.rawTime(i),this):this._tTime:this._tTime},e.revert=function(i){i===void 0&&(i=Zd);var n=ye;return ye=i,Pr(this)&&(this.timeline&&this.timeline.revert(i),this.totalTime(-.01,i.suppressEvents)),this.data!=="nested"&&i.kill!==!1&&this.kill(),ye=n,this},e.globalTime=function(i){for(var n=this,r=arguments.length?i:n.rawTime();n;)r=n._start+r/(Math.abs(n._ts)||1),n=n._dp;return!this.parent&&this._sat?this._sat.globalTime(i):r},e.repeat=function(i){return arguments.length?(this._repeat=i===1/0?-2:i,ma(this)):this._repeat===-2?1/0:this._repeat},e.repeatDelay=function(i){if(arguments.length){var n=this._time;return this._rDelay=i,ma(this),n?this.time(n):this}return this._rDelay},e.yoyo=function(i){return arguments.length?(this._yoyo=i,this):this._yoyo},e.seek=function(i,n){return this.totalTime(ze(this,i),ke(n))},e.restart=function(i,n){return this.play().totalTime(i?-this._delay:0,ke(n)),this._dur||(this._zTime=-te),this},e.play=function(i,n){return i!=null&&this.seek(i,n),this.reversed(!1).paused(!1)},e.reverse=function(i,n){return i!=null&&this.seek(i||this.totalDuration(),n),this.reversed(!0).paused(!1)},e.pause=function(i,n){return i!=null&&this.seek(i,n),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(i){return arguments.length?(!!i!==this.reversed()&&this.timeScale(-this._rts||(i?-te:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-te,this},e.isActive=function(){var i=this.parent||this._dp,n=this._start,r;return!!(!i||this._ts&&this._initted&&i.isActive()&&(r=i.rawTime(!0))>=n&&r<this.endTime(!0)-te)},e.eventCallback=function(i,n,r){var a=this.vars;return arguments.length>1?(n?(a[i]=n,r&&(a[i+"Params"]=r),i==="onUpdate"&&(this._onUpdate=n)):delete a[i],this):a[i]},e.then=function(i){var n=this;return new Promise(function(r){var a=oe(i)?i:tl,o=function(){var l=n.then;n.then=null,oe(a)&&(a=a(n))&&(a.then||a===n)&&(n.then=l),r(a),n.then=l};n._initted&&n.totalProgress()===1&&n._ts>=0||!n._tTime&&n._ts<0?o():n._prom=o})},e.kill=function(){Gi(this)},s}();Ue(cn.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-te,_prom:0,_ps:!1,_rts:1});var Ce=function(s){qo(e,s);function e(i,n){var r;return i===void 0&&(i={}),r=s.call(this,i)||this,r.labels={},r.smoothChildTiming=!!i.smoothChildTiming,r.autoRemoveChildren=!!i.autoRemoveChildren,r._sort=ke(i.sortChildren),re&&rt(i.parent||re,ft(r),n),i.reversed&&r.reverse(),i.paused&&r.paused(!0),i.scrollTrigger&&sl(ft(r),i.scrollTrigger),r}var t=e.prototype;return t.to=function(n,r,a){return Ki(0,arguments,this),this},t.from=function(n,r,a){return Ki(1,arguments,this),this},t.fromTo=function(n,r,a,o){return Ki(2,arguments,this),this},t.set=function(n,r,a){return r.duration=0,r.parent=this,Qi(r).repeatDelay||(r.repeat=0),r.immediateRender=!!r.immediateRender,new he(n,r,ze(this,a),1),this},t.call=function(n,r,a){return rt(this,he.delayedCall(0,n,r),a)},t.staggerTo=function(n,r,a,o,c,l,h){return a.duration=r,a.stagger=a.stagger||o,a.onComplete=l,a.onCompleteParams=h,a.parent=this,new he(n,a,ze(this,c)),this},t.staggerFrom=function(n,r,a,o,c,l,h){return a.runBackwards=1,Qi(a).immediateRender=ke(a.immediateRender),this.staggerTo(n,r,a,o,c,l,h)},t.staggerFromTo=function(n,r,a,o,c,l,h,u){return o.startAt=a,Qi(o).immediateRender=ke(o.immediateRender),this.staggerTo(n,r,o,c,l,h,u)},t.render=function(n,r,a){var o=this._time,c=this._dirty?this.totalDuration():this._tDur,l=this._dur,h=n<=0?0:ue(n),u=this._zTime<0!=n<0&&(this._initted||!l),f,p,g,d,m,_,x,b,T,k,C,y;if(this!==re&&h>c&&n>=0&&(h=c),h!==this._tTime||a||u){if(o!==this._time&&l&&(h+=this._time-o,n+=this._time-o),f=h,T=this._start,b=this._ts,_=!b,u&&(l||(o=this._zTime),(n||!r)&&(this._zTime=n)),this._repeat){if(C=this._yoyo,m=l+this._rDelay,this._repeat<-1&&n<0)return this.totalTime(m*100+n,r,a);if(f=ue(h%m),h===c?(d=this._repeat,f=l):(k=ue(h/m),d=~~k,d&&d===k&&(f=l,d--),f>l&&(f=l)),k=ki(this._tTime,m),!o&&this._tTime&&k!==d&&this._tTime-k*m-this._dur<=0&&(k=d),C&&d&1&&(f=l-f,y=1),d!==k&&!this._lock){var v=C&&k&1,A=v===(C&&d&1);if(d<k&&(v=!v),o=v?0:h%l?l:h,this._lock=1,this.render(o||(y?0:ue(d*m)),r,!l)._lock=0,this._tTime=h,!r&&this.parent&&Ve(this,"onRepeat"),this.vars.repeatRefresh&&!y&&(this.invalidate()._lock=1),o&&o!==this._time||_!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(l=this._dur,c=this._tDur,A&&(this._lock=2,o=v?l:-1e-4,this.render(o,!0),this.vars.repeatRefresh&&!y&&this.invalidate()),this._lock=0,!this._ts&&!_)return this;vl(this,y)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(x=lf(this,ue(o),ue(f)),x&&(h-=f-(f=x._start))),this._tTime=h,this._time=f,this._act=!b,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=n,o=0),!o&&h&&!r&&!k&&(Ve(this,"onStart"),this._tTime!==h))return this;if(f>=o&&n>=0)for(p=this._first;p;){if(g=p._next,(p._act||f>=p._start)&&p._ts&&x!==p){if(p.parent!==this)return this.render(n,r,a);if(p.render(p._ts>0?(f-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(f-p._start)*p._ts,r,a),f!==this._time||!this._ts&&!_){x=0,g&&(h+=this._zTime=-te);break}}p=g}else{p=this._last;for(var E=n<0?n:f;p;){if(g=p._prev,(p._act||E<=p._end)&&p._ts&&x!==p){if(p.parent!==this)return this.render(n,r,a);if(p.render(p._ts>0?(E-p._start)*p._ts:(p._dirty?p.totalDuration():p._tDur)+(E-p._start)*p._ts,r,a||ye&&Pr(p)),f!==this._time||!this._ts&&!_){x=0,g&&(h+=this._zTime=E?-te:te);break}}p=g}}if(x&&!r&&(this.pause(),x.render(f>=o?0:-te)._zTime=f>=o?1:-1,this._ts))return this._start=T,rs(this),this.render(n,r,a);this._onUpdate&&!r&&Ve(this,"onUpdate",!0),(h===c&&this._tTime>=this.totalDuration()||!h&&o)&&(T===this._start||Math.abs(b)!==Math.abs(this._ts))&&(this._lock||((n||!l)&&(h===c&&this._ts>0||!h&&this._ts<0)&&Dt(this,1),!r&&!(n<0&&!o)&&(h||o||!c)&&(Ve(this,h===c&&n>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(h<c&&this.timeScale()>0)&&this._prom())))}return this},t.add=function(n,r){var a=this;if(bt(r)||(r=ze(this,r,n)),!(n instanceof cn)){if(be(n))return n.forEach(function(o){return a.add(o,r)}),this;if(_e(n))return this.addLabel(n,r);if(oe(n))n=he.delayedCall(0,n);else return this}return this!==n?rt(this,n,r):this},t.getChildren=function(n,r,a,o){n===void 0&&(n=!0),r===void 0&&(r=!0),a===void 0&&(a=!0),o===void 0&&(o=-He);for(var c=[],l=this._first;l;)l._start>=o&&(l instanceof he?r&&c.push(l):(a&&c.push(l),n&&c.push.apply(c,l.getChildren(!0,r,a)))),l=l._next;return c},t.getById=function(n){for(var r=this.getChildren(1,1,1),a=r.length;a--;)if(r[a].vars.id===n)return r[a]},t.remove=function(n){return _e(n)?this.removeLabel(n):oe(n)?this.killTweensOf(n):(n.parent===this&&ss(this,n),n===this._recent&&(this._recent=this._last),Wt(this))},t.totalTime=function(n,r){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=ue($e.time-(this._ts>0?n/this._ts:(this.totalDuration()-n)/-this._ts))),s.prototype.totalTime.call(this,n,r),this._forcing=0,this):this._tTime},t.addLabel=function(n,r){return this.labels[n]=ze(this,r),this},t.removeLabel=function(n){return delete this.labels[n],this},t.addPause=function(n,r,a){var o=he.delayedCall(0,r||an,a);return o.data="isPause",this._hasPause=1,rt(this,o,ze(this,n))},t.removePause=function(n){var r=this._first;for(n=ze(this,n);r;)r._start===n&&r.data==="isPause"&&Dt(r),r=r._next},t.killTweensOf=function(n,r,a){for(var o=this.getTweensOf(n,a),c=o.length;c--;)kt!==o[c]&&o[c].kill(n,r);return this},t.getTweensOf=function(n,r){for(var a=[],o=We(n),c=this._first,l=bt(r),h;c;)c instanceof he?ef(c._targets,o)&&(l?(!kt||c._initted&&c._ts)&&c.globalTime(0)<=r&&c.globalTime(c.totalDuration())>r:!r||c.isActive())&&a.push(c):(h=c.getTweensOf(o,r)).length&&a.push.apply(a,h),c=c._next;return a},t.tweenTo=function(n,r){r=r||{};var a=this,o=ze(a,n),c=r,l=c.startAt,h=c.onStart,u=c.onStartParams,f=c.immediateRender,p,g=he.to(a,Ue({ease:r.ease||"none",lazy:!1,immediateRender:!1,time:o,overwrite:"auto",duration:r.duration||Math.abs((o-(l&&"time"in l?l.time:a._time))/a.timeScale())||te,onStart:function(){if(a.pause(),!p){var m=r.duration||Math.abs((o-(l&&"time"in l?l.time:a._time))/a.timeScale());g._dur!==m&&Pi(g,m,0,1).render(g._time,!0,!0),p=1}h&&h.apply(g,u||[])}},r));return f?g.render(0):g},t.tweenFromTo=function(n,r,a){return this.tweenTo(r,Ue({startAt:{time:ze(this,n)}},a))},t.recent=function(){return this._recent},t.nextLabel=function(n){return n===void 0&&(n=this._time),pa(this,ze(this,n))},t.previousLabel=function(n){return n===void 0&&(n=this._time),pa(this,ze(this,n),1)},t.currentLabel=function(n){return arguments.length?this.seek(n,!0):this.previousLabel(this._time+te)},t.shiftChildren=function(n,r,a){a===void 0&&(a=0);for(var o=this._first,c=this.labels,l;o;)o._start>=a&&(o._start+=n,o._end+=n),o=o._next;if(r)for(l in c)c[l]>=a&&(c[l]+=n);return Wt(this)},t.invalidate=function(n){var r=this._first;for(this._lock=0;r;)r.invalidate(n),r=r._next;return s.prototype.invalidate.call(this,n)},t.clear=function(n){n===void 0&&(n=!0);for(var r=this._first,a;r;)a=r._next,this.remove(r),r=a;return this._dp&&(this._time=this._tTime=this._pTime=0),n&&(this.labels={}),Wt(this)},t.totalDuration=function(n){var r=0,a=this,o=a._last,c=He,l,h,u;if(arguments.length)return a.timeScale((a._repeat<0?a.duration():a.totalDuration())/(a.reversed()?-n:n));if(a._dirty){for(u=a.parent;o;)l=o._prev,o._dirty&&o.totalDuration(),h=o._start,h>c&&a._sort&&o._ts&&!a._lock?(a._lock=1,rt(a,o,h-o._delay,1)._lock=0):c=h,h<0&&o._ts&&(r-=h,(!u&&!a._dp||u&&u.smoothChildTiming)&&(a._start+=h/a._ts,a._time-=h,a._tTime-=h),a.shiftChildren(-h,!1,-1/0),c=0),o._end>r&&o._ts&&(r=o._end),o=l;Pi(a,a===re&&a._time>r?a._time:r,1,1),a._dirty=0}return a._tDur},e.updateRoot=function(n){if(re._ts&&(Zo(re,Hn(n,re)),Ko=$e.frame),$e.frame>=ua){ua+=Fe.autoSleep||120;var r=re._first;if((!r||!r._ts)&&Fe.autoSleep&&$e._listeners.length<2){for(;r&&!r._ts;)r=r._next;r||$e.sleep()}}},e}(cn);Ue(Ce.prototype,{_lock:0,_hasPause:0,_forcing:0});var Cf=function(e,t,i,n,r,a,o){var c=new Me(this._pt,e,t,0,1,Pl,null,r),l=0,h=0,u,f,p,g,d,m,_,x;for(c.b=i,c.e=n,i+="",n+="",(_=~n.indexOf("random("))&&(n=on(n)),a&&(x=[i,n],a(x,e,t),i=x[0],n=x[1]),f=i.match(As)||[];u=As.exec(n);)g=u[0],d=n.substring(l,u.index),p?p=(p+1)%5:d.substr(-5)==="rgba("&&(p=1),g!==f[h++]&&(m=parseFloat(f[h-1])||0,c._pt={_next:c._pt,p:d||h===1?d:",",s:m,c:g.charAt(1)==="="?yi(m,g)-m:parseFloat(g)-m,m:p&&p<4?Math.round:0},l=As.lastIndex);return c.c=l<n.length?n.substring(l,n.length):"",c.fp=o,(Xo.test(n)||_)&&(c.e=0),this._pt=c,c},Mr=function(e,t,i,n,r,a,o,c,l,h){oe(n)&&(n=n(r||0,e,a));var u=e[t],f=i!=="get"?i:oe(u)?l?e[t.indexOf("set")||!oe(e["get"+t.substr(3)])?t:"get"+t.substr(3)](l):e[t]():u,p=oe(u)?l?Sf:Tl:Er,g;if(_e(n)&&(~n.indexOf("random(")&&(n=on(n)),n.charAt(1)==="="&&(g=yi(f,n)+(xe(f)||0),(g||g===0)&&(n=g))),!h||f!==n||ir)return!isNaN(f*n)&&n!==""?(g=new Me(this._pt,e,t,+f||0,n-(f||0),typeof u=="boolean"?Rf:kl,0,p),l&&(g.fp=l),o&&g.modifier(o,this,e),this._pt=g):(!u&&!(t in e)&&Ar(t,n),Cf.call(this,e,t,f,n,p,c||Fe.stringFilter,l))},Tf=function(e,t,i,n,r){if(oe(e)&&(e=Ji(e,r,t,i,n)),!ct(e)||e.style&&e.nodeType||be(e)||Go(e))return _e(e)?Ji(e,r,t,i,n):e;var a={},o;for(o in e)a[o]=Ji(e[o],r,t,i,n);return a},wl=function(e,t,i,n,r,a){var o,c,l,h;if(Oe[e]&&(o=new Oe[e]).init(r,o.rawVars?t[e]:Tf(t[e],n,r,a,i),i,n,a)!==!1&&(i._pt=c=new Me(i._pt,r,e,0,1,o.render,o,0,o.priority),i!==pi))for(l=i._ptLookup[i._targets.indexOf(r)],h=o._props.length;h--;)l[o._props[h]]=c;return o},kt,ir,Sr=function s(e,t,i){var n=e.vars,r=n.ease,a=n.startAt,o=n.immediateRender,c=n.lazy,l=n.onUpdate,h=n.runBackwards,u=n.yoyoEase,f=n.keyframes,p=n.autoRevert,g=e._dur,d=e._startAt,m=e._targets,_=e.parent,x=_&&_.data==="nested"?_.vars.targets:m,b=e._overwrite==="auto"&&!vr,T=e.timeline,k,C,y,v,A,E,S,R,V,X,q,B,G;if(T&&(!f||!r)&&(r="none"),e._ease=Qt(r,Ci.ease),e._yEase=u?yl(Qt(u===!0?r:u,Ci.ease)):0,u&&e._yoyo&&!e._repeat&&(u=e._yEase,e._yEase=e._ease,e._ease=u),e._from=!T&&!!n.runBackwards,!T||f&&!n.stagger){if(R=m[0]?Ht(m[0]).harness:0,B=R&&n[R.prop],k=jn(n,Cr),d&&(d._zTime<0&&d.progress(1),t<0&&h&&o&&!p?d.render(-1,!0):d.revert(h&&g?$n:Jd),d._lazy=0),a){if(Dt(e._startAt=he.set(m,Ue({data:"isStart",overwrite:!1,parent:_,immediateRender:!0,lazy:!d&&ke(c),startAt:null,delay:0,onUpdate:l&&function(){return Ve(e,"onUpdate")},stagger:0},a))),e._startAt._dp=0,e._startAt._sat=e,t<0&&(ye||!o&&!p)&&e._startAt.revert($n),o&&g&&t<=0&&i<=0){t&&(e._zTime=t);return}}else if(h&&g&&!d){if(t&&(o=!1),y=Ue({overwrite:!1,data:"isFromStart",lazy:o&&!d&&ke(c),immediateRender:o,stagger:0,parent:_},k),B&&(y[R.prop]=B),Dt(e._startAt=he.set(m,y)),e._startAt._dp=0,e._startAt._sat=e,t<0&&(ye?e._startAt.revert($n):e._startAt.render(-1,!0)),e._zTime=t,!o)s(e._startAt,te,te);else if(!t)return}for(e._pt=e._ptCache=0,c=g&&ke(c)||c&&!g,C=0;C<m.length;C++){if(A=m[C],S=A._gsap||kr(m)[C]._gsap,e._ptLookup[C]=X={},Qs[S.id]&&Et.length&&Xn(),q=x===m?C:x.indexOf(A),R&&(V=new R).init(A,B||k,e,q,x)!==!1&&(e._pt=v=new Me(e._pt,A,V.name,0,1,V.render,V,0,V.priority),V._props.forEach(function($){X[$]=v}),V.priority&&(E=1)),!R||B)for(y in k)Oe[y]&&(V=wl(y,k,e,q,A,x))?V.priority&&(E=1):X[y]=v=Mr.call(e,A,y,"get",k[y],q,x,0,n.stringFilter);e._op&&e._op[C]&&e.kill(A,e._op[C]),b&&e._pt&&(kt=e,re.killTweensOf(A,X,e.globalTime(t)),G=!e.parent,kt=0),e._pt&&c&&(Qs[S.id]=1)}E&&Ml(e),e._onInit&&e._onInit(e)}e._onUpdate=l,e._initted=(!e._op||e._pt)&&!G,f&&t<=0&&T.render(He,!0,!0)},kf=function(e,t,i,n,r,a,o,c){var l=(e._pt&&e._ptCache||(e._ptCache={}))[t],h,u,f,p;if(!l)for(l=e._ptCache[t]=[],f=e._ptLookup,p=e._targets.length;p--;){if(h=f[p][t],h&&h.d&&h.d._pt)for(h=h.d._pt;h&&h.p!==t&&h.fp!==t;)h=h._next;if(!h)return ir=1,e.vars[t]="+=0",Sr(e,o),ir=0,c?rn(t+" not eligible for reset"):1;l.push(h)}for(p=l.length;p--;)u=l[p],h=u._pt||u,h.s=(n||n===0)&&!r?n:h.s+(n||0)+a*h.c,h.c=i-h.s,u.e&&(u.e=le(i)+xe(u.e)),u.b&&(u.b=h.s+xe(u.b))},Pf=function(e,t){var i=e[0]?Ht(e[0]).harness:0,n=i&&i.aliases,r,a,o,c;if(!n)return t;r=Ti({},t);for(a in n)if(a in r)for(c=n[a].split(","),o=c.length;o--;)r[c[o]]=r[a];return r},Mf=function(e,t,i,n){var r=t.ease||n||"power1.inOut",a,o;if(be(t))o=i[e]||(i[e]=[]),t.forEach(function(c,l){return o.push({t:l/(t.length-1)*100,v:c,e:r})});else for(a in t)o=i[a]||(i[a]=[]),a==="ease"||o.push({t:parseFloat(e),v:t[a],e:r})},Ji=function(e,t,i,n,r){return oe(e)?e.call(t,i,n,r):_e(e)&&~e.indexOf("random(")?on(e):e},Al=Tr+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",Cl={};Pe(Al+",id,stagger,delay,duration,paused,scrollTrigger",function(s){return Cl[s]=1});var he=function(s){qo(e,s);function e(i,n,r,a){var o;typeof n=="number"&&(r.duration=n,n=r,r=null),o=s.call(this,a?n:Qi(n))||this;var c=o.vars,l=c.duration,h=c.delay,u=c.immediateRender,f=c.stagger,p=c.overwrite,g=c.keyframes,d=c.defaults,m=c.scrollTrigger,_=c.yoyoEase,x=n.parent||re,b=(be(i)||Go(i)?bt(i[0]):"length"in n)?[i]:We(i),T,k,C,y,v,A,E,S;if(o._targets=b.length?kr(b):rn("GSAP target "+i+" not found. https://gsap.com",!Fe.nullTargetWarn)||[],o._ptLookup=[],o._overwrite=p,g||f||Mn(l)||Mn(h)){if(n=o.vars,T=o.timeline=new Ce({data:"nested",defaults:d||{},targets:x&&x.data==="nested"?x.vars.targets:b}),T.kill(),T.parent=T._dp=ft(o),T._start=0,f||Mn(l)||Mn(h)){if(y=b.length,E=f&&ll(f),ct(f))for(v in f)~Al.indexOf(v)&&(S||(S={}),S[v]=f[v]);for(k=0;k<y;k++)C=jn(n,Cl),C.stagger=0,_&&(C.yoyoEase=_),S&&Ti(C,S),A=b[k],C.duration=+Ji(l,ft(o),k,A,b),C.delay=(+Ji(h,ft(o),k,A,b)||0)-o._delay,!f&&y===1&&C.delay&&(o._delay=h=C.delay,o._start+=h,C.delay=0),T.to(A,C,E?E(k,A,b):0),T._ease=H.none;T.duration()?l=h=0:o.timeline=0}else if(g){Qi(Ue(T.vars.defaults,{ease:"none"})),T._ease=Qt(g.ease||n.ease||"none");var R=0,V,X,q;if(be(g))g.forEach(function(B){return T.to(b,B,">")}),T.duration();else{C={};for(v in g)v==="ease"||v==="easeEach"||Mf(v,g[v],C,g.easeEach);for(v in C)for(V=C[v].sort(function(B,G){return B.t-G.t}),R=0,k=0;k<V.length;k++)X=V[k],q={ease:X.e,duration:(X.t-(k?V[k-1].t:0))/100*l},q[v]=X.v,T.to(b,q,R),R+=q.duration;T.duration()<l&&T.to({},{duration:l-T.duration()})}}l||o.duration(l=T.duration())}else o.timeline=0;return p===!0&&!vr&&(kt=ft(o),re.killTweensOf(b),kt=0),rt(x,ft(o),r),n.reversed&&o.reverse(),n.paused&&o.paused(!0),(u||!l&&!g&&o._start===ue(x._time)&&ke(u)&&rf(ft(o))&&x.data!=="nested")&&(o._tTime=-te,o.render(Math.max(0,-h)||0)),m&&sl(ft(o),m),o}var t=e.prototype;return t.render=function(n,r,a){var o=this._time,c=this._tDur,l=this._dur,h=n<0,u=n>c-te&&!h?c:n<te?0:n,f,p,g,d,m,_,x,b,T;if(!l)of(this,n,r,a);else if(u!==this._tTime||!n||a||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==h||this._lazy){if(f=u,b=this.timeline,this._repeat){if(d=l+this._rDelay,this._repeat<-1&&h)return this.totalTime(d*100+n,r,a);if(f=ue(u%d),u===c?(g=this._repeat,f=l):(m=ue(u/d),g=~~m,g&&g===m?(f=l,g--):f>l&&(f=l)),_=this._yoyo&&g&1,_&&(T=this._yEase,f=l-f),m=ki(this._tTime,d),f===o&&!a&&this._initted&&g===m)return this._tTime=u,this;g!==m&&(b&&this._yEase&&vl(b,_),this.vars.repeatRefresh&&!_&&!this._lock&&f!==d&&this._initted&&(this._lock=a=1,this.render(ue(d*g),!0).invalidate()._lock=0))}if(!this._initted){if(rl(this,h?n:f,a,r,u))return this._tTime=0,this;if(o!==this._time&&!(a&&this.vars.repeatRefresh&&g!==m))return this;if(l!==this._dur)return this.render(n,r,a)}if(this._tTime=u,this._time=f,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=x=(T||this._ease)(f/l),this._from&&(this.ratio=x=1-x),!o&&u&&!r&&!m&&(Ve(this,"onStart"),this._tTime!==u))return this;for(p=this._pt;p;)p.r(x,p.d),p=p._next;b&&b.render(n<0?n:b._dur*b._ease(f/this._dur),r,a)||this._startAt&&(this._zTime=n),this._onUpdate&&!r&&(h&&Ks(this,n,r,a),Ve(this,"onUpdate")),this._repeat&&g!==m&&this.vars.onRepeat&&!r&&this.parent&&Ve(this,"onRepeat"),(u===this._tDur||!u)&&this._tTime===u&&(h&&!this._onUpdate&&Ks(this,n,!0,!0),(n||!l)&&(u===this._tDur&&this._ts>0||!u&&this._ts<0)&&Dt(this,1),!r&&!(h&&!o)&&(u||o||_)&&(Ve(this,u===c?"onComplete":"onReverseComplete",!0),this._prom&&!(u<c&&this.timeScale()>0)&&this._prom()))}return this},t.targets=function(){return this._targets},t.invalidate=function(n){return(!n||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(n),s.prototype.invalidate.call(this,n)},t.resetTo=function(n,r,a,o,c){ln||$e.wake(),this._ts||this.play();var l=Math.min(this._dur,(this._dp._time-this._start)*this._ts),h;return this._initted||Sr(this,l),h=this._ease(l/this._dur),kf(this,n,r,a,o,h,l,c)?this.resetTo(n,r,a,o,1):(as(this,0),this.parent||il(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},t.kill=function(n,r){if(r===void 0&&(r="all"),!n&&(!r||r==="all"))return this._lazy=this._pt=0,this.parent?Gi(this):this.scrollTrigger&&this.scrollTrigger.kill(!!ye),this;if(this.timeline){var a=this.timeline.totalDuration();return this.timeline.killTweensOf(n,r,kt&&kt.vars.overwrite!==!0)._first||Gi(this),this.parent&&a!==this.timeline.totalDuration()&&Pi(this,this._dur*this.timeline._tDur/a,0,1),this}var o=this._targets,c=n?We(n):o,l=this._ptLookup,h=this._pt,u,f,p,g,d,m,_;if((!r||r==="all")&&nf(o,c))return r==="all"&&(this._pt=0),Gi(this);for(u=this._op=this._op||[],r!=="all"&&(_e(r)&&(d={},Pe(r,function(x){return d[x]=1}),r=d),r=Pf(o,r)),_=o.length;_--;)if(~c.indexOf(o[_])){f=l[_],r==="all"?(u[_]=r,g=f,p={}):(p=u[_]=u[_]||{},g=r);for(d in g)m=f&&f[d],m&&((!("kill"in m.d)||m.d.kill(d)===!0)&&ss(this,m,"_pt"),delete f[d]),p!=="all"&&(p[d]=1)}return this._initted&&!this._pt&&h&&Gi(this),this},e.to=function(n,r){return new e(n,r,arguments[2])},e.from=function(n,r){return Ki(1,arguments)},e.delayedCall=function(n,r,a,o){return new e(r,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:n,onComplete:r,onReverseComplete:r,onCompleteParams:a,onReverseCompleteParams:a,callbackScope:o})},e.fromTo=function(n,r,a){return Ki(2,arguments)},e.set=function(n,r){return r.duration=0,r.repeatDelay||(r.repeat=0),new e(n,r)},e.killTweensOf=function(n,r,a){return re.killTweensOf(n,r,a)},e}(cn);Ue(he.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0});Pe("staggerTo,staggerFrom,staggerFromTo",function(s){he[s]=function(){var e=new Ce,t=Zs.call(arguments,0);return t.splice(s==="staggerFromTo"?5:4,0,0),e[s].apply(e,t)}});var Er=function(e,t,i){return e[t]=i},Tl=function(e,t,i){return e[t](i)},Sf=function(e,t,i,n){return e[t](n.fp,i)},Ef=function(e,t,i){return e.setAttribute(t,i)},Rr=function(e,t){return oe(e[t])?Tl:xr(e[t])&&e.setAttribute?Ef:Er},kl=function(e,t){return t.set(t.t,t.p,Math.round((t.s+t.c*e)*1e6)/1e6,t)},Rf=function(e,t){return t.set(t.t,t.p,!!(t.s+t.c*e),t)},Pl=function(e,t){var i=t._pt,n="";if(!e&&t.b)n=t.b;else if(e===1&&t.e)n=t.e;else{for(;i;)n=i.p+(i.m?i.m(i.s+i.c*e):Math.round((i.s+i.c*e)*1e4)/1e4)+n,i=i._next;n+=t.c}t.set(t.t,t.p,n,t)},Ir=function(e,t){for(var i=t._pt;i;)i.r(e,i.d),i=i._next},If=function(e,t,i,n){for(var r=this._pt,a;r;)a=r._next,r.p===n&&r.modifier(e,t,i),r=a},Df=function(e){for(var t=this._pt,i,n;t;)n=t._next,t.p===e&&!t.op||t.op===e?ss(this,t,"_pt"):t.dep||(i=1),t=n;return!i},Of=function(e,t,i,n){n.mSet(e,t,n.m.call(n.tween,i,n.mt),n)},Ml=function(e){for(var t=e._pt,i,n,r,a;t;){for(i=t._next,n=r;n&&n.pr>t.pr;)n=n._next;(t._prev=n?n._prev:a)?t._prev._next=t:r=t,(t._next=n)?n._prev=t:a=t,t=i}e._pt=r},Me=function(){function s(t,i,n,r,a,o,c,l,h){this.t=i,this.s=r,this.c=a,this.p=n,this.r=o||kl,this.d=c||this,this.set=l||Er,this.pr=h||0,this._next=t,t&&(t._prev=this)}var e=s.prototype;return e.modifier=function(i,n,r){this.mSet=this.mSet||this.set,this.set=Of,this.m=i,this.mt=r,this.tween=n},s}();Pe(Tr+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(s){return Cr[s]=1});Le.TweenMax=Le.TweenLite=he;Le.TimelineLite=Le.TimelineMax=Ce;re=new Ce({sortChildren:!1,defaults:Ci,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0});Fe.stringFilter=gl;var Kt=[],Fn={},$f=[],ga=0,Vf=0,Ms=function(e){return(Fn[e]||$f).map(function(t){return t()})},nr=function(){var e=Date.now(),t=[];e-ga>2&&(Ms("matchMediaInit"),Kt.forEach(function(i){var n=i.queries,r=i.conditions,a,o,c,l;for(o in n)a=st.matchMedia(n[o]).matches,a&&(c=1),a!==r[o]&&(r[o]=a,l=1);l&&(i.revert(),c&&t.push(i))}),Ms("matchMediaRevert"),t.forEach(function(i){return i.onMatch(i,function(n){return i.add(null,n)})}),ga=e,Ms("matchMedia"))},Sl=function(){function s(t,i){this.selector=i&&er(i),this.data=[],this._r=[],this.isReverted=!1,this.id=Vf++,t&&this.add(t)}var e=s.prototype;return e.add=function(i,n,r){oe(i)&&(r=n,n=i,i=oe);var a=this,o=function(){var l=ne,h=a.selector,u;return l&&l!==a&&l.data.push(a),r&&(a.selector=er(r)),ne=a,u=n.apply(a,arguments),oe(u)&&a._r.push(u),ne=l,a.selector=h,a.isReverted=!1,u};return a.last=o,i===oe?o(a,function(c){return a.add(null,c)}):i?a[i]=o:o},e.ignore=function(i){var n=ne;ne=null,i(this),ne=n},e.getTweens=function(){var i=[];return this.data.forEach(function(n){return n instanceof s?i.push.apply(i,n.getTweens()):n instanceof he&&!(n.parent&&n.parent.data==="nested")&&i.push(n)}),i},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(i,n){var r=this;if(i?function(){for(var o=r.getTweens(),c=r.data.length,l;c--;)l=r.data[c],l.data==="isFlip"&&(l.revert(),l.getChildren(!0,!0,!1).forEach(function(h){return o.splice(o.indexOf(h),1)}));for(o.map(function(h){return{g:h._dur||h._delay||h._sat&&!h._sat.vars.immediateRender?h.globalTime(0):-1/0,t:h}}).sort(function(h,u){return u.g-h.g||-1/0}).forEach(function(h){return h.t.revert(i)}),c=r.data.length;c--;)l=r.data[c],l instanceof Ce?l.data!=="nested"&&(l.scrollTrigger&&l.scrollTrigger.revert(),l.kill()):!(l instanceof he)&&l.revert&&l.revert(i);r._r.forEach(function(h){return h(i,r)}),r.isReverted=!0}():this.data.forEach(function(o){return o.kill&&o.kill()}),this.clear(),n)for(var a=Kt.length;a--;)Kt[a].id===this.id&&Kt.splice(a,1)},e.revert=function(i){this.kill(i||{})},s}(),Ff=function(){function s(t){this.contexts=[],this.scope=t,ne&&ne.data.push(this)}var e=s.prototype;return e.add=function(i,n,r){ct(i)||(i={matches:i});var a=new Sl(0,r||this.scope),o=a.conditions={},c,l,h;ne&&!a.selector&&(a.selector=ne.selector),this.contexts.push(a),n=a.add("onMatch",n),a.queries=i;for(l in i)l==="all"?h=1:(c=st.matchMedia(i[l]),c&&(Kt.indexOf(a)<0&&Kt.push(a),(o[l]=c.matches)&&(h=1),c.addListener?c.addListener(nr):c.addEventListener("change",nr)));return h&&n(a,function(u){return a.add(null,u)}),this},e.revert=function(i){this.kill(i||{})},e.kill=function(i){this.contexts.forEach(function(n){return n.kill(i,!0)})},s}(),Wn={registerPlugin:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];t.forEach(function(n){return ml(n)})},timeline:function(e){return new Ce(e)},getTweensOf:function(e,t){return re.getTweensOf(e,t)},getProperty:function(e,t,i,n){_e(e)&&(e=We(e)[0]);var r=Ht(e||{}).get,a=i?tl:el;return i==="native"&&(i=""),e&&(t?a((Oe[t]&&Oe[t].get||r)(e,t,i,n)):function(o,c,l){return a((Oe[o]&&Oe[o].get||r)(e,o,c,l))})},quickSetter:function(e,t,i){if(e=We(e),e.length>1){var n=e.map(function(h){return Ee.quickSetter(h,t,i)}),r=n.length;return function(h){for(var u=r;u--;)n[u](h)}}e=e[0]||{};var a=Oe[t],o=Ht(e),c=o.harness&&(o.harness.aliases||{})[t]||t,l=a?function(h){var u=new a;pi._pt=0,u.init(e,i?h+i:h,pi,0,[e]),u.render(1,u),pi._pt&&Ir(1,pi)}:o.set(e,c);return a?l:function(h){return l(e,c,i?h+i:h,o,1)}},quickTo:function(e,t,i){var n,r=Ee.to(e,Ue((n={},n[t]="+=0.1",n.paused=!0,n.stagger=0,n),i||{})),a=function(c,l,h){return r.resetTo(t,c,l,h)};return a.tween=r,a},isTweening:function(e){return re.getTweensOf(e,!0).length>0},defaults:function(e){return e&&e.ease&&(e.ease=Qt(e.ease,Ci.ease)),da(Ci,e||{})},config:function(e){return da(Fe,e||{})},registerEffect:function(e){var t=e.name,i=e.effect,n=e.plugins,r=e.defaults,a=e.extendTimeline;(n||"").split(",").forEach(function(o){return o&&!Oe[o]&&!Le[o]&&rn(t+" effect requires "+o+" plugin.")}),Cs[t]=function(o,c,l){return i(We(o),Ue(c||{},r),l)},a&&(Ce.prototype[t]=function(o,c,l){return this.add(Cs[t](o,ct(c)?c:(l=c)&&{},this),l)})},registerEase:function(e,t){H[e]=Qt(t)},parseEase:function(e,t){return arguments.length?Qt(e,t):H},getById:function(e){return re.getById(e)},exportRoot:function(e,t){e===void 0&&(e={});var i=new Ce(e),n,r;for(i.smoothChildTiming=ke(e.smoothChildTiming),re.remove(i),i._dp=0,i._time=i._tTime=re._time,n=re._first;n;)r=n._next,(t||!(!n._dur&&n instanceof he&&n.vars.onComplete===n._targets[0]))&&rt(i,n,n._start-n._delay),n=r;return rt(re,i,0),i},context:function(e,t){return e?new Sl(e,t):ne},matchMedia:function(e){return new Ff(e)},matchMediaRefresh:function(){return Kt.forEach(function(e){var t=e.conditions,i,n;for(n in t)t[n]&&(t[n]=!1,i=1);i&&e.revert()})||nr()},addEventListener:function(e,t){var i=Fn[e]||(Fn[e]=[]);~i.indexOf(t)||i.push(t)},removeEventListener:function(e,t){var i=Fn[e],n=i&&i.indexOf(t);n>=0&&i.splice(n,1)},utils:{wrap:pf,wrapYoyo:_f,distribute:ll,random:hl,snap:cl,normalize:mf,getUnit:xe,clamp:hf,splitColor:pl,toArray:We,selector:er,mapRange:dl,pipe:df,unitize:ff,interpolate:gf,shuffle:ol},install:Wo,effects:Cs,ticker:$e,updateRoot:Ce.updateRoot,plugins:Oe,globalTimeline:re,core:{PropTween:Me,globals:Qo,Tween:he,Timeline:Ce,Animation:cn,getCache:Ht,_removeLinkedListItem:ss,reverting:function(){return ye},context:function(e){return e&&ne&&(ne.data.push(e),e._ctx=ne),ne},suppressOverwrites:function(e){return vr=e}}};Pe("to,from,fromTo,delayedCall,set,killTweensOf",function(s){return Wn[s]=he[s]});$e.add(Ce.updateRoot);pi=Wn.to({},{duration:0});var Lf=function(e,t){for(var i=e._pt;i&&i.p!==t&&i.op!==t&&i.fp!==t;)i=i._next;return i},Uf=function(e,t){var i=e._targets,n,r,a;for(n in t)for(r=i.length;r--;)a=e._ptLookup[r][n],a&&(a=a.d)&&(a._pt&&(a=Lf(a,n)),a&&a.modifier&&a.modifier(t[n],e,i[r],n))},Ss=function(e,t){return{name:e,headless:1,rawVars:1,init:function(n,r,a){a._onInit=function(o){var c,l;if(_e(r)&&(c={},Pe(r,function(h){return c[h]=1}),r=c),t){c={};for(l in r)c[l]=t(r[l]);r=c}Uf(o,r)}}}},Ee=Wn.registerPlugin({name:"attr",init:function(e,t,i,n,r){var a,o,c;this.tween=i;for(a in t)c=e.getAttribute(a)||"",o=this.add(e,"setAttribute",(c||0)+"",t[a],n,r,0,0,a),o.op=a,o.b=c,this._props.push(a)},render:function(e,t){for(var i=t._pt;i;)ye?i.set(i.t,i.p,i.b,i):i.r(e,i.d),i=i._next}},{name:"endArray",headless:1,init:function(e,t){for(var i=t.length;i--;)this.add(e,i,e[i]||0,t[i],0,0,0,0,0,1)}},Ss("roundProps",tr),Ss("modifiers"),Ss("snap",cl))||Wn;he.version=Ce.version=Ee.version="3.13.0";Ho=1;br()&&Mi();H.Power0;H.Power1;H.Power2;H.Power3;H.Power4;H.Linear;H.Quad;H.Cubic;H.Quart;H.Quint;H.Strong;H.Elastic;H.Back;H.SteppedEase;H.Bounce;H.Sine;H.Expo;H.Circ;/*!
 * CSSPlugin 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var ya,Pt,vi,Dr,Xt,va,Or,Bf=function(){return typeof window<"u"},wt={},zt=180/Math.PI,xi=Math.PI/180,ai=Math.atan2,xa=1e8,$r=/([A-Z])/g,Nf=/(left|right|width|margin|padding|x)/i,qf=/[\s,\(]\S/,lt={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},sr=function(e,t){return t.set(t.t,t.p,Math.round((t.s+t.c*e)*1e4)/1e4+t.u,t)},zf=function(e,t){return t.set(t.t,t.p,e===1?t.e:Math.round((t.s+t.c*e)*1e4)/1e4+t.u,t)},Gf=function(e,t){return t.set(t.t,t.p,e?Math.round((t.s+t.c*e)*1e4)/1e4+t.u:t.b,t)},Yf=function(e,t){var i=t.s+t.c*e;t.set(t.t,t.p,~~(i+(i<0?-.5:.5))+t.u,t)},El=function(e,t){return t.set(t.t,t.p,e?t.e:t.b,t)},Rl=function(e,t){return t.set(t.t,t.p,e!==1?t.b:t.e,t)},Xf=function(e,t,i){return e.style[t]=i},jf=function(e,t,i){return e.style.setProperty(t,i)},Hf=function(e,t,i){return e._gsap[t]=i},Wf=function(e,t,i){return e._gsap.scaleX=e._gsap.scaleY=i},Qf=function(e,t,i,n,r){var a=e._gsap;a.scaleX=a.scaleY=i,a.renderTransform(r,a)},Kf=function(e,t,i,n,r){var a=e._gsap;a[t]=i,a.renderTransform(r,a)},ae="transform",Se=ae+"Origin",Jf=function s(e,t){var i=this,n=this.target,r=n.style,a=n._gsap;if(e in wt&&r){if(this.tfm=this.tfm||{},e!=="transform")e=lt[e]||e,~e.indexOf(",")?e.split(",").forEach(function(o){return i.tfm[o]=mt(n,o)}):this.tfm[e]=a.x?a[e]:mt(n,e),e===Se&&(this.tfm.zOrigin=a.zOrigin);else return lt.transform.split(",").forEach(function(o){return s.call(i,o,t)});if(this.props.indexOf(ae)>=0)return;a.svg&&(this.svgo=n.getAttribute("data-svg-origin"),this.props.push(Se,t,"")),e=ae}(r||t)&&this.props.push(e,t,r[e])},Il=function(e){e.translate&&(e.removeProperty("translate"),e.removeProperty("scale"),e.removeProperty("rotate"))},Zf=function(){var e=this.props,t=this.target,i=t.style,n=t._gsap,r,a;for(r=0;r<e.length;r+=3)e[r+1]?e[r+1]===2?t[e[r]](e[r+2]):t[e[r]]=e[r+2]:e[r+2]?i[e[r]]=e[r+2]:i.removeProperty(e[r].substr(0,2)==="--"?e[r]:e[r].replace($r,"-$1").toLowerCase());if(this.tfm){for(a in this.tfm)n[a]=this.tfm[a];n.svg&&(n.renderTransform(),t.setAttribute("data-svg-origin",this.svgo||"")),r=Or(),(!r||!r.isStart)&&!i[ae]&&(Il(i),n.zOrigin&&i[Se]&&(i[Se]+=" "+n.zOrigin+"px",n.zOrigin=0,n.renderTransform()),n.uncache=1)}},Dl=function(e,t){var i={target:e,props:[],revert:Zf,save:Jf};return e._gsap||Ee.core.getCache(e),t&&e.style&&e.nodeType&&t.split(",").forEach(function(n){return i.save(n)}),i},Ol,rr=function(e,t){var i=Pt.createElementNS?Pt.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):Pt.createElement(e);return i&&i.style?i:Pt.createElement(e)},Qe=function s(e,t,i){var n=getComputedStyle(e);return n[t]||n.getPropertyValue(t.replace($r,"-$1").toLowerCase())||n.getPropertyValue(t)||!i&&s(e,Si(t)||t,1)||""},ba="O,Moz,ms,Ms,Webkit".split(","),Si=function(e,t,i){var n=t||Xt,r=n.style,a=5;if(e in r&&!i)return e;for(e=e.charAt(0).toUpperCase()+e.substr(1);a--&&!(ba[a]+e in r););return a<0?null:(a===3?"ms":a>=0?ba[a]:"")+e},ar=function(){Bf()&&window.document&&(ya=window,Pt=ya.document,vi=Pt.documentElement,Xt=rr("div")||{style:{}},rr("div"),ae=Si(ae),Se=ae+"Origin",Xt.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",Ol=!!Si("perspective"),Or=Ee.core.reverting,Dr=1)},wa=function(e){var t=e.ownerSVGElement,i=rr("svg",t&&t.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),n=e.cloneNode(!0),r;n.style.display="block",i.appendChild(n),vi.appendChild(i);try{r=n.getBBox()}catch{}return i.removeChild(n),vi.removeChild(i),r},Aa=function(e,t){for(var i=t.length;i--;)if(e.hasAttribute(t[i]))return e.getAttribute(t[i])},$l=function(e){var t,i;try{t=e.getBBox()}catch{t=wa(e),i=1}return t&&(t.width||t.height)||i||(t=wa(e)),t&&!t.width&&!t.x&&!t.y?{x:+Aa(e,["x","cx","x1"])||0,y:+Aa(e,["y","cy","y1"])||0,width:0,height:0}:t},Vl=function(e){return!!(e.getCTM&&(!e.parentNode||e.ownerSVGElement)&&$l(e))},ei=function(e,t){if(t){var i=e.style,n;t in wt&&t!==Se&&(t=ae),i.removeProperty?(n=t.substr(0,2),(n==="ms"||t.substr(0,6)==="webkit")&&(t="-"+t),i.removeProperty(n==="--"?t:t.replace($r,"-$1").toLowerCase())):i.removeAttribute(t)}},Mt=function(e,t,i,n,r,a){var o=new Me(e._pt,t,i,0,1,a?Rl:El);return e._pt=o,o.b=n,o.e=r,e._props.push(i),o},Ca={deg:1,rad:1,turn:1},em={grid:1,flex:1},Ot=function s(e,t,i,n){var r=parseFloat(i)||0,a=(i+"").trim().substr((r+"").length)||"px",o=Xt.style,c=Nf.test(t),l=e.tagName.toLowerCase()==="svg",h=(l?"client":"offset")+(c?"Width":"Height"),u=100,f=n==="px",p=n==="%",g,d,m,_;if(n===a||!r||Ca[n]||Ca[a])return r;if(a!=="px"&&!f&&(r=s(e,t,i,"px")),_=e.getCTM&&Vl(e),(p||a==="%")&&(wt[t]||~t.indexOf("adius")))return g=_?e.getBBox()[c?"width":"height"]:e[h],le(p?r/g*u:r/100*g);if(o[c?"width":"height"]=u+(f?a:n),d=n!=="rem"&&~t.indexOf("adius")||n==="em"&&e.appendChild&&!l?e:e.parentNode,_&&(d=(e.ownerSVGElement||{}).parentNode),(!d||d===Pt||!d.appendChild)&&(d=Pt.body),m=d._gsap,m&&p&&m.width&&c&&m.time===$e.time&&!m.uncache)return le(r/m.width*u);if(p&&(t==="height"||t==="width")){var x=e.style[t];e.style[t]=u+n,g=e[h],x?e.style[t]=x:ei(e,t)}else(p||a==="%")&&!em[Qe(d,"display")]&&(o.position=Qe(e,"position")),d===e&&(o.position="static"),d.appendChild(Xt),g=Xt[h],d.removeChild(Xt),o.position="absolute";return c&&p&&(m=Ht(d),m.time=$e.time,m.width=d[h]),le(f?g*r/u:g&&r?u/g*r:0)},mt=function(e,t,i,n){var r;return Dr||ar(),t in lt&&t!=="transform"&&(t=lt[t],~t.indexOf(",")&&(t=t.split(",")[0])),wt[t]&&t!=="transform"?(r=un(e,n),r=t!=="transformOrigin"?r[t]:r.svg?r.origin:Kn(Qe(e,Se))+" "+r.zOrigin+"px"):(r=e.style[t],(!r||r==="auto"||n||~(r+"").indexOf("calc("))&&(r=Qn[t]&&Qn[t](e,t,i)||Qe(e,t)||Jo(e,t)||(t==="opacity"?1:0))),i&&!~(r+"").trim().indexOf(" ")?Ot(e,t,r,i)+i:r},tm=function(e,t,i,n){if(!i||i==="none"){var r=Si(t,e,1),a=r&&Qe(e,r,1);a&&a!==i?(t=r,i=a):t==="borderColor"&&(i=Qe(e,"borderTopColor"))}var o=new Me(this._pt,e.style,t,0,1,Pl),c=0,l=0,h,u,f,p,g,d,m,_,x,b,T,k;if(o.b=i,o.e=n,i+="",n+="",n.substring(0,6)==="var(--"&&(n=Qe(e,n.substring(4,n.indexOf(")")))),n==="auto"&&(d=e.style[t],e.style[t]=n,n=Qe(e,t)||n,d?e.style[t]=d:ei(e,t)),h=[i,n],gl(h),i=h[0],n=h[1],f=i.match(mi)||[],k=n.match(mi)||[],k.length){for(;u=mi.exec(n);)m=u[0],x=n.substring(c,u.index),g?g=(g+1)%5:(x.substr(-5)==="rgba("||x.substr(-5)==="hsla(")&&(g=1),m!==(d=f[l++]||"")&&(p=parseFloat(d)||0,T=d.substr((p+"").length),m.charAt(1)==="="&&(m=yi(p,m)+T),_=parseFloat(m),b=m.substr((_+"").length),c=mi.lastIndex-b.length,b||(b=b||Fe.units[t]||T,c===n.length&&(n+=b,o.e+=b)),T!==b&&(p=Ot(e,t,d,b)||0),o._pt={_next:o._pt,p:x||l===1?x:",",s:p,c:_-p,m:g&&g<4||t==="zIndex"?Math.round:0});o.c=c<n.length?n.substring(c,n.length):""}else o.r=t==="display"&&n==="none"?Rl:El;return Xo.test(n)&&(o.e=0),this._pt=o,o},Ta={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},im=function(e){var t=e.split(" "),i=t[0],n=t[1]||"50%";return(i==="top"||i==="bottom"||n==="left"||n==="right")&&(e=i,i=n,n=e),t[0]=Ta[i]||i,t[1]=Ta[n]||n,t.join(" ")},nm=function(e,t){if(t.tween&&t.tween._time===t.tween._dur){var i=t.t,n=i.style,r=t.u,a=i._gsap,o,c,l;if(r==="all"||r===!0)n.cssText="",c=1;else for(r=r.split(","),l=r.length;--l>-1;)o=r[l],wt[o]&&(c=1,o=o==="transformOrigin"?Se:ae),ei(i,o);c&&(ei(i,ae),a&&(a.svg&&i.removeAttribute("transform"),n.scale=n.rotate=n.translate="none",un(i,1),a.uncache=1,Il(n)))}},Qn={clearProps:function(e,t,i,n,r){if(r.data!=="isFromStart"){var a=e._pt=new Me(e._pt,t,i,0,0,nm);return a.u=n,a.pr=-10,a.tween=r,e._props.push(i),1}}},hn=[1,0,0,1,0,0],Fl={},Ll=function(e){return e==="matrix(1, 0, 0, 1, 0, 0)"||e==="none"||!e},ka=function(e){var t=Qe(e,ae);return Ll(t)?hn:t.substr(7).match(Yo).map(le)},Vr=function(e,t){var i=e._gsap||Ht(e),n=e.style,r=ka(e),a,o,c,l;return i.svg&&e.getAttribute("transform")?(c=e.transform.baseVal.consolidate().matrix,r=[c.a,c.b,c.c,c.d,c.e,c.f],r.join(",")==="1,0,0,1,0,0"?hn:r):(r===hn&&!e.offsetParent&&e!==vi&&!i.svg&&(c=n.display,n.display="block",a=e.parentNode,(!a||!e.offsetParent&&!e.getBoundingClientRect().width)&&(l=1,o=e.nextElementSibling,vi.appendChild(e)),r=ka(e),c?n.display=c:ei(e,"display"),l&&(o?a.insertBefore(e,o):a?a.appendChild(e):vi.removeChild(e))),t&&r.length>6?[r[0],r[1],r[4],r[5],r[12],r[13]]:r)},or=function(e,t,i,n,r,a){var o=e._gsap,c=r||Vr(e,!0),l=o.xOrigin||0,h=o.yOrigin||0,u=o.xOffset||0,f=o.yOffset||0,p=c[0],g=c[1],d=c[2],m=c[3],_=c[4],x=c[5],b=t.split(" "),T=parseFloat(b[0])||0,k=parseFloat(b[1])||0,C,y,v,A;i?c!==hn&&(y=p*m-g*d)&&(v=T*(m/y)+k*(-d/y)+(d*x-m*_)/y,A=T*(-g/y)+k*(p/y)-(p*x-g*_)/y,T=v,k=A):(C=$l(e),T=C.x+(~b[0].indexOf("%")?T/100*C.width:T),k=C.y+(~(b[1]||b[0]).indexOf("%")?k/100*C.height:k)),n||n!==!1&&o.smooth?(_=T-l,x=k-h,o.xOffset=u+(_*p+x*d)-_,o.yOffset=f+(_*g+x*m)-x):o.xOffset=o.yOffset=0,o.xOrigin=T,o.yOrigin=k,o.smooth=!!n,o.origin=t,o.originIsAbsolute=!!i,e.style[Se]="0px 0px",a&&(Mt(a,o,"xOrigin",l,T),Mt(a,o,"yOrigin",h,k),Mt(a,o,"xOffset",u,o.xOffset),Mt(a,o,"yOffset",f,o.yOffset)),e.setAttribute("data-svg-origin",T+" "+k)},un=function(e,t){var i=e._gsap||new bl(e);if("x"in i&&!t&&!i.uncache)return i;var n=e.style,r=i.scaleX<0,a="px",o="deg",c=getComputedStyle(e),l=Qe(e,Se)||"0",h,u,f,p,g,d,m,_,x,b,T,k,C,y,v,A,E,S,R,V,X,q,B,G,$,Y,L,ce,Ne,F,ge,qe;return h=u=f=d=m=_=x=b=T=0,p=g=1,i.svg=!!(e.getCTM&&Vl(e)),c.translate&&((c.translate!=="none"||c.scale!=="none"||c.rotate!=="none")&&(n[ae]=(c.translate!=="none"?"translate3d("+(c.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+(c.rotate!=="none"?"rotate("+c.rotate+") ":"")+(c.scale!=="none"?"scale("+c.scale.split(" ").join(",")+") ":"")+(c[ae]!=="none"?c[ae]:"")),n.scale=n.rotate=n.translate="none"),y=Vr(e,i.svg),i.svg&&(i.uncache?($=e.getBBox(),l=i.xOrigin-$.x+"px "+(i.yOrigin-$.y)+"px",G=""):G=!t&&e.getAttribute("data-svg-origin"),or(e,G||l,!!G||i.originIsAbsolute,i.smooth!==!1,y)),k=i.xOrigin||0,C=i.yOrigin||0,y!==hn&&(S=y[0],R=y[1],V=y[2],X=y[3],h=q=y[4],u=B=y[5],y.length===6?(p=Math.sqrt(S*S+R*R),g=Math.sqrt(X*X+V*V),d=S||R?ai(R,S)*zt:0,x=V||X?ai(V,X)*zt+d:0,x&&(g*=Math.abs(Math.cos(x*xi))),i.svg&&(h-=k-(k*S+C*V),u-=C-(k*R+C*X))):(qe=y[6],F=y[7],L=y[8],ce=y[9],Ne=y[10],ge=y[11],h=y[12],u=y[13],f=y[14],v=ai(qe,Ne),m=v*zt,v&&(A=Math.cos(-v),E=Math.sin(-v),G=q*A+L*E,$=B*A+ce*E,Y=qe*A+Ne*E,L=q*-E+L*A,ce=B*-E+ce*A,Ne=qe*-E+Ne*A,ge=F*-E+ge*A,q=G,B=$,qe=Y),v=ai(-V,Ne),_=v*zt,v&&(A=Math.cos(-v),E=Math.sin(-v),G=S*A-L*E,$=R*A-ce*E,Y=V*A-Ne*E,ge=X*E+ge*A,S=G,R=$,V=Y),v=ai(R,S),d=v*zt,v&&(A=Math.cos(v),E=Math.sin(v),G=S*A+R*E,$=q*A+B*E,R=R*A-S*E,B=B*A-q*E,S=G,q=$),m&&Math.abs(m)+Math.abs(d)>359.9&&(m=d=0,_=180-_),p=le(Math.sqrt(S*S+R*R+V*V)),g=le(Math.sqrt(B*B+qe*qe)),v=ai(q,B),x=Math.abs(v)>2e-4?v*zt:0,T=ge?1/(ge<0?-ge:ge):0),i.svg&&(G=e.getAttribute("transform"),i.forceCSS=e.setAttribute("transform","")||!Ll(Qe(e,ae)),G&&e.setAttribute("transform",G))),Math.abs(x)>90&&Math.abs(x)<270&&(r?(p*=-1,x+=d<=0?180:-180,d+=d<=0?180:-180):(g*=-1,x+=x<=0?180:-180)),t=t||i.uncache,i.x=h-((i.xPercent=h&&(!t&&i.xPercent||(Math.round(e.offsetWidth/2)===Math.round(-h)?-50:0)))?e.offsetWidth*i.xPercent/100:0)+a,i.y=u-((i.yPercent=u&&(!t&&i.yPercent||(Math.round(e.offsetHeight/2)===Math.round(-u)?-50:0)))?e.offsetHeight*i.yPercent/100:0)+a,i.z=f+a,i.scaleX=le(p),i.scaleY=le(g),i.rotation=le(d)+o,i.rotationX=le(m)+o,i.rotationY=le(_)+o,i.skewX=x+o,i.skewY=b+o,i.transformPerspective=T+a,(i.zOrigin=parseFloat(l.split(" ")[2])||!t&&i.zOrigin||0)&&(n[Se]=Kn(l)),i.xOffset=i.yOffset=0,i.force3D=Fe.force3D,i.renderTransform=i.svg?rm:Ol?Ul:sm,i.uncache=0,i},Kn=function(e){return(e=e.split(" "))[0]+" "+e[1]},Es=function(e,t,i){var n=xe(t);return le(parseFloat(t)+parseFloat(Ot(e,"x",i+"px",n)))+n},sm=function(e,t){t.z="0px",t.rotationY=t.rotationX="0deg",t.force3D=0,Ul(e,t)},Ft="0deg",Li="0px",Lt=") ",Ul=function(e,t){var i=t||this,n=i.xPercent,r=i.yPercent,a=i.x,o=i.y,c=i.z,l=i.rotation,h=i.rotationY,u=i.rotationX,f=i.skewX,p=i.skewY,g=i.scaleX,d=i.scaleY,m=i.transformPerspective,_=i.force3D,x=i.target,b=i.zOrigin,T="",k=_==="auto"&&e&&e!==1||_===!0;if(b&&(u!==Ft||h!==Ft)){var C=parseFloat(h)*xi,y=Math.sin(C),v=Math.cos(C),A;C=parseFloat(u)*xi,A=Math.cos(C),a=Es(x,a,y*A*-b),o=Es(x,o,-Math.sin(C)*-b),c=Es(x,c,v*A*-b+b)}m!==Li&&(T+="perspective("+m+Lt),(n||r)&&(T+="translate("+n+"%, "+r+"%) "),(k||a!==Li||o!==Li||c!==Li)&&(T+=c!==Li||k?"translate3d("+a+", "+o+", "+c+") ":"translate("+a+", "+o+Lt),l!==Ft&&(T+="rotate("+l+Lt),h!==Ft&&(T+="rotateY("+h+Lt),u!==Ft&&(T+="rotateX("+u+Lt),(f!==Ft||p!==Ft)&&(T+="skew("+f+", "+p+Lt),(g!==1||d!==1)&&(T+="scale("+g+", "+d+Lt),x.style[ae]=T||"translate(0, 0)"},rm=function(e,t){var i=t||this,n=i.xPercent,r=i.yPercent,a=i.x,o=i.y,c=i.rotation,l=i.skewX,h=i.skewY,u=i.scaleX,f=i.scaleY,p=i.target,g=i.xOrigin,d=i.yOrigin,m=i.xOffset,_=i.yOffset,x=i.forceCSS,b=parseFloat(a),T=parseFloat(o),k,C,y,v,A;c=parseFloat(c),l=parseFloat(l),h=parseFloat(h),h&&(h=parseFloat(h),l+=h,c+=h),c||l?(c*=xi,l*=xi,k=Math.cos(c)*u,C=Math.sin(c)*u,y=Math.sin(c-l)*-f,v=Math.cos(c-l)*f,l&&(h*=xi,A=Math.tan(l-h),A=Math.sqrt(1+A*A),y*=A,v*=A,h&&(A=Math.tan(h),A=Math.sqrt(1+A*A),k*=A,C*=A)),k=le(k),C=le(C),y=le(y),v=le(v)):(k=u,v=f,C=y=0),(b&&!~(a+"").indexOf("px")||T&&!~(o+"").indexOf("px"))&&(b=Ot(p,"x",a,"px"),T=Ot(p,"y",o,"px")),(g||d||m||_)&&(b=le(b+g-(g*k+d*y)+m),T=le(T+d-(g*C+d*v)+_)),(n||r)&&(A=p.getBBox(),b=le(b+n/100*A.width),T=le(T+r/100*A.height)),A="matrix("+k+","+C+","+y+","+v+","+b+","+T+")",p.setAttribute("transform",A),x&&(p.style[ae]=A)},am=function(e,t,i,n,r){var a=360,o=_e(r),c=parseFloat(r)*(o&&~r.indexOf("rad")?zt:1),l=c-n,h=n+l+"deg",u,f;return o&&(u=r.split("_")[1],u==="short"&&(l%=a,l!==l%(a/2)&&(l+=l<0?a:-a)),u==="cw"&&l<0?l=(l+a*xa)%a-~~(l/a)*a:u==="ccw"&&l>0&&(l=(l-a*xa)%a-~~(l/a)*a)),e._pt=f=new Me(e._pt,t,i,n,l,zf),f.e=h,f.u="deg",e._props.push(i),f},Pa=function(e,t){for(var i in t)e[i]=t[i];return e},om=function(e,t,i){var n=Pa({},i._gsap),r="perspective,force3D,transformOrigin,svgOrigin",a=i.style,o,c,l,h,u,f,p,g;n.svg?(l=i.getAttribute("transform"),i.setAttribute("transform",""),a[ae]=t,o=un(i,1),ei(i,ae),i.setAttribute("transform",l)):(l=getComputedStyle(i)[ae],a[ae]=t,o=un(i,1),a[ae]=l);for(c in wt)l=n[c],h=o[c],l!==h&&r.indexOf(c)<0&&(p=xe(l),g=xe(h),u=p!==g?Ot(i,c,l,g):parseFloat(l),f=parseFloat(h),e._pt=new Me(e._pt,o,c,u,f-u,sr),e._pt.u=g||0,e._props.push(c));Pa(o,n)};Pe("padding,margin,Width,Radius",function(s,e){var t="Top",i="Right",n="Bottom",r="Left",a=(e<3?[t,i,n,r]:[t+r,t+i,n+i,n+r]).map(function(o){return e<2?s+o:"border"+o+s});Qn[e>1?"border"+s:s]=function(o,c,l,h,u){var f,p;if(arguments.length<4)return f=a.map(function(g){return mt(o,g,l)}),p=f.join(" "),p.split(f[0]).length===5?f[0]:p;f=(h+"").split(" "),p={},a.forEach(function(g,d){return p[g]=f[d]=f[d]||f[(d-1)/2|0]}),o.init(c,p,u)}});var Bl={name:"css",register:ar,targetTest:function(e){return e.style&&e.nodeType},init:function(e,t,i,n,r){var a=this._props,o=e.style,c=i.vars.startAt,l,h,u,f,p,g,d,m,_,x,b,T,k,C,y,v;Dr||ar(),this.styles=this.styles||Dl(e),v=this.styles.props,this.tween=i;for(d in t)if(d!=="autoRound"&&(h=t[d],!(Oe[d]&&wl(d,t,i,n,e,r)))){if(p=typeof h,g=Qn[d],p==="function"&&(h=h.call(i,n,e,r),p=typeof h),p==="string"&&~h.indexOf("random(")&&(h=on(h)),g)g(this,e,d,h,i)&&(y=1);else if(d.substr(0,2)==="--")l=(getComputedStyle(e).getPropertyValue(d)+"").trim(),h+="",Rt.lastIndex=0,Rt.test(l)||(m=xe(l),_=xe(h)),_?m!==_&&(l=Ot(e,d,l,_)+_):m&&(h+=m),this.add(o,"setProperty",l,h,n,r,0,0,d),a.push(d),v.push(d,0,o[d]);else if(p!=="undefined"){if(c&&d in c?(l=typeof c[d]=="function"?c[d].call(i,n,e,r):c[d],_e(l)&&~l.indexOf("random(")&&(l=on(l)),xe(l+"")||l==="auto"||(l+=Fe.units[d]||xe(mt(e,d))||""),(l+"").charAt(1)==="="&&(l=mt(e,d))):l=mt(e,d),f=parseFloat(l),x=p==="string"&&h.charAt(1)==="="&&h.substr(0,2),x&&(h=h.substr(2)),u=parseFloat(h),d in lt&&(d==="autoAlpha"&&(f===1&&mt(e,"visibility")==="hidden"&&u&&(f=0),v.push("visibility",0,o.visibility),Mt(this,o,"visibility",f?"inherit":"hidden",u?"inherit":"hidden",!u)),d!=="scale"&&d!=="transform"&&(d=lt[d],~d.indexOf(",")&&(d=d.split(",")[0]))),b=d in wt,b){if(this.styles.save(d),p==="string"&&h.substring(0,6)==="var(--"&&(h=Qe(e,h.substring(4,h.indexOf(")"))),u=parseFloat(h)),T||(k=e._gsap,k.renderTransform&&!t.parseTransform||un(e,t.parseTransform),C=t.smoothOrigin!==!1&&k.smooth,T=this._pt=new Me(this._pt,o,ae,0,1,k.renderTransform,k,0,-1),T.dep=1),d==="scale")this._pt=new Me(this._pt,k,"scaleY",k.scaleY,(x?yi(k.scaleY,x+u):u)-k.scaleY||0,sr),this._pt.u=0,a.push("scaleY",d),d+="X";else if(d==="transformOrigin"){v.push(Se,0,o[Se]),h=im(h),k.svg?or(e,h,0,C,0,this):(_=parseFloat(h.split(" ")[2])||0,_!==k.zOrigin&&Mt(this,k,"zOrigin",k.zOrigin,_),Mt(this,o,d,Kn(l),Kn(h)));continue}else if(d==="svgOrigin"){or(e,h,1,C,0,this);continue}else if(d in Fl){am(this,k,d,f,x?yi(f,x+h):h);continue}else if(d==="smoothOrigin"){Mt(this,k,"smooth",k.smooth,h);continue}else if(d==="force3D"){k[d]=h;continue}else if(d==="transform"){om(this,h,e);continue}}else d in o||(d=Si(d)||d);if(b||(u||u===0)&&(f||f===0)&&!qf.test(h)&&d in o)m=(l+"").substr((f+"").length),u||(u=0),_=xe(h)||(d in Fe.units?Fe.units[d]:m),m!==_&&(f=Ot(e,d,l,_)),this._pt=new Me(this._pt,b?k:o,d,f,(x?yi(f,x+u):u)-f,!b&&(_==="px"||d==="zIndex")&&t.autoRound!==!1?Yf:sr),this._pt.u=_||0,m!==_&&_!=="%"&&(this._pt.b=l,this._pt.r=Gf);else if(d in o)tm.call(this,e,d,l,x?x+h:h);else if(d in e)this.add(e,d,l||e[d],x?x+h:h,n,r);else if(d!=="parseTransform"){Ar(d,h);continue}b||(d in o?v.push(d,0,o[d]):typeof e[d]=="function"?v.push(d,2,e[d]()):v.push(d,1,l||e[d])),a.push(d)}}y&&Ml(this)},render:function(e,t){if(t.tween._time||!Or())for(var i=t._pt;i;)i.r(e,i.d),i=i._next;else t.styles.revert()},get:mt,aliases:lt,getSetter:function(e,t,i){var n=lt[t];return n&&n.indexOf(",")<0&&(t=n),t in wt&&t!==Se&&(e._gsap.x||mt(e,"x"))?i&&va===i?t==="scale"?Wf:Hf:(va=i||{})&&(t==="scale"?Qf:Kf):e.style&&!xr(e.style[t])?Xf:~t.indexOf("-")?jf:Rr(e,t)},core:{_removeProperty:ei,_getMatrix:Vr}};Ee.utils.checkPrefix=Si;Ee.core.getStyleSaver=Dl;(function(s,e,t,i){var n=Pe(s+","+e+","+t,function(r){wt[r]=1});Pe(e,function(r){Fe.units[r]="deg",Fl[r]=1}),lt[n[13]]=s+","+e,Pe(i,function(r){var a=r.split(":");lt[a[1]]=n[a[0]]})})("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY");Pe("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(s){Fe.units[s]="px"});Ee.registerPlugin(Bl);var os=Ee.registerPlugin(Bl)||Ee;os.core.Tween;/*!
 * paths 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var lm=/[achlmqstvz]|(-?\d*\.?\d*(?:e[\-+]?\d+)?)[0-9]/ig,cm=/(?:(-)?\d*\.?\d*(?:e[\-+]?\d+)?)[0-9]/ig,hm=/[\+\-]?\d*\.?\d+e[\+\-]?\d+/ig,um=/(^[#\.][a-z]|[a-y][a-z])/i,dm=Math.PI/180,fm=180/Math.PI,Sn=Math.sin,En=Math.cos,Ke=Math.abs,pt=Math.sqrt,mm=Math.atan2,lr=1e8,Ma=function(e){return typeof e=="string"},Nl=function(e){return typeof e=="number"},pm=function(e){return typeof e>"u"},_m={},gm={},Jn=1e5,ql=function(e){return Math.round((e+lr)%1*Jn)/Jn||(e<0?0:1)},K=function(e){return Math.round(e*Jn)/Jn||0},Sa=function(e){return Math.round(e*1e10)/1e10||0},Ea=function(e,t,i,n){var r=e[t],a=n===1?6:cr(r,i,n);if((a||!n)&&a+i+2<r.length)return e.splice(t,0,r.slice(0,i+a+2)),r.splice(0,i+a),1},zl=function(e,t,i){var n=e.length,r=~~(i*n);if(e[r]>t){for(;--r&&e[r]>t;);r<0&&(r=0)}else for(;e[++r]<t&&r<n;);return r<n?r:n-1},ym=function(e,t){var i=e.length;for(e.reverse();i--;)e[i].reversed||bm(e[i])},Ra=function(e,t){return t.totalLength=e.totalLength,e.samples?(t.samples=e.samples.slice(0),t.lookup=e.lookup.slice(0),t.minLength=e.minLength,t.resolution=e.resolution):e.totalPoints&&(t.totalPoints=e.totalPoints),t},vm=function(e,t){var i=e.length,n=e[i-1]||[],r=n.length;i&&t[0]===n[r-2]&&t[1]===n[r-1]&&(t=n.concat(t.slice(2)),i--),e[i]=t};function Ln(s){s=Ma(s)&&um.test(s)&&document.querySelector(s)||s;var e=s.getAttribute?s:0,t;return e&&(s=s.getAttribute("d"))?(e._gsPath||(e._gsPath={}),t=e._gsPath[s],t&&!t._dirty?t:e._gsPath[s]=Zn(s)):s?Ma(s)?Zn(s):Nl(s[0])?[s]:s:console.warn("Expecting a <path> element or an SVG path data string")}function xm(s){for(var e=[],t=0;t<s.length;t++)e[t]=Ra(s[t],s[t].slice(0));return Ra(s,e)}function bm(s){var e=0,t;for(s.reverse();e<s.length;e+=2)t=s[e],s[e]=s[e+1],s[e+1]=t;s.reversed=!s.reversed}var wm=function(e,t){var i=document.createElementNS("http://www.w3.org/2000/svg","path"),n=[].slice.call(e.attributes),r=n.length,a;for(t=","+t+",";--r>-1;)a=n[r].nodeName.toLowerCase(),t.indexOf(","+a+",")<0&&i.setAttributeNS(null,a,n[r].nodeValue);return i},Am={rect:"rx,ry,x,y,width,height",circle:"r,cx,cy",ellipse:"rx,ry,cx,cy",line:"x1,x2,y1,y2"},Cm=function(e,t){for(var i=t?t.split(","):[],n={},r=i.length;--r>-1;)n[i[r]]=+e.getAttribute(i[r])||0;return n};function Tm(s,e){var t=s.tagName.toLowerCase(),i=.552284749831,n,r,a,o,c,l,h,u,f,p,g,d,m,_,x,b,T,k,C,y,v,A;return t==="path"||!s.getBBox?s:(l=wm(s,"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points"),A=Cm(s,Am[t]),t==="rect"?(o=A.rx,c=A.ry||o,r=A.x,a=A.y,p=A.width-o*2,g=A.height-c*2,o||c?(d=r+o*(1-i),m=r+o,_=m+p,x=_+o*i,b=_+o,T=a+c*(1-i),k=a+c,C=k+g,y=C+c*i,v=C+c,n="M"+b+","+k+" V"+C+" C"+[b,y,x,v,_,v,_-(_-m)/3,v,m+(_-m)/3,v,m,v,d,v,r,y,r,C,r,C-(C-k)/3,r,k+(C-k)/3,r,k,r,T,d,a,m,a,m+(_-m)/3,a,_-(_-m)/3,a,_,a,x,a,b,T,b,k].join(",")+"z"):n="M"+(r+p)+","+a+" v"+g+" h"+-p+" v"+-g+" h"+p+"z"):t==="circle"||t==="ellipse"?(t==="circle"?(o=c=A.r,u=o*i):(o=A.rx,c=A.ry,u=c*i),r=A.cx,a=A.cy,h=o*i,n="M"+(r+o)+","+a+" C"+[r+o,a+u,r+h,a+c,r,a+c,r-h,a+c,r-o,a+u,r-o,a,r-o,a-u,r-h,a-c,r,a-c,r+h,a-c,r+o,a-u,r+o,a].join(",")+"z"):t==="line"?n="M"+A.x1+","+A.y1+" L"+A.x2+","+A.y2:(t==="polyline"||t==="polygon")&&(f=(s.getAttribute("points")+"").match(cm)||[],r=f.shift(),a=f.shift(),n="M"+r+","+a+" L"+f.join(","),t==="polygon"&&(n+=","+r+","+a+"z")),l.setAttribute("d",Xl(l._gsRawPath=Zn(n))),e&&s.parentNode&&(s.parentNode.insertBefore(l,s),s.parentNode.removeChild(s)),l)}function Gl(s,e,t){var i=s[e],n=s[e+2],r=s[e+4],a;return i+=(n-i)*t,n+=(r-n)*t,i+=(n-i)*t,a=n+(r+(s[e+6]-r)*t-n)*t-i,i=s[e+1],n=s[e+3],r=s[e+5],i+=(n-i)*t,n+=(r-n)*t,i+=(n-i)*t,K(mm(n+(r+(s[e+7]-r)*t-n)*t-i,a)*fm)}function Yl(s,e,t){t=pm(t)?1:Sa(t)||0,e=Sa(e)||0;var i=Math.max(0,~~(Ke(t-e)-1e-8)),n=xm(s);if(e>t&&(e=1-e,t=1-t,ym(n),n.totalLength=0),e<0||t<0){var r=Math.abs(~~Math.min(e,t))+1;e+=r,t+=r}n.totalLength||Jt(n);var a=t>1,o=Ia(n,e,_m,!0),c=Ia(n,t,gm),l=c.segment,h=o.segment,u=c.segIndex,f=o.segIndex,p=c.i,g=o.i,d=f===u,m=p===g&&d,_,x,b,T,k,C,y,v;if(a||i){for(_=u<f||d&&p<g||m&&c.t<o.t,Ea(n,f,g,o.t)&&(f++,_||(u++,m?(c.t=(c.t-o.t)/(1-o.t),p=0):d&&(p-=g))),Math.abs(1-(t-e))<1e-5?u=f-1:!c.t&&u?u--:Ea(n,u,p,c.t)&&_&&f++,o.t===1&&(f=(f+1)%n.length),k=[],C=n.length,y=1+C*i,v=f,y+=(C-f+u)%C,T=0;T<y;T++)vm(k,n[v++%C]);n=k}else if(b=c.t===1?6:cr(l,p,c.t),e!==t)for(x=cr(h,g,m?o.t/c.t:o.t),d&&(b+=x),l.splice(p+b+2),(x||g)&&h.splice(0,g+x),T=n.length;T--;)(T<f||T>u)&&n.splice(T,1);else l.angle=Gl(l,p+b,0),p+=b,o=l[p],c=l[p+1],l.length=l.totalLength=0,l.totalPoints=n.totalPoints=8,l.push(o,c,o,c,o,c,o,c);return n.totalLength=0,n}function km(s,e,t){e=e||0,s.samples||(s.samples=[],s.lookup=[]);var i=~~s.resolution||12,n=1/i,r=s.length,a=s[e],o=s[e+1],c=e?e/6*i:0,l=s.samples,h=s.lookup,u=(e?s.minLength:lr)||lr,f=l[c+t*i-1],p=e?l[c-1]:0,g,d,m,_,x,b,T,k,C,y,v,A,E,S,R,V,X;for(l.length=h.length=0,d=e+2;d<r;d+=6){if(m=s[d+4]-a,_=s[d+2]-a,x=s[d]-a,k=s[d+5]-o,C=s[d+3]-o,y=s[d+1]-o,b=T=v=A=0,Ke(m)<.01&&Ke(k)<.01&&Ke(x)+Ke(y)<.01)s.length>8&&(s.splice(d,6),d-=6,r-=6);else for(g=1;g<=i;g++)S=n*g,E=1-S,b=T-(T=(S*S*m+3*E*(S*_+E*x))*S),v=A-(A=(S*S*k+3*E*(S*C+E*y))*S),V=pt(v*v+b*b),V<u&&(u=V),p+=V,l[c++]=p;a+=m,o+=k}if(f)for(f-=p;c<l.length;c++)l[c]+=f;if(l.length&&u){if(s.totalLength=X=l[l.length-1]||0,s.minLength=u,X/u<9999)for(V=R=0,g=0;g<X;g+=u)h[V++]=l[R]<g?++R:R}else s.totalLength=l[0]=0;return e?p-l[e/2-1]:p}function Jt(s,e){var t,i,n;for(n=t=i=0;n<s.length;n++)s[n].resolution=~~e||12,i+=s[n].length,t+=km(s[n]);return s.totalPoints=i,s.totalLength=t,s}function cr(s,e,t){if(t<=0||t>=1)return 0;var i=s[e],n=s[e+1],r=s[e+2],a=s[e+3],o=s[e+4],c=s[e+5],l=s[e+6],h=s[e+7],u=i+(r-i)*t,f=r+(o-r)*t,p=n+(a-n)*t,g=a+(c-a)*t,d=u+(f-u)*t,m=p+(g-p)*t,_=o+(l-o)*t,x=c+(h-c)*t;return f+=(_-f)*t,g+=(x-g)*t,s.splice(e+2,4,K(u),K(p),K(d),K(m),K(d+(f-d)*t),K(m+(g-m)*t),K(f),K(g),K(_),K(x)),s.samples&&s.samples.splice(e/6*s.resolution|0,0,0,0,0,0,0,0),6}function Ia(s,e,t,i){t=t||{},s.totalLength||Jt(s),(e<0||e>1)&&(e=ql(e));var n=0,r=s[0],a,o,c,l,h,u,f;if(!e)f=u=n=0,r=s[0];else if(e===1)f=1,n=s.length-1,r=s[n],u=r.length-8;else{if(s.length>1){for(c=s.totalLength*e,h=u=0;(h+=s[u++].totalLength)<c;)n=u;r=s[n],l=h-r.totalLength,e=(c-l)/(h-l)||0}a=r.samples,o=r.resolution,c=r.totalLength*e,u=r.lookup.length?r.lookup[~~(c/r.minLength)]||0:zl(a,c,e),l=u?a[u-1]:0,h=a[u],h<c&&(l=h,h=a[++u]),f=1/o*((c-l)/(h-l)+u%o),u=~~(u/o)*6,i&&f===1&&(u+6<r.length?(u+=6,f=0):n+1<s.length&&(u=f=0,r=s[++n]))}return t.t=f,t.i=u,t.path=s,t.segment=r,t.segIndex=n,t}function Da(s,e,t,i){var n=s[0],r=i||{},a,o,c,l,h,u,f,p,g;if((e<0||e>1)&&(e=ql(e)),n.lookup||Jt(s),s.length>1){for(c=s.totalLength*e,h=u=0;(h+=s[u++].totalLength)<c;)n=s[u];l=h-n.totalLength,e=(c-l)/(h-l)||0}return a=n.samples,o=n.resolution,c=n.totalLength*e,u=n.lookup.length?n.lookup[e<1?~~(c/n.minLength):n.lookup.length-1]||0:zl(a,c,e),l=u?a[u-1]:0,h=a[u],h<c&&(l=h,h=a[++u]),f=1/o*((c-l)/(h-l)+u%o)||0,g=1-f,u=~~(u/o)*6,p=n[u],r.x=K((f*f*(n[u+6]-p)+3*g*(f*(n[u+4]-p)+g*(n[u+2]-p)))*f+p),r.y=K((f*f*(n[u+7]-(p=n[u+1]))+3*g*(f*(n[u+5]-p)+g*(n[u+3]-p)))*f+p),t&&(r.angle=n.totalLength?Gl(n,u,f>=1?1-1e-9:f||1e-9):n.angle||0),r}function Xi(s,e,t,i,n,r,a){for(var o=s.length,c,l,h,u,f;--o>-1;)for(c=s[o],l=c.length,h=0;h<l;h+=2)u=c[h],f=c[h+1],c[h]=u*e+f*i+r,c[h+1]=u*t+f*n+a;return s._dirty=1,s}function Pm(s,e,t,i,n,r,a,o,c){if(!(s===o&&e===c)){t=Ke(t),i=Ke(i);var l=n%360*dm,h=En(l),u=Sn(l),f=Math.PI,p=f*2,g=(s-o)/2,d=(e-c)/2,m=h*g+u*d,_=-u*g+h*d,x=m*m,b=_*_,T=x/(t*t)+b/(i*i);T>1&&(t=pt(T)*t,i=pt(T)*i);var k=t*t,C=i*i,y=(k*C-k*b-C*x)/(k*b+C*x);y<0&&(y=0);var v=(r===a?-1:1)*pt(y),A=v*(t*_/i),E=v*-(i*m/t),S=(s+o)/2,R=(e+c)/2,V=S+(h*A-u*E),X=R+(u*A+h*E),q=(m-A)/t,B=(_-E)/i,G=(-m-A)/t,$=(-_-E)/i,Y=q*q+B*B,L=(B<0?-1:1)*Math.acos(q/pt(Y)),ce=(q*$-B*G<0?-1:1)*Math.acos((q*G+B*$)/pt(Y*(G*G+$*$)));isNaN(ce)&&(ce=f),!a&&ce>0?ce-=p:a&&ce<0&&(ce+=p),L%=p,ce%=p;var Ne=Math.ceil(Ke(ce)/(p/4)),F=[],ge=ce/Ne,qe=4/3*Sn(ge/2)/(1+En(ge/2)),ic=h*t,nc=u*t,sc=u*-i,rc=h*i,Re;for(Re=0;Re<Ne;Re++)n=L+Re*ge,m=En(n),_=Sn(n),q=En(n+=ge),B=Sn(n),F.push(m-qe*_,_+qe*m,q+qe*B,B-qe*q,q,B);for(Re=0;Re<F.length;Re+=2)m=F[Re],_=F[Re+1],F[Re]=m*ic+_*sc+V,F[Re+1]=m*nc+_*rc+X;return F[Re-2]=o,F[Re-1]=c,F}}function Zn(s){var e=(s+"").replace(hm,function(A){var E=+A;return E<1e-4&&E>-1e-4?0:E}).match(lm)||[],t=[],i=0,n=0,r=2/3,a=e.length,o=0,c="ERROR: malformed path: "+s,l,h,u,f,p,g,d,m,_,x,b,T,k,C,y,v=function(E,S,R,V){x=(R-E)/3,b=(V-S)/3,d.push(E+x,S+b,R-x,V-b,R,V)};if(!s||!isNaN(e[0])||isNaN(e[1]))return console.log(c),t;for(l=0;l<a;l++)if(k=p,isNaN(e[l])?(p=e[l].toUpperCase(),g=p!==e[l]):l--,u=+e[l+1],f=+e[l+2],g&&(u+=i,f+=n),l||(m=u,_=f),p==="M")d&&(d.length<8?t.length-=1:o+=d.length),i=m=u,n=_=f,d=[u,f],t.push(d),l+=2,p="L";else if(p==="C")d||(d=[0,0]),g||(i=n=0),d.push(u,f,i+e[l+3]*1,n+e[l+4]*1,i+=e[l+5]*1,n+=e[l+6]*1),l+=6;else if(p==="S")x=i,b=n,(k==="C"||k==="S")&&(x+=i-d[d.length-4],b+=n-d[d.length-3]),g||(i=n=0),d.push(x,b,u,f,i+=e[l+3]*1,n+=e[l+4]*1),l+=4;else if(p==="Q")x=i+(u-i)*r,b=n+(f-n)*r,g||(i=n=0),i+=e[l+3]*1,n+=e[l+4]*1,d.push(x,b,i+(u-i)*r,n+(f-n)*r,i,n),l+=4;else if(p==="T")x=i-d[d.length-4],b=n-d[d.length-3],d.push(i+x,n+b,u+(i+x*1.5-u)*r,f+(n+b*1.5-f)*r,i=u,n=f),l+=2;else if(p==="H")v(i,n,i=u,n),l+=1;else if(p==="V")v(i,n,i,n=u+(g?n-i:0)),l+=1;else if(p==="L"||p==="Z")p==="Z"&&(u=m,f=_,d.closed=!0),(p==="L"||Ke(i-u)>.5||Ke(n-f)>.5)&&(v(i,n,u,f),p==="L"&&(l+=2)),i=u,n=f;else if(p==="A"){if(C=e[l+4],y=e[l+5],x=e[l+6],b=e[l+7],h=7,C.length>1&&(C.length<3?(b=x,x=y,h--):(b=y,x=C.substr(2),h-=2),y=C.charAt(1),C=C.charAt(0)),T=Pm(i,n,+e[l+1],+e[l+2],+e[l+3],+C,+y,(g?i:0)+x*1,(g?n:0)+b*1),l+=h,T)for(h=0;h<T.length;h++)d.push(T[h]);i=d[d.length-2],n=d[d.length-1]}else console.log(c);return l=d.length,l<6?(t.pop(),l=0):d[0]===d[l-2]&&d[1]===d[l-1]&&(d.closed=!0),t.totalPoints=o+l,t}function Mm(s,e){e===void 0&&(e=1);for(var t=s[0],i=0,n=[t,i],r=2;r<s.length;r+=2)n.push(t,i,s[r],i=(s[r]-t)*e/2,t=s[r],-i);return n}function hr(s,e){Ke(s[0]-s[2])<1e-4&&Ke(s[1]-s[3])<1e-4&&(s=s.slice(2));var t=s.length-2,i=+s[0],n=+s[1],r=+s[2],a=+s[3],o=[i,n,i,n],c=r-i,l=a-n,h=Math.abs(s[t]-i)<.001&&Math.abs(s[t+1]-n)<.001,u,f,p,g,d,m,_,x,b,T,k,C,y,v,A;for(h&&(s.push(r,a),r=i,a=n,i=s[t-2],n=s[t-1],s.unshift(i,n),t+=4),e=e||e===0?+e:1,p=2;p<t;p+=2)u=i,f=n,i=r,n=a,r=+s[p+2],a=+s[p+3],!(i===r&&n===a)&&(g=c,d=l,c=r-i,l=a-n,m=pt(g*g+d*d),_=pt(c*c+l*l),x=pt(Math.pow(c/_+g/m,2)+Math.pow(l/_+d/m,2)),b=(m+_)*e*.25/x,T=i-(i-u)*(m?b/m:0),k=i+(r-i)*(_?b/_:0),C=i-(T+((k-T)*(m*3/(m+_)+.5)/4||0)),y=n-(n-f)*(m?b/m:0),v=n+(a-n)*(_?b/_:0),A=n-(y+((v-y)*(m*3/(m+_)+.5)/4||0)),(i!==u||n!==f)&&o.push(K(T+C),K(y+A),K(i),K(n),K(k+C),K(v+A)));return i!==r||n!==a||o.length<4?o.push(K(r),K(a),K(r),K(a)):o.length-=2,o.length===2?o.push(i,n,i,n,i,n):h&&(o.splice(0,6),o.length=o.length-6),o}function Xl(s){Nl(s[0])&&(s=[s]);var e="",t=s.length,i,n,r,a;for(n=0;n<t;n++){for(a=s[n],e+="M"+K(a[0])+","+K(a[1])+" C",i=a.length,r=2;r<i;r++)e+=K(a[r++])+","+K(a[r++])+" "+K(a[r++])+","+K(a[r++])+" "+K(a[r++])+","+K(a[r])+" ";a.closed&&(e+="z")}return e}/*!
 * matrix 3.13.0
 * https://gsap.com
 *
 * Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var gt,Zt,Fr,ls,ji,Un,es,Zi,tt="transform",ur=tt+"Origin",jl,Hl=function(e){var t=e.ownerDocument||e;for(!(tt in e.style)&&("msTransform"in e.style)&&(tt="msTransform",ur=tt+"Origin");t.parentNode&&(t=t.parentNode););if(Zt=window,es=new dn,t){gt=t,Fr=t.documentElement,ls=t.body,Zi=gt.createElementNS("http://www.w3.org/2000/svg","g"),Zi.style.transform="none";var i=t.createElement("div"),n=t.createElement("div"),r=t&&(t.body||t.firstElementChild);r&&r.appendChild&&(r.appendChild(i),i.appendChild(n),i.setAttribute("style","position:static;transform:translate3d(0,0,1px)"),jl=n.offsetParent!==i,r.removeChild(i))}return t},Sm=function(e){for(var t,i;e&&e!==ls;)i=e._gsap,i&&i.uncache&&i.get(e,"x"),i&&!i.scaleX&&!i.scaleY&&i.renderTransform&&(i.scaleX=i.scaleY=1e-4,i.renderTransform(1,i),t?t.push(i):t=[i]),e=e.parentNode;return t},Wl=[],Ql=[],Em=function(){return Zt.pageYOffset||gt.scrollTop||Fr.scrollTop||ls.scrollTop||0},Rm=function(){return Zt.pageXOffset||gt.scrollLeft||Fr.scrollLeft||ls.scrollLeft||0},Lr=function(e){return e.ownerSVGElement||((e.tagName+"").toLowerCase()==="svg"?e:null)},Im=function s(e){if(Zt.getComputedStyle(e).position==="fixed")return!0;if(e=e.parentNode,e&&e.nodeType===1)return s(e)},Rs=function s(e,t){if(e.parentNode&&(gt||Hl(e))){var i=Lr(e),n=i?i.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",r=i?t?"rect":"g":"div",a=t!==2?0:100,o=t===3?100:0,c="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",l=gt.createElementNS?gt.createElementNS(n.replace(/^https/,"http"),r):gt.createElement(r);return t&&(i?(Un||(Un=s(e)),l.setAttribute("width",.01),l.setAttribute("height",.01),l.setAttribute("transform","translate("+a+","+o+")"),Un.appendChild(l)):(ji||(ji=s(e),ji.style.cssText=c),l.style.cssText=c+"width:0.1px;height:0.1px;top:"+o+"px;left:"+a+"px",ji.appendChild(l))),l}throw"Need document and parent."},Dm=function(e){for(var t=new dn,i=0;i<e.numberOfItems;i++)t.multiply(e.getItem(i).matrix);return t},Om=function(e){var t=e.getCTM(),i;return t||(i=e.style[tt],e.style[tt]="none",e.appendChild(Zi),t=Zi.getCTM(),e.removeChild(Zi),i?e.style[tt]=i:e.style.removeProperty(tt.replace(/([A-Z])/g,"-$1").toLowerCase())),t||es.clone()},$m=function(e,t){var i=Lr(e),n=e===i,r=i?Wl:Ql,a=e.parentNode,o=a&&!i&&a.shadowRoot&&a.shadowRoot.appendChild?a.shadowRoot:a,c,l,h,u,f,p;if(e===Zt)return e;if(r.length||r.push(Rs(e,1),Rs(e,2),Rs(e,3)),c=i?Un:ji,i)n?(h=Om(e),u=-h.e/h.a,f=-h.f/h.d,l=es):e.getBBox?(h=e.getBBox(),l=e.transform?e.transform.baseVal:{},l=l.numberOfItems?l.numberOfItems>1?Dm(l):l.getItem(0).matrix:es,u=l.a*h.x+l.c*h.y,f=l.b*h.x+l.d*h.y):(l=new dn,u=f=0),t&&e.tagName.toLowerCase()==="g"&&(u=f=0),(n?i:a).appendChild(c),c.setAttribute("transform","matrix("+l.a+","+l.b+","+l.c+","+l.d+","+(l.e+u)+","+(l.f+f)+")");else{if(u=f=0,jl)for(l=e.offsetParent,h=e;h&&(h=h.parentNode)&&h!==l&&h.parentNode;)(Zt.getComputedStyle(h)[tt]+"").length>4&&(u=h.offsetLeft,f=h.offsetTop,h=0);if(p=Zt.getComputedStyle(e),p.position!=="absolute"&&p.position!=="fixed")for(l=e.offsetParent;a&&a!==l;)u+=a.scrollLeft||0,f+=a.scrollTop||0,a=a.parentNode;h=c.style,h.top=e.offsetTop-f+"px",h.left=e.offsetLeft-u+"px",h[tt]=p[tt],h[ur]=p[ur],h.position=p.position==="fixed"?"fixed":"absolute",o.appendChild(c)}return c},Is=function(e,t,i,n,r,a,o){return e.a=t,e.b=i,e.c=n,e.d=r,e.e=a,e.f=o,e},dn=function(){function s(t,i,n,r,a,o){t===void 0&&(t=1),i===void 0&&(i=0),n===void 0&&(n=0),r===void 0&&(r=1),a===void 0&&(a=0),o===void 0&&(o=0),Is(this,t,i,n,r,a,o)}var e=s.prototype;return e.inverse=function(){var i=this.a,n=this.b,r=this.c,a=this.d,o=this.e,c=this.f,l=i*a-n*r||1e-10;return Is(this,a/l,-n/l,-r/l,i/l,(r*c-a*o)/l,-(i*c-n*o)/l)},e.multiply=function(i){var n=this.a,r=this.b,a=this.c,o=this.d,c=this.e,l=this.f,h=i.a,u=i.c,f=i.b,p=i.d,g=i.e,d=i.f;return Is(this,h*n+f*a,h*r+f*o,u*n+p*a,u*r+p*o,c+g*n+d*a,l+g*r+d*o)},e.clone=function(){return new s(this.a,this.b,this.c,this.d,this.e,this.f)},e.equals=function(i){var n=this.a,r=this.b,a=this.c,o=this.d,c=this.e,l=this.f;return n===i.a&&r===i.b&&a===i.c&&o===i.d&&c===i.e&&l===i.f},e.apply=function(i,n){n===void 0&&(n={});var r=i.x,a=i.y,o=this.a,c=this.b,l=this.c,h=this.d,u=this.e,f=this.f;return n.x=r*o+a*l+u||0,n.y=r*c+a*h+f||0,n},s}();function bi(s,e,t,i){if(!s||!s.parentNode||(gt||Hl(s)).documentElement===s)return new dn;var n=Sm(s),r=Lr(s),a=r?Wl:Ql,o=$m(s,t),c=a[0].getBoundingClientRect(),l=a[1].getBoundingClientRect(),h=a[2].getBoundingClientRect(),u=o.parentNode,f=!i&&Im(s),p=new dn((l.left-c.left)/100,(l.top-c.top)/100,(h.left-c.left)/100,(h.top-c.top)/100,c.left+(f?0:Rm()),c.top+(f?0:Em()));if(u.removeChild(o),n)for(c=n.length;c--;)l=n[c],l.scaleX=l.scaleY=0,l.renderTransform(1,l);return e?p.inverse():p}/*!
 * MotionPathPlugin 3.13.0
 * https://gsap.com
 *
 * @license Copyright 2008-2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license
 * @author: Jack Doyle, <EMAIL>
*/var Vm="x,translateX,left,marginLeft,xPercent".split(","),Fm="y,translateY,top,marginTop,yPercent".split(","),Lm=Math.PI/180,Ye,Kl,ui,dr,Ds,Oa,Um=function(){return Ye||typeof window<"u"&&(Ye=window.gsap)&&Ye.registerPlugin&&Ye},Ui=function(e,t,i,n){for(var r=t.length,a=n===2?0:n,o=0;o<r;o++)e[a]=parseFloat(t[o][i]),n===2&&(e[a+1]=0),a+=2;return e},_i=function(e,t,i){return parseFloat(e._gsap.get(e,t,i||"px"))||0},Jl=function(e){var t=e[0],i=e[1],n;for(n=2;n<e.length;n+=2)t=e[n]+=t,i=e[n+1]+=i},$a=function(e,t,i,n,r,a,o,c,l){if(o.type==="cubic")t=[t];else{o.fromCurrent!==!1&&t.unshift(_i(i,n,c),r?_i(i,r,l):0),o.relative&&Jl(t);var h=r?hr:Mm;t=[h(t,o.curviness)]}return t=a(Zl(t,i,o)),ts(e,i,n,t,"x",c),r&&ts(e,i,r,t,"y",l),Jt(t,o.resolution||(o.curviness===0?20:12))},Bm=function(e){return e},Nm=/[-+\.]*\d+\.?(?:e-|e\+)?\d*/g,Va=function(e,t,i){var n=bi(e),r=0,a=0,o;return(e.tagName+"").toLowerCase()==="svg"?(o=e.viewBox.baseVal,o.width||(o={width:+e.getAttribute("width"),height:+e.getAttribute("height")})):o=t&&e.getBBox&&e.getBBox(),t&&t!=="auto"&&(r=t.push?t[0]*(o?o.width:e.offsetWidth||0):t.x,a=t.push?t[1]*(o?o.height:e.offsetHeight||0):t.y),i.apply(r||a?n.apply({x:r,y:a}):{x:n.e,y:n.f})},fr=function(e,t,i,n){var r=bi(e.parentNode,!0,!0),a=r.clone().multiply(bi(t)),o=Va(e,i,r),c=Va(t,n,r),l=c.x,h=c.y,u;return a.e=a.f=0,n==="auto"&&t.getTotalLength&&t.tagName.toLowerCase()==="path"&&(u=t.getAttribute("d").match(Nm)||[],u=a.apply({x:+u[0],y:+u[1]}),l+=u.x,h+=u.y),u&&(u=a.apply(t.getBBox()),l-=u.x,h-=u.y),a.e=l-o.x,a.f=h-o.y,a},Zl=function(e,t,i){var n=i.align,r=i.matrix,a=i.offsetX,o=i.offsetY,c=i.alignOrigin,l=e[0][0],h=e[0][1],u=_i(t,"x"),f=_i(t,"y"),p,g,d;return!e||!e.length?Ln("M0,0L0,0"):(n&&(n==="self"||(p=dr(n)[0]||t)===t?Xi(e,1,0,0,1,u-l,f-h):(c&&c[2]!==!1?Ye.set(t,{transformOrigin:c[0]*100+"% "+c[1]*100+"%"}):c=[_i(t,"xPercent")/-100,_i(t,"yPercent")/-100],g=fr(t,p,c,"auto"),d=g.apply({x:l,y:h}),Xi(e,g.a,g.b,g.c,g.d,u+g.e-(d.x-g.e),f+g.f-(d.y-g.f)))),r?Xi(e,r.a,r.b,r.c,r.d,r.e,r.f):(a||o)&&Xi(e,1,0,0,1,a||0,o||0),e)},ts=function(e,t,i,n,r,a){var o=t._gsap,c=o.harness,l=c&&c.aliases&&c.aliases[i],h=l&&l.indexOf(",")<0?l:i,u=e._pt=new Kl(e._pt,t,h,0,0,Bm,0,o.set(t,h,e));u.u=ui(o.get(t,h,a))||0,u.path=n,u.pp=r,e._props.push(h)},qm=function(e,t){return function(i){return e||t!==1?Yl(i,e,t):i}},Ur={version:"3.13.0",name:"motionPath",register:function(e,t,i){Ye=e,ui=Ye.utils.getUnit,dr=Ye.utils.toArray,Ds=Ye.core.getStyleSaver,Oa=Ye.core.reverting||function(){},Kl=i},init:function(e,t,i){if(!Ye)return console.warn("Please gsap.registerPlugin(MotionPathPlugin)"),!1;(!(typeof t=="object"&&!t.style)||!t.path)&&(t={path:t});var n=[],r=t,a=r.path,o=r.autoRotate,c=r.unitX,l=r.unitY,h=r.x,u=r.y,f=a[0],p=qm(t.start,"end"in t?t.end:1),g,d;if(this.rawPaths=n,this.target=e,this.tween=i,this.styles=Ds&&Ds(e,"transform"),(this.rotate=o||o===0)&&(this.rOffset=parseFloat(o)||0,this.radians=!!t.useRadians,this.rProp=t.rotation||"rotation",this.rSet=e._gsap.set(e,this.rProp,this),this.ru=ui(e._gsap.get(e,this.rProp))||0),Array.isArray(a)&&!("closed"in a)&&typeof f!="number"){for(d in f)!h&&~Vm.indexOf(d)?h=d:!u&&~Fm.indexOf(d)&&(u=d);h&&u?n.push($a(this,Ui(Ui([],a,h,0),a,u,1),e,h,u,p,t,c||ui(a[0][h]),l||ui(a[0][u]))):h=u=0;for(d in f)d!==h&&d!==u&&n.push($a(this,Ui([],a,d,2),e,d,0,p,t,ui(a[0][d])))}else g=p(Zl(Ln(t.path),e,t)),Jt(g,t.resolution),n.push(g),ts(this,e,t.x||"x",g,"x",t.unitX||"px"),ts(this,e,t.y||"y",g,"y",t.unitY||"px");i.vars.immediateRender&&this.render(i.progress(),this)},render:function(e,t){var i=t.rawPaths,n=i.length,r=t._pt;if(t.tween._time||!Oa()){for(e>1?e=1:e<0&&(e=0);n--;)Da(i[n],e,!n&&t.rotate,i[n]);for(;r;)r.set(r.t,r.p,r.path[r.pp]+r.u,r.d,e),r=r._next;t.rotate&&t.rSet(t.target,t.rProp,i[0].angle*(t.radians?Lm:1)+t.rOffset+t.ru,t,e)}else t.styles.revert()},getLength:function(e){return Jt(Ln(e)).totalLength},sliceRawPath:Yl,getRawPath:Ln,pointsToSegment:hr,stringToRawPath:Zn,rawPathToString:Xl,transformRawPath:Xi,getGlobalMatrix:bi,getPositionOnPath:Da,cacheRawPathMeasurements:Jt,convertToPath:function(e,t){return dr(e).map(function(i){return Tm(i,t!==!1)})},convertCoordinates:function(e,t,i){var n=bi(t,!0,!0).multiply(bi(e));return i?n.apply(i):n},getAlignMatrix:fr,getRelativePosition:function(e,t,i,n){var r=fr(e,t,i,n);return{x:r.e,y:r.f}},arrayToRawPath:function(e,t){t=t||{};var i=Ui(Ui([],e,t.x||"x",0),e,t.y||"y",1);return t.relative&&Jl(i),[t.type==="cubic"?i:hr(i,t.curviness)]}};Um()&&Ye.registerPlugin(Ur);os.registerPlugin(Ur);os.registerPlugin(Ur);class zm{constructor(){Ie(this,"animations",new Map);Ie(this,"animationCounter",0)}createMultiElementPathAnimation(e,t,i={}){const n=this.buildConnectedPath(t);return this.createPathAnimation(e,n,i)}createPathAnimation(e,t,i={}){if(!t||t.length<2)throw new Error("路径至少需要2个点");const n=`path_anim_${++this.animationCounter}`,a={...{duration:5e3,loop:!0,autoRotate:!1,ease:"power2.inOut",speed:1,direction:"forward"},...i},o=os.timeline({repeat:a.loop?-1:0,paused:!0}),c=a.direction==="reverse"?[...t].reverse():t;if(e.id&&typeof e.id=="string")this.createMeta2DPathAnimation(o,e,c,a);else if(e instanceof HTMLElement)this.createDOMPathAnimation(o,e,c,a);else if(e.x!==void 0&&e.y!==void 0)this.createPixiPathAnimation(o,e,c,a);else throw new Error("不支持的目标对象类型");const l={id:n,timeline:o,isRunning:!1,target:e,path:c,options:a,originalConnections:void 0};return this.animations.set(n,l),n}createMeta2DPathAnimation(e,t,i,n){const r=(n.duration||5e3)/1e3,a=this.temporarilyDisconnectPen(t.id),o=this.findAnimationIdByTarget(t);if(o){const l=this.animations.get(o);l&&(l.originalConnections=a)}const c={progress:0};e.to(c,{progress:1,duration:r/(n.speed||1),ease:n.ease,repeat:n.repeat!==void 0?n.repeat:n.loop?-1:0,onUpdate:()=>{const l=this.getPositionAtProgress(i,c.progress),h=this.calculateCenterOffset(t);window.meta2d&&(window.meta2d.setValue({id:t.id,x:l.x-h.x,y:l.y-h.y}),window.meta2d.render()),n.onUpdate&&n.onUpdate(c.progress,l)},onRepeat:()=>{n.onRepeat&&n.onRepeat()},onComplete:()=>{n.onComplete&&n.onComplete()}}),e.eventCallback("onComplete",()=>{this.restoreConnections(t.id,a)})}temporarilyDisconnectPen(e){var n;if(!window.meta2d)return null;const t=window.meta2d.store.data.pens.find(r=>r.id===e);if(!t)return null;const i={connectedLines:t.connectedLines?[...t.connectedLines]:[],anchors:t.anchors?t.anchors.map(r=>({...r})):[],calculative:t.calculative?{connectedLines:t.calculative.connectedLines?[...t.calculative.connectedLines]:[],worldAnchors:t.calculative.worldAnchors?t.calculative.worldAnchors.map(r=>({...r})):[]}:null};return t.connectedLines&&(t.connectedLines=[]),(n=t.calculative)!=null&&n.connectedLines&&(t.calculative.connectedLines=[]),this.disconnectFromConnectedLines(e),i}disconnectFromConnectedLines(e){window.meta2d&&window.meta2d.store.data.pens.forEach(t=>{var i;(t.lineName||t.type==="line")&&(t.anchors&&t.anchors.forEach(n=>{n.connectTo===e&&(n.tempDisconnectedFrom=n.connectTo,delete n.connectTo)}),(i=t.calculative)!=null&&i.worldAnchors&&t.calculative.worldAnchors.forEach(n=>{n.connectTo===e&&(n.tempDisconnectedFrom=n.connectTo,delete n.connectTo)}))})}restoreConnections(e,t){if(!window.meta2d||!t)return;const i=window.meta2d.store.data.pens.find(n=>n.id===e);i&&(i.connectedLines=t.connectedLines,i.calculative&&t.calculative&&(i.calculative.connectedLines=t.calculative.connectedLines),this.restoreLineConnections(e),window.meta2d.render())}restoreLineConnections(e){window.meta2d&&window.meta2d.store.data.pens.forEach(t=>{var i;(t.lineName||t.type==="line")&&(t.anchors&&t.anchors.forEach(n=>{n.tempDisconnectedFrom===e&&(n.connectTo=n.tempDisconnectedFrom,delete n.tempDisconnectedFrom)}),(i=t.calculative)!=null&&i.worldAnchors&&t.calculative.worldAnchors.forEach(n=>{n.tempDisconnectedFrom===e&&(n.connectTo=n.tempDisconnectedFrom,delete n.tempDisconnectedFrom)}))})}createDOMPathAnimation(e,t,i,n){const r=(n.duration||5e3)/1e3;e.to(t,{duration:r/(n.speed||1),ease:n.ease,repeat:n.repeat!==void 0?n.repeat:n.loop?-1:0,motionPath:{path:i,autoRotate:n.autoRotate},onUpdate:()=>{if(n.onUpdate){const a=t.getBoundingClientRect();n.onUpdate(e.progress(),{x:a.left,y:a.top})}},onRepeat:n.onRepeat,onComplete:n.onComplete})}createPixiPathAnimation(e,t,i,n){const r=(n.duration||5e3)/1e3;e.to(t,{duration:r/(n.speed||1),ease:n.ease,repeat:n.repeat!==void 0?n.repeat:n.loop?-1:0,motionPath:{path:i,autoRotate:n.autoRotate},onUpdate:()=>{n.onUpdate&&n.onUpdate(e.progress(),{x:t.x,y:t.y})},onRepeat:n.onRepeat,onComplete:n.onComplete})}buildConnectedPath(e){if(e.length===0)return[];console.log("🔍 开始构建连接路径，元素数量:",e.length),console.log("📋 所有元素:",e.map(r=>({id:r.id,name:r.name,lineName:r.lineName,type:r.lineName?"line":r.name})));const t=e.filter(r=>r.lineName||r.name==="line"),i=e.filter(r=>r.name==="circle"||r.name==="ellipse");if(console.log("📏 线段数量:",t.length),console.log("⭕ 转折点数量:",i.length),t.length===1&&i.length===0)return console.log("✅ 单线段，直接返回路径点"),this.extractPathPoints(t[0]);console.log("🔗 执行多线段连接算法");const n=this.connectMultipleSegments(t,i);return console.log("🎯 最终连接路径点数量:",n.length),console.log("📍 路径点:",n),n}connectMultipleSegments(e,t){const i=[];console.log("🔗 开始连接多个线段");const n=e.map((l,h)=>{const u=this.extractPathPoints(l),f={line:l,startPoint:u[0],endPoint:u[u.length-1],points:u};return console.log(`📏 线段${h+1}:`,{id:l.id,startPoint:f.startPoint,endPoint:f.endPoint,pointCount:u.length,allPoints:u}),f}),r=t.map(l=>({junction:l,center:{x:l.x+(l.width||0)/2,y:l.y+(l.height||0)/2}})),a=new Set;let o=null,c=this.findStartLine(n);for(console.log("🎯 起始线段:",c?c.line.id:"null");c&&!a.has(c.line);){if(console.log(`🔄 处理线段: ${c.line.id}`),a.add(c.line),i.length===0)console.log(`➕ 添加第一条线段的所有点 (${c.points.length}个)`),i.push(...c.points),o=c.endPoint;else{const u=this.calculateDistance(o,c.startPoint),f=this.calculateDistance(o,c.endPoint);if(console.log(`🔍 检查连接方向: 到起点距离=${u}, 到终点距离=${f}`),u<f)console.log(`➕ 正常方向：添加线段点 (跳过起始点，${c.points.length-1}个)`),i.push(...c.points.slice(1)),o=c.endPoint;else{console.log(`🔄 反转方向：添加反转线段点 (跳过起始点，${c.points.length-1}个)`);const p=[...c.points].reverse();i.push(...p.slice(1)),o=p[p.length-1]}}console.log(`📍 当前位置: (${o==null?void 0:o.x}, ${o==null?void 0:o.y})`),console.log(`📊 已连接路径点数: ${i.length}`),console.log(`🔢 剩余未使用线段数: ${n.filter(u=>!a.has(u.line)).length}`);const l=n.filter(u=>!a.has(u.line));console.log("🔍 查找下一个连接...");const h=this.findNextConnection(o,l,r);if(console.log("🎯 找到连接类型:",h.type),h.type==="junction"){const u=h.target;i.push(u.center),o=u.center,c=this.findNearestLine(o,n.filter(f=>!a.has(f.line)))}else if(h.type==="line"){const u=h.target;console.log("🔗 连接到下一条线段:",u.line.id),console.log("📍 下一条线段原始点:",u.points);const f=this.calculateDistance(o,u.startPoint),p=this.calculateDistance(o,u.endPoint);console.log("📏 连接距离:",{toStart:f,toEnd:p,currentPoint:o,nextStart:u.startPoint,nextEnd:u.endPoint}),c=u}else break}return i}extractPathPoints(e){var a;console.log("🔍 分析图元路径点:",{id:e.id,name:e.name,lineName:e.lineName,type:e.type});const t=(a=e.calculative)==null?void 0:a.worldAnchors,i=e.anchors||[];if(e.lineName==="curve"&&t&&t.length>0){console.log("🎯 检测到弯曲轨道，查找曲线点...");for(const o of t)if(o.curvePoints&&o.curvePoints.length>0){console.log("✅ 发现曲线点数据:",o.curvePoints.length,"个点");const c=o.curvePoints.map(l=>({x:l.x,y:l.y}));return console.log("📍 使用曲线路径点，前3个点:",c.slice(0,3)),c}console.log("⚠️ 弯曲轨道未找到曲线点，回退到锚点")}const r=(t||i).map(o=>({x:o.x,y:o.y}));return console.log("📍 使用普通锚点:",r.length,"个点",r),r}findStartLine(e){return e.length===0?null:e.reduce((t,i)=>{const n=Math.min(t.startPoint.x,t.endPoint.x)+Math.min(t.startPoint.y,t.endPoint.y);return Math.min(i.startPoint.x,i.endPoint.x)+Math.min(i.startPoint.y,i.endPoint.y)<n?i:t})}findNextConnection(e,t,i){for(const r of i)if(this.calculateDistance(e,r.center)<=100)return{type:"junction",target:r};for(const r of t){const a=this.calculateDistance(e,r.startPoint),o=this.calculateDistance(e,r.endPoint);if(a<=100||o<=100){if(a<o){const c=[...r.points].reverse();r.points=c,[r.startPoint,r.endPoint]=[r.endPoint,r.startPoint]}return{type:"line",target:r}}}if(t.length>0){const r=this.findNearestLine(e,t);if(r){const a=this.calculateDistance(e,r.startPoint),o=this.calculateDistance(e,r.endPoint);if(a<o){const c=[...r.points].reverse();r.points=c,[r.startPoint,r.endPoint]=[r.endPoint,r.startPoint]}return{type:"line",target:r}}}return{type:"none",target:null}}findNearestLine(e,t){return t.length===0?null:t.reduce((i,n)=>{const r=Math.min(this.calculateDistance(e,i.startPoint),this.calculateDistance(e,i.endPoint));return Math.min(this.calculateDistance(e,n.startPoint),this.calculateDistance(e,n.endPoint))<r?n:i})}calculateDistance(e,t){const i=e.x-t.x,n=e.y-t.y;return Math.sqrt(i*i+n*n)}calculateCenterOffset(e){var n,r;const t=e.width||((n=e.calculative)==null?void 0:n.width)||0,i=e.height||((r=e.calculative)==null?void 0:r.height)||0;return{x:t/2,y:i/2}}getPositionAtProgress(e,t){if(t<=0)return e[0];if(t>=1)return e[e.length-1];if(e.length<2)return e[0];e.length>2&&console.log(`🎯 多点路径插值: ${e.length}个点, 进度${(t*100).toFixed(1)}%`);const i=e.length-1,n=t*i,r=Math.floor(n),a=n-r;if(r>=i)return e[e.length-1];const o=e[r],c=e[r+1],l={x:o.x+(c.x-o.x)*a,y:o.y+(c.y-o.y)*a};return e.length>2&&Math.random()<.1&&console.log(`📍 段${r}/${i}, 局部进度${(a*100).toFixed(1)}%, 位置(${l.x.toFixed(1)}, ${l.y.toFixed(1)})`),l}startAnimation(e){const t=this.animations.get(e);return t?(t.timeline.play(),t.isRunning=!0,!0):!1}pauseAnimation(e){const t=this.animations.get(e);return t?(t.timeline.pause(),t.isRunning=!1,!0):!1}stopAnimation(e){const t=this.animations.get(e);return t?(this.restoreConnectionsForAnimation(t),t.timeline.kill(),t.isRunning=!1,this.animations.delete(e),!0):!1}setAnimationSpeed(e,t){const i=this.animations.get(e);return i?(i.timeline.timeScale(t),i.options.speed=t,!0):!1}getAnimationInfo(e){return this.animations.get(e)}getAllAnimations(){return Array.from(this.animations.values())}findAnimationIdByTarget(e){for(const[t,i]of this.animations)if(i.target===e||i.target.id===e.id)return t;return null}restoreConnectionsForAnimation(e){e.target.id&&typeof e.target.id=="string"&&e.originalConnections&&this.restoreConnections(e.target.id,e.originalConnections)}stopAllAnimations(){this.animations.forEach(e=>{this.restoreConnectionsForAnimation(e),e.timeline.kill()}),this.animations.clear()}destroy(){this.stopAllAnimations()}}const we=new zm;class Gm{constructor(){Ie(this,"currentAnimationId",null);Ie(this,"isWaitingForDecision",!1);Ie(this,"junctions",[])}analyzeJunctions(e){const t=[];return e.filter(n=>n.name==="circle"&&n.color===void 0).forEach(n=>{var a,o,c,l;const r=e.filter(h=>h.color==="rgba(255, 89, 89, 1)"&&this.isNearPosition(n,h));r.length>0&&t.push({id:n.id,position:{x:((o=(a=n.worldAnchors)==null?void 0:a[0])==null?void 0:o.x)||n.x||0,y:((l=(c=n.worldAnchors)==null?void 0:c[0])==null?void 0:l.y)||n.y||0},element:n,connections:{red:r}})}),console.log(`🔍 检测到 ${t.length} 个转折点:`,t),t}isNearPosition(e,t,i=50){var o,c,l,h,u,f,p,g;const n={x:((c=(o=e.worldAnchors)==null?void 0:o[0])==null?void 0:c.x)||e.x||0,y:((h=(l=e.worldAnchors)==null?void 0:l[0])==null?void 0:h.y)||e.y||0},r={x:((f=(u=t.worldAnchors)==null?void 0:u[0])==null?void 0:f.x)||t.x||0,y:((g=(p=t.worldAnchors)==null?void 0:p[0])==null?void 0:g.y)||t.y||0};return Math.sqrt(Math.pow(n.x-r.x,2)+Math.pow(n.y-r.y,2))<=i}checkJunctionReached(e){for(const t of this.junctions)if(Math.sqrt(Math.pow(e.x-t.position.x,2)+Math.pow(e.y-t.position.y,2))<=30)return t;return null}async showPathDecisionDialog(){try{return await fi.confirm("小车已到达转折点！请选择路径：","🚗 路径选择",{confirmButtonText:"🔄 切换到蓝色轨道",cancelButtonText:"➡️ 继续黑色轨道",type:"info",center:!0,customClass:"path-decision-dialog"}),"switch"}catch{return"continue"}}buildPathSegments(e){return{black:e.filter(t=>t.color==="rgba(0, 0, 0, 1)"||t.mqttId==="micro_approach"||t.mqttId==="smooth_approach"||t.mqttId==="approach"||!t.color&&!t.mqttId&&t.id&&t.name),blue:e.filter(t=>t.color==="rgba(115, 129, 255, 1)"),red:e.filter(t=>t.color==="rgba(255, 89, 89, 1)")}}connectPathSegments(e){if(e.length===0)return[];console.log(`🔗 连接 ${e.length} 个路径段`);const t=e.map(a=>{var _;console.log("🔍 InteractivePathAnimationService分析段:",{id:a.id,name:a.name,lineName:a.lineName,type:a.type,hasStartPoint:!!a.startPoint,hasEndPoint:!!a.endPoint,segmentDirection:a.segmentDirection,startPoint:a.startPoint,endPoint:a.endPoint});const o=((_=a.calculative)==null?void 0:_.worldAnchors)||a.worldAnchors,c=a.anchors||[];if(a.lineName==="curve"&&o&&o.length>0){console.log("🎯 InteractivePathAnimationService检测到弯曲轨道，查找曲线点...");for(const x of o)if(x.curvePoints&&x.curvePoints.length>0){console.log("✅ InteractivePathAnimationService发现曲线点数据:",x.curvePoints.length,"个点");let b=x.curvePoints.map(A=>({x:A.x,y:A.y}));if(a.startPoint&&a.endPoint){console.log("🎯 路径段有明确起始点和结束点，截取曲线部分"),console.log("起始点:",a.startPoint),console.log("结束点:",a.endPoint);const A=this.findClosestPointIndex(b,a.startPoint),E=this.findClosestPointIndex(b,a.endPoint);if(console.log(`曲线点索引: 起始${A}, 结束${E}`),A===E){const S=Math.max(0,A-1),R=Math.min(b.length-1,E+1);b=b.slice(S,R+1),console.log(`🔧 索引相同，扩展范围: ${S} -> ${R}`)}else A<=E?b=b.slice(A,E+1):b=b.slice(E,A+1).reverse();if(b.length<2){console.warn("⚠️ 截取后点数不足，使用更多曲线点");const S=Math.max(0,Math.min(A,E)-2),R=Math.min(b.length-1,Math.max(A,E)+2);b=x.curvePoints.slice(S,R+1).map(V=>({x:V.x,y:V.y}))}console.log("📍 截取后的曲线点数量:",b.length)}console.log("📍 InteractivePathAnimationService使用曲线路径点，前3个点:",b.slice(0,3));const T=b,k=Math.min(...T.map(A=>A.x)),C=Math.max(...T.map(A=>A.x)),y=Math.min(...T.map(A=>A.y)),v=Math.max(...T.map(A=>A.y));return{id:a.id,points:T,startPoint:T[0],endPoint:T[T.length-1],bounds:{minX:k,maxX:C,minY:y,maxY:v},type:"curved",length:this.calculatePathLength(T)}}console.log("⚠️ InteractivePathAnimationService弯曲轨道未找到曲线点，回退到锚点")}const l=(o||c).map(x=>({x:x.x,y:x.y}));console.log("📍 InteractivePathAnimationService使用普通锚点:",l.length,"个点");const h=Math.min(...l.map(x=>x.x)),u=Math.max(...l.map(x=>x.x)),f=Math.min(...l.map(x=>x.y)),p=Math.max(...l.map(x=>x.y)),g=Math.abs(p-f)<Math.abs(u-h),d=Math.abs(u-h)<Math.abs(p-f),m=!g&&!d;return{id:a.id,points:l,startPoint:l[0],endPoint:l[l.length-1],bounds:{minX:h,maxX:u,minY:f,maxY:p},type:m?"curved":g?"horizontal":"vertical",length:this.calculatePathLength(l)}});if(t.length===1)return console.log("单个路径段，直接返回"),t[0].points;const i=[],n=new Set;let r=this.selectOptimalStartSegment(t);for(console.log(`🚀 起始段: ${r.id} (${r.type})`),i.push(...r.points),n.add(r.id);n.size<t.length;){const a=i[i.length-1],o=this.selectNextOptimalSegment(r,a,t,n);if(o.segment){console.log(`🔗 连接段: ${o.segment.id} (${o.segment.type}), 距离: ${o.distance.toFixed(2)}, 反转: ${o.shouldReverse}`);let c=[...o.segment.points];o.shouldReverse&&c.reverse(),i.push(...c.slice(1)),n.add(o.segment.id),r=o.segment}else{console.warn("无法找到下一个连接段");break}}return console.log(`✅ 路径连接完成，总点数: ${i.length}`),i}selectOptimalStartSegment(e){const t=e.filter(i=>i.type==="horizontal");return t.length>0?t.reduce((i,n)=>n.bounds.minX<i.bounds.minX?n:i):e.reduce((i,n)=>n.bounds.minX<i.bounds.minX?n:i)}selectNextOptimalSegment(e,t,i,n){let r=null,a=1/0,o=!1;for(const c of i){if(n.has(c.id))continue;const l=[{point:c.startPoint,reverse:!1,distance:this.calculateDistance(t,c.startPoint)},{point:c.endPoint,reverse:!0,distance:this.calculateDistance(t,c.endPoint)}];for(const h of l){let u=h.distance;e.type==="horizontal"&&c.type==="curved"||e.type==="curved"&&c.type==="horizontal"?u*=.8:e.type===c.type&&(u*=1.2),this.isDirectionContinuous(e,c,h.reverse)&&(u*=.7),u<a&&(a=u,r=c,o=h.reverse)}}return{segment:r,distance:a,shouldReverse:o}}isDirectionContinuous(e,t,i){return e.type==="horizontal"&&t.type==="curved"?Math.abs(e.bounds.maxX-t.bounds.minX)<50:e.type==="curved"&&t.type==="horizontal"?Math.abs(e.bounds.maxY-t.bounds.minY)<50:!1}findClosestPointIndex(e,t){let i=1/0,n=0;for(let r=0;r<e.length;r++){const a=Math.sqrt(Math.pow(e[r].x-t.x,2)+Math.pow(e[r].y-t.y,2));a<i&&(i=a,n=r)}return n}calculatePathLength(e){let t=0;for(let i=1;i<e.length;i++)t+=this.calculateDistance(e[i-1],e[i]);return t}calculateDistance(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}async createInteractiveAnimation(e,t,i={}){if(this.currentAnimationId)return console.warn("交互式动画已在运行中"),null;this.junctions=this.analyzeJunctions(t);const n=this.buildPathSegments(t);return console.log("🎯 开始交互式路径动画"),console.log("路径段:",{black:n.black.length,blue:n.blue.length,red:n.red.length}),await this.executeBlackPathPhase(e,n.black,n,i),this.currentAnimationId}async executeBlackPathPhase(e,t,i,n){const r=this.connectPathSegments(t);if(r.length===0){console.warn("黑色轨道路径为空");return}let a=!1;const o={...n,loop:!1,onUpdate:(c,l)=>{if(!a&&!this.isWaitingForDecision){const h=this.checkJunctionReached(l);h&&(a=!0,this.handleJunctionReached(e,h,i,n))}n.onUpdate&&n.onUpdate(c,l)},onComplete:()=>{a||(console.log("🏁 黑色轨道完成，没有遇到转折点"),n.onPathComplete&&n.onPathComplete())}};this.currentAnimationId=we.createPathAnimation(e,r,o),this.currentAnimationId&&we.startAnimation(this.currentAnimationId)}async handleJunctionReached(e,t,i,n){console.log("🔄 到达转折点:",t.id),this.currentAnimationId&&we.stopAnimation(this.currentAnimationId),this.isWaitingForDecision=!0;try{const r=await this.showPathDecisionDialog();console.log("👤 用户选择:",r),r==="switch"?await this.executeBluePathPhase(e,i.red,i.blue,n):await this.continueBlackPath(e,i.black,n)}catch(r){console.error("处理转折点决策时出错:",r)}finally{this.isWaitingForDecision=!1}}async executeBluePathPhase(e,t,i,n){if(console.log("🔄 切换到蓝色轨道"),t.length>0){const r=this.connectPathSegments(t),a={...n,duration:1500,loop:!1,onComplete:()=>{const o=r[r.length-1];this.executeBlueTrackPhase(e,i,n,o)}};this.currentAnimationId=we.createPathAnimation(e,r,a),this.currentAnimationId&&we.startAnimation(this.currentAnimationId)}else this.executeBlueTrackPhase(e,i,n)}executeBlueTrackPhase(e,t,i,n){let r=this.connectPathSegments(t);if(r.length===0){console.warn("蓝色轨道路径为空");return}if(n){console.log("🔗 调整蓝色轨道起始点:",n);let o=1/0,c=0;for(let h=0;h<r.length;h++){const u=this.calculateDistance(n,r[h]);u<o&&(o=u,c=h)}console.log(`📍 找到最近点索引: ${c}, 距离: ${o.toFixed(2)}`),r=[n,...r.slice(c)],console.log(`✅ 调整后蓝色轨道点数: ${r.length}`)}const a={...i,loop:!1,onComplete:()=>{console.log("🏁 蓝色轨道完成"),this.currentAnimationId=null,i.onPathComplete&&i.onPathComplete()}};this.currentAnimationId=we.createPathAnimation(e,r,a),this.currentAnimationId&&we.startAnimation(this.currentAnimationId)}async continueBlackPath(e,t,i){console.log("➡️ 继续黑色轨道");const n=this.connectPathSegments(t),r={...i,loop:!1,onComplete:()=>{console.log("🏁 黑色轨道完成"),this.currentAnimationId=null,i.onPathComplete&&i.onPathComplete()}};this.currentAnimationId=we.createPathAnimation(e,n,r),this.currentAnimationId&&we.startAnimation(this.currentAnimationId)}stopInteractiveAnimation(){if(this.currentAnimationId){const e=we.stopAnimation(this.currentAnimationId);return this.currentAnimationId=null,this.isWaitingForDecision=!1,e}return!1}isRunning(){return this.currentAnimationId!==null||this.isWaitingForDecision}}const en=new Gm,Ym={class:"property-panel"},Xm={class:"panel-content"},jm={key:0,class:"empty-state"},Hm={key:1},Wm={key:2},Qm={class:"animation-controls"},Km={class:"control-group"},Jm={class:"advanced-controls"},Zm={class:"control-item"},ep={class:"control-item"},tp={class:"control-item"},ip={class:"control-item"},np={class:"control-item"},sp={key:0,class:"progress-display"},rp={class:"progress-info"},ap={class:"progress-percentage"},op={class:"vehicle-info"},lp={class:"interactive-animation-section"},cp={class:"interactive-controls"},hp={key:0,class:"interactive-status"},up={key:3,class:"multi-select-state"},dp=Ze({__name:"PenPanel",setup(s){const{selections:e}=Oi();vt();const t=Xe(()=>{var C;return((C=e.pens)==null?void 0:C.length)||0}),i=Xe(()=>e.pens?e.pens.filter(C=>!C.lineName&&C.name!=="circle"&&C.name!=="ellipse").length:0),n=Xe(()=>e.pens?e.pens.filter(C=>C.lineName||C.name==="circle"||C.name==="ellipse").length:0),r=Xe(()=>!e.pens||e.pens.length<2?!1:i.value>0&&n.value>0),a=U([]),o=U(1),c=U("power2.inOut"),l=U("forward"),h=U(!1),u=U(10),f=U({isRunning:!1,current:0,total:10,percentage:0,activeVehicles:0}),p=U(!1),g=()=>{var E;if(((E=e.pens)==null?void 0:E.length)<2){console.warn("请至少选择一个小车和路径元素。");return}const C=e.pens.filter(S=>!S.lineName&&S.name!=="circle"&&S.name!=="ellipse"),y=e.pens.filter(S=>S.lineName||S.name==="circle"||S.name==="ellipse");if(C.length===0){console.warn("请选择至少一台小车（非线条、非圆形的图元）。");return}if(y.length===0){console.warn("请选择至少一个路径元素（线条或转折点）。");return}console.log(`开始多台小车路径动画: ${C.length} 台小车沿 ${y.length} 个路径元素移动`),console.log("小车:",C.map(S=>`${S.name||"vehicle"}(${S.id})`)),console.log("路径元素:",y.map(S=>`${S.name||"line"}(${S.id})`)),a.value.forEach(S=>{we.stopAnimation(S)}),a.value=[],f.value={isRunning:!0,current:0,total:u.value,percentage:0,activeVehicles:C.length};let v=0,A=0;try{C.forEach((S,R)=>{const V=R*.5,X={duration:5e3,loop:u.value===-1,repeat:u.value===-1?-1:u.value-1,autoRotate:h.value,ease:c.value,speed:o.value,direction:l.value,onUpdate:(B,G)=>{Math.floor(B*100)%20===0&&console.log(`小车${R+1}动画进度: ${(B*100).toFixed(0)}%`)},onRepeat:()=>{if(f.value.total!==-1){A++;const B=A/(C.length*f.value.total);f.value.current=Math.floor(B*f.value.total),f.value.percentage=Math.round(B*100),console.log(`小车${R+1}完成一次循环，总体进度: ${f.value.percentage}%`)}},onComplete:()=>{v++,console.log(`小车${R+1}动画完成 (${v}/${C.length})`),v>=C.length&&(f.value.isRunning=!1,f.value.current=f.value.total,f.value.percentage=100,f.value.activeVehicles=0,console.log("所有小车动画完成！"))}},q=we.createMultiElementPathAnimation(S,y,X);a.value.push(q),setTimeout(()=>{we.startAnimation(q)?console.log(`小车${R+1}动画已启动，ID: ${q}`):console.error(`小车${R+1}动画启动失败`)},V*1e3)}),console.log(`${C.length} 台小车的动画已创建完成`)}catch(S){console.error("创建多台小车动画失败:",S)}},d=()=>{if(console.log("停止所有小车动画"),a.value.length>0){let C=0;a.value.forEach(y=>{we.stopAnimation(y)&&C++}),console.log(`已停止 ${C}/${a.value.length} 个动画`),a.value=[],f.value.isRunning=!1,f.value.activeVehicles=0}else console.log("没有正在运行的动画")},m=C=>{a.value.length>0&&(a.value.forEach(y=>{we.setAnimationSpeed(y,C)}),console.log(`所有小车动画速度已调整为: ${C}x`))},_=()=>{console.log(`缓动类型已更改为: ${c.value}`)},x=()=>{console.log(`移动方向已更改为: ${l.value}`)},b=()=>{console.log(`自动旋转已${h.value?"启用":"禁用"}`)},T=async()=>{if(!e.pens||e.pens.length<2){console.warn("请至少选择一个小车和路径元素");return}const C=e.pens.filter(v=>v.name==="square"&&v.color===void 0),y=e.pens.filter(v=>v.name==="line"||v.name==="circle");if(C.length===0){console.warn("未找到小车元素（正方形，color: undefined）");return}if(y.length===0){console.warn("未找到路径元素");return}console.log(`🎯 启动交互式动画: ${C.length} 台小车, ${y.length} 个路径元素`),p.value=!0;try{const v=C[0],A={speed:o.value,ease:c.value,autoRotate:h.value,onPathComplete:()=>{console.log("🏁 交互式路径动画完成"),p.value=!1}};await en.createInteractiveAnimation(v,y,A)?console.log("✅ 交互式动画启动成功"):(console.error("❌ 交互式动画启动失败"),p.value=!1)}catch(v){console.error("启动交互式动画时出错:",v),p.value=!1}},k=()=>{console.log("停止交互式动画");const C=en.stopInteractiveAnimation();console.log(C?"✅ 交互式动画已停止":"⚠️ 没有正在运行的交互式动画"),p.value=!1};return(C,y)=>{const v=_n,A=ii,E=Di,S=Ii,R=pr,V=Wa,X=Qa,q=Ja,B=bc,G=Za;return I(),O("div",Ym,[y[21]||(y[21]=M("div",{class:"panel-header"},[M("h3",null,"属性面板")],-1)),M("div",Xm,[t.value===0?(I(),O("div",jm,[w(v,{size:48},{default:P(()=>[w(D(xc))]),_:1}),y[5]||(y[5]=M("p",null,"未选择任何图元",-1))])):t.value===1?(I(),O("div",Hm,[w(Xd,{pen:D(e).pens[0]},null,8,["pen"])])):t.value>=2&&r.value?(I(),O("div",Wm,[y[19]||(y[19]=M("h4",null,"多图元路径动画控制",-1)),M("div",Qm,[M("p",null,"已选择 "+z(t.value)+" 个图元，包含 "+z(i.value)+" 台小车和 "+z(n.value)+" 个路径元素。",1),M("div",Km,[w(A,{type:"primary",onClick:g},{default:P(()=>y[6]||(y[6]=[N("开始动画",-1)])),_:1,__:[6]}),w(A,{type:"danger",onClick:d},{default:P(()=>y[7]||(y[7]=[N("停止动画",-1)])),_:1,__:[7]})]),M("div",Jm,[M("div",Zm,[y[8]||(y[8]=M("label",null,"运动次数:",-1)),w(S,{modelValue:u.value,"onUpdate:modelValue":y[0]||(y[0]=$=>u.value=$),style:{width:"120px"}},{default:P(()=>[w(E,{label:"10次",value:10}),w(E,{label:"50次",value:50}),w(E,{label:"100次",value:100}),w(E,{label:"200次",value:200}),w(E,{label:"500次",value:500}),w(E,{label:"无限循环",value:-1})]),_:1},8,["modelValue"])]),M("div",ep,[y[9]||(y[9]=M("label",null,"动画速度:",-1)),w(R,{modelValue:o.value,"onUpdate:modelValue":y[1]||(y[1]=$=>o.value=$),min:.1,max:3,step:.1,onChange:m,style:{width:"120px"}},null,8,["modelValue"]),M("span",null,z(o.value)+"x",1)]),M("div",tp,[y[10]||(y[10]=M("label",null,"缓动类型:",-1)),w(S,{modelValue:c.value,"onUpdate:modelValue":y[2]||(y[2]=$=>c.value=$),onChange:_,style:{width:"140px"}},{default:P(()=>[w(E,{label:"线性",value:"linear"}),w(E,{label:"缓入缓出",value:"power2.inOut"}),w(E,{label:"缓入",value:"power2.in"}),w(E,{label:"缓出",value:"power2.out"}),w(E,{label:"弹性",value:"elastic.out"}),w(E,{label:"反弹",value:"bounce.out"})]),_:1},8,["modelValue"])]),M("div",ip,[y[13]||(y[13]=M("label",null,"移动方向:",-1)),w(X,{modelValue:l.value,"onUpdate:modelValue":y[3]||(y[3]=$=>l.value=$),onChange:x},{default:P(()=>[w(V,{value:"forward"},{default:P(()=>y[11]||(y[11]=[N("正向",-1)])),_:1,__:[11]}),w(V,{value:"reverse"},{default:P(()=>y[12]||(y[12]=[N("反向",-1)])),_:1,__:[12]})]),_:1},8,["modelValue"])]),M("div",np,[w(q,{modelValue:h.value,"onUpdate:modelValue":y[4]||(y[4]=$=>h.value=$),onChange:b},{default:P(()=>y[14]||(y[14]=[N(" 自动旋转 ",-1)])),_:1,__:[14]},8,["modelValue"])]),f.value.isRunning?(I(),O("div",sp,[M("div",rp,[M("span",null,"进度: "+z(f.value.current)+"/"+z(f.value.total===-1?"∞":f.value.total),1),M("span",ap,z(f.value.percentage)+"%",1)]),M("div",op,[M("span",null,"运行中的小车: "+z(f.value.activeVehicles)+" 台",1)]),w(B,{percentage:f.value.percentage,"show-text":!1,"stroke-width":6},null,8,["percentage"])])):Q("",!0)]),M("div",lp,[y[17]||(y[17]=M("h4",null,"🎯 交互式路径动画",-1)),y[18]||(y[18]=M("p",{class:"section-description"},"支持转折点决策的智能路径动画系统",-1)),M("div",cp,[w(A,{type:"primary",onClick:T,disabled:p.value,loading:p.value},{default:P(()=>[N(z(p.value?"运行中...":"🚀 启动交互动画"),1)]),_:1},8,["disabled","loading"]),w(A,{type:"danger",onClick:k,disabled:!p.value},{default:P(()=>y[15]||(y[15]=[N(" ⏹️ 停止交互动画 ",-1)])),_:1,__:[15]},8,["disabled"])]),p.value?(I(),O("div",hp,[w(G,{title:"交互式动画运行中",type:"info",closable:!1,"show-icon":""},{default:P(()=>y[16]||(y[16]=[M("p",null,"小车正在沿轨道移动，到达转折点时会弹出选择对话框",-1),M("p",null,[M("strong",null,"黑色轨道"),N(" → 转折点 → "),M("strong",null,"蓝色轨道"),N(" 或 继续黑色轨道")],-1)])),_:1})])):Q("",!0)])])])):(I(),O("div",up,[w(v,{size:48},{default:P(()=>[w(D(wc))]),_:1}),M("p",null,"已选择 "+z(t.value)+" 个图元",1),y[20]||(y[20]=M("p",{class:"hint"},"请选择包含小车和路径元素的组合来启用动画功能",-1))]))])])}}}),fp=Be(dp,[["__scopeId","data-v-7acd1a24"]]),mp={class:"divider"},Fa=180,La=200,pp=Ze({__name:"Contextmenu",setup(s){Ac(d=>({"21c52a7c":o.value.top,"16dc5bc0":o.value.left,"6dde6e19":o.value.visible}));const{selections:e}=Oi();let t=U(!1),i=U([]),n=U(null),r=gi({top:-9999,left:-9999,visible:!1});const a=Xe(()=>e.pens?(Array.isArray(e.pens)?e.pens:[e.pens]).every(m=>m&&m.locked===2):!1),o=Xe(()=>({top:r.top+"px",left:r.left+"px",visible:r.visible?"visible":"hidden"}));jt(e,d=>{t.value=Array.isArray(d.pens)?d.pens.length>0:!!d.pens,i.value=Array.isArray(d.pens)?d.pens:d.pens?[d.pens]:[]}),ti(()=>{const d=document.querySelector(".meta2d-canvas-container");d?d.addEventListener("contextmenu",m=>{var y;m.preventDefault();const _=d.clientWidth,x=d.clientHeight,b=m.clientX,T=m.clientY;let k=b+10,C=T+10;k+Fa>_&&(k=Math.max(0,b-Fa)),C+La>x&&(C=Math.max(0,T-La)),r.top=C,r.left=k,r.visible=!0,(y=n.value)==null||y.focus()}):console.warn("Canvas container not found")}),window.addEventListener("contextmenu",d=>{var m;d.preventDefault(),(m=n.value)==null||m.focus()});function c(d){switch(d){case"top":meta2d.top();break;case"bottom":meta2d.bottom();break;case"up":meta2d.up();break;case"down":meta2d.down();break}meta2d.render(),n.value&&n.value.blur()}function l(){var d,m;(d=e.pens)==null||d.forEach(_=>{_&&(_.locked=2)}),(m=n.value)==null||m.blur()}function h(){var d,m;(d=e.pens)==null||d.forEach(_=>{_&&(_.locked=0)}),(m=n.value)==null||m.blur()}function u(){var d;meta2d.paste(),(d=n.value)==null||d.blur()}function f(){var d;meta2d.copy(),(d=n.value)==null||d.blur()}function p(){var d;meta2d.cut(),(d=n.value)==null||d.blur()}function g(){r.visible=!1}return(d,m)=>(I(),O("div",{class:"contextmenu",ref_key:"ctxMenu",ref:n,tabindex:"-1",onBlur:g},[At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[0]||(m[0]=_=>!a.value&&c("top"))},"置顶",2),[[Ct,D(t)]]),At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[1]||(m[1]=_=>!a.value&&c("bottom"))},"置底",2),[[Ct,D(t)]]),At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[2]||(m[2]=_=>!a.value&&c("up"))},"上一图层",2),[[Ct,D(t)]]),At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[3]||(m[3]=_=>!a.value&&c("down"))},"下一图层",2),[[Ct,D(t)]]),m[9]||(m[9]=M("div",{class:"divider"},null,-1)),D(t)&&!a.value?(I(),O("div",{key:0,class:"ctx_item",onClick:l},"锁定")):Q("",!0),D(t)&&a.value?(I(),O("div",{key:1,class:"ctx_item",onClick:h},"解锁")):Q("",!0),At(M("div",mp,null,512),[[Ct,D(t)]]),At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[4]||(m[4]=_=>!a.value&&p)},m[6]||(m[6]=[N(" 剪切",-1),M("span",{class:"shortcut"},"Ctrl + X",-1)]),2),[[Ct,D(t)]]),At(M("div",{class:Ge(["ctx_item",{ctx_item_disabled:a.value}]),onClick:m[5]||(m[5]=_=>!a.value&&f)},m[7]||(m[7]=[N(" 复制",-1),M("span",{class:"shortcut"},"Ctrl + C",-1)]),2),[[Ct,D(t)]]),M("div",{class:"ctx_item",onClick:u},m[8]||(m[8]=[N(" 粘贴",-1),M("span",{class:"shortcut"},"Ctrl + V",-1)]))],544))}}),_p=Be(pp,[["__scopeId","data-v-3ed1c4af"]]),gp=[{name:"鼠标移入",event:"enter"},{name:"鼠标移出",event:"leave"},{name:"选中",event:"active"},{name:"取消选中",event:"inactive"},{name:"单击",event:"click"},{name:"双击",event:"dbclick"}],Os=[{name:"打开链接",behavior:Vt.Link,depend:[{name:"链接地址",type:"input",bindProp:"value",option:{placeholder:"URL"},bindData:""},{name:"打开方式",type:"select",bindProp:"params",option:{list:[{name:"新窗口打开",value:"_blank"},{name:"覆盖当前页面",value:"self"}]},bindData:""}]},{name:"执行动画",behavior:Vt.StartAnimate,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]},{name:"暂停动画",behavior:Vt.PauseAnimate,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]},{name:"停止动画",behavior:Vt.StopAnimate,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]},{name:"播放视频",behavior:Vt.StartVideo,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]},{name:"暂停视频",behavior:Vt.PauseVideo,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]},{name:"停止视频",behavior:Vt.StopVideo,depend:[{name:"目标id/tag",type:"input",bindProp:"value",option:{placeholder:"id/tag"},bindData:""}]}],yp={class:"event-panel"},vp={key:0,class:"event-cards"},xp={class:"event-card-header"},bp={class:"event-card-title"},wp={class:"event-card-actions"},Ap={key:0,class:"event-card-content"},Cp={key:2},Tp={key:1,class:"empty-state"},kp={__name:"EventPanel",setup(s){ni();const e=vt(),{selections:t}=Oi();let i=gi({}),n=U([]),r=U({}),a=U(-1);gi(Os);const o=U(new Map),c=Xe(()=>{var x;if(!Object.keys(i).length)return!1;const m=((x=i.events)==null?void 0:x.length)||0;return n.value.length-m<1}),l=()=>{const m=e.meta2dApp;if(!m)return;const x={...m.data(),mqtt:m.mqtt||{},mqttOptions:m.mqttOptions||{},mqttTopics:m.mqttTopics||[],https:m.https||[],websocket:m.websocket||"",websocketProtocols:m.websocketProtocols||[]};tn.save(x)};jt(()=>t,m=>{Object.keys(i).length>0&&i.id&&o.value.set(i.id,{...r.value}),m.mode===dt.Pen&&m.pens?(i=m.pens[0],i.events?n.value=d(i.events):(i.events=[],n.value=[]),r.value=o.value.get(i.id)||{}):(i={},n.value=[],r.value={})},{immediate:!0,deep:!0});function h(m){if(!n||m>=n.length)return;n.value.splice(m,1),i.events&&m<i.events.length?(i.events.splice(m,1),l(),j.success("事件已从图元中删除")):j.success("事件已删除"),delete r.value[m];const _={};Object.keys(r.value).forEach(x=>{const b=parseInt(x);b>m?_[b-1]=r.value[b]:_[b]=r.value[b]}),r.value=_}function u(m){r.value[m]=!r.value[m]}function f(m,_){const x=Os.find(b=>b.behavior===m);return!x||!x.depend?(_.reactiveDependencies||(_.reactiveDependencies={}),[]):(_.reactiveDependencies||(_.reactiveDependencies={}),x.depend.map(b=>{const T=b.bindProp;if(_.reactiveDependencies[T])return _.reactiveDependencies[T];const k=d(b);k.bindData=_[b.bindProp]!==void 0?_[b.bindProp]:k.bindData||"";const C=gi(k);return _.reactiveDependencies[T]=C,C}))}async function p(m){if(!n||m>=n.value.length)return;const _=n.value[m];if(!_.name||!(_.action!==void 0&&_.action!==null)){j.warning("请填写事件类型和行为");return}a.value=m;try{const x=f(_.action,_);Array.isArray(x)&&(_.dependencies=x.map(T=>({name:T.name,type:T.type,bindProp:T.bindProp,bindData:T.bindData||""})),x.forEach(T=>{_[T.bindProp]=T.bindData||""})),i.events||(i.events=[]);const b={..._};delete b.reactiveDependencies,delete b.dependencies,i.events[m]=b,l(),j.success("事件已保存")}catch(x){j.error("保存失败，请重试"),console.error("保存事件失败:",x)}finally{a.value=-1}}function g(){if(!Object.keys(i).length){j.warning("请先选择一个图元");return}if(!c.value){j.warning("已达到最大可编辑事件数量");return}const m={name:"",action:"",reactiveDependencies:{}};n.value.push(m);const _=n.value.length-1;r.value[_]=!0}function d(m){if(m===null||typeof m!="object")return m;const _=Array.isArray(m)?[]:{};for(const x in m)Object.prototype.hasOwnProperty.call(m,x)&&(_[x]=d(m[x]));return _}return fn(()=>{o.value.clear()}),(m,_)=>{const x=ii,b=_n,T=Di,k=Ii,C=Ri,y=mn,v=Ei;return I(),O("div",yp,[w(x,{onClick:g,type:"primary",style:{width:"100%","margin-bottom":"16px"},disabled:!c.value},{default:P(()=>_[1]||(_[1]=[N(" 添加事件 ",-1)])),_:1,__:[1]},8,["disabled"]),D(n)&&D(n).length>0?(I(),O("div",vp,[(I(!0),O(J,null,ie(D(n),(A,E)=>(I(),O("div",{class:"event-card",key:E},[M("div",xp,[M("div",bp,"事件"+z(E+1),1),M("div",wp,[w(x,{onClick:Bi(S=>p(E),["stop"]),type:"primary",size:"small",icon:"Check",loading:D(a)===E},{default:P(()=>_[2]||(_[2]=[N(" 保存 ",-1)])),_:2,__:[2]},1032,["onClick","loading"]),w(x,{onClick:Bi(S=>h(E),["stop"]),type:"danger",size:"small",icon:"Delete"},{default:P(()=>_[3]||(_[3]=[N(" 删除 ",-1)])),_:2,__:[3]},1032,["onClick"]),w(b,{class:Ge(["expand-icon",{"rotate-180":D(r)[E]}]),onClick:Bi(S=>u(E),["stop"])},{default:P(()=>[w(D(Cc))]),_:2},1032,["class","onClick"])])]),D(r)[E]?(I(),O("div",Ap,[w(v,{onSubmit:_[0]||(_[0]=Bi(()=>{},["prevent"]))},{default:P(()=>[w(C,{label:"事件类型"},{default:P(()=>[w(k,{modelValue:A.name,"onUpdate:modelValue":S=>A.name=S,placeholder:"选择事件类型"},{default:P(()=>[(I(!0),O(J,null,ie(D(gp),S=>(I(),W(T,{key:S.event,label:S.name,value:S.event},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),w(C,{label:"事件行为"},{default:P(()=>[w(k,{modelValue:A.action,"onUpdate:modelValue":S=>A.action=S,placeholder:"选择行为类型"},{default:P(()=>[(I(!0),O(J,null,ie(D(Os),S=>(I(),W(T,{key:S.behavior,label:S.name,value:S.behavior},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),A.action!==void 0&&A.action!==null?(I(!0),O(J,{key:0},ie(f(A.action,A),S=>(I(),W(C,{key:S.name,label:S.name},{default:P(()=>{var R,V,X;return[S.type==="input"?(I(),W(y,Tc({key:0,modelValue:S.bindData,"onUpdate:modelValue":q=>S.bindData=q,placeholder:((R=S.option)==null?void 0:R.placeholder)||"请输入",type:((V=S.option)==null?void 0:V.type)||"text"},{[kc(S.event)]:S.func}),null,16,["modelValue","onUpdate:modelValue","placeholder","type"])):S.type==="select"?(I(),W(k,{key:1,modelValue:S.bindData,"onUpdate:modelValue":q=>S.bindData=q,placeholder:((X=S.option)==null?void 0:X.placeholder)||"请选择"},{default:P(()=>{var q;return[(I(!0),O(J,null,ie(((q=S.option)==null?void 0:q.list)||[],B=>(I(),W(T,{key:B.value,label:B.name,value:B.value,disabled:B.disabled||!1},null,8,["label","value","disabled"]))),128))]}),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):(I(),O("div",Cp," 不支持的输入类型: "+z(S.type),1))]}),_:2},1032,["label"]))),128)):Q("",!0)]),_:2},1024)])):Q("",!0)]))),128))])):Object.keys(D(i)).length===0?(I(),O("div",Tp,_[4]||(_[4]=[M("p",null,"请先选择一个图元",-1)]))):Q("",!0)])}}},Pp=Be(kp,[["__scopeId","data-v-3261814a"]]),Mp=""+new URL("SL_logo-left_purple_web-BM4sF-67.png",import.meta.url).href;var ec={exports:{}};(function(s,e){(function(t,i){i()})($i,function(){function t(l,h){return typeof h>"u"?h={autoBom:!1}:typeof h!="object"&&(console.warn("Deprecated: Expected third argument to be a object"),h={autoBom:!h}),h.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(l.type)?new Blob(["\uFEFF",l],{type:l.type}):l}function i(l,h,u){var f=new XMLHttpRequest;f.open("GET",l),f.responseType="blob",f.onload=function(){c(f.response,h,u)},f.onerror=function(){console.error("could not download file")},f.send()}function n(l){var h=new XMLHttpRequest;h.open("HEAD",l,!1);try{h.send()}catch{}return 200<=h.status&&299>=h.status}function r(l){try{l.dispatchEvent(new MouseEvent("click"))}catch{var h=document.createEvent("MouseEvents");h.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),l.dispatchEvent(h)}}var a=typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof $i=="object"&&$i.global===$i?$i:void 0,o=a.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),c=a.saveAs||(typeof window!="object"||window!==a?function(){}:"download"in HTMLAnchorElement.prototype&&!o?function(l,h,u){var f=a.URL||a.webkitURL,p=document.createElement("a");h=h||l.name||"download",p.download=h,p.rel="noopener",typeof l=="string"?(p.href=l,p.origin===location.origin?r(p):n(p.href)?i(l,h,u):r(p,p.target="_blank")):(p.href=f.createObjectURL(l),setTimeout(function(){f.revokeObjectURL(p.href)},4e4),setTimeout(function(){r(p)},0))}:"msSaveOrOpenBlob"in navigator?function(l,h,u){if(h=h||l.name||"download",typeof l!="string")navigator.msSaveOrOpenBlob(t(l,u),h);else if(n(l))i(l,h,u);else{var f=document.createElement("a");f.href=l,f.target="_blank",setTimeout(function(){r(f)})}}:function(l,h,u,f){if(f=f||open("","_blank"),f&&(f.document.title=f.document.body.innerText="downloading..."),typeof l=="string")return i(l,h,u);var p=l.type==="application/octet-stream",g=/constructor/i.test(a.HTMLElement)||a.safari,d=/CriOS\/[\d]+/.test(navigator.userAgent);if((d||p&&g||o)&&typeof FileReader<"u"){var m=new FileReader;m.onloadend=function(){var b=m.result;b=d?b:b.replace(/^data:[^;]*;/,"data:attachment/file;"),f?f.location.href=b:location=b,f=null},m.readAsDataURL(l)}else{var _=a.URL||a.webkitURL,x=_.createObjectURL(l);f?f.location=x:location.href=x,f=null,setTimeout(function(){_.revokeObjectURL(x)},4e4)}});a.saveAs=c.saveAs=c,s.exports=c})})(ec);var Sp=ec.exports;const Ep=async()=>{const s=localStorage.getItem("meta2d")||"";j.info("正在生成文件，请稍候...");const e={meta2d:s},t=await Fc.post("/api/build",e,{responseType:"blob"});if(t.data.size===0){j.error("下载失败：文件内容为空");return}const i=new Blob([t.data],{type:"application/octet-stream"});j.success("文件生成成功,下载中...");const n=new Date,r=n.getHours().toString().padStart(2,"0"),a=n.getMinutes().toString().padStart(2,"0"),o=n.getSeconds().toString().padStart(2,"0"),c=n.getMilliseconds().toString().padStart(3,"0"),l=`${r}${a}${o}${c}.zip`;Sp.saveAs(i,l)},{selections:Ua}=Oi(),oi=ni(),tc=vt();function li(s,e,...t){typeof s=="string"&&s in Ba?(console.log("Calling menuFunc.",s),Ba[s](e,...t)):console.log("act not found in menuFunc:",s)}const fe=()=>{const s=tc.meta2dApp||window.meta2d;return s||null},Rn=s=>{console.warn(`Function ${s} is not implemented.`),j.info(`功能 ${s} 暂未实现`)},Rp=()=>({header:[{key:"newFile",name:"新建",icon:"NewFile",action:"newFile"},{key:"openFile",name:"打开",icon:"OpenFile",action:"openFile"},{key:"saveFile",name:"保存",icon:"SaveFile",action:"saveFile"},{key:"saveAsFile",name:"下载离线包",action:"buildFile"}],editor:[{key:"undo",name:"撤销",icon:"undo",action:"undo"},{key:"redo",name:"重做",icon:"redo",action:"redo"},{key:"selectAll",name:"全选",icon:"selectAll",action:"selectAll"},{key:"delete",name:"删除",icon:"delete",action:"delete"},{key:"combine",name:"组合",icon:"combine",action:"combine"},{key:"uncombine",name:"取消组合",icon:"uncombine",action:"uncombine"},{key:"zoomin",name:"放大",icon:"zoomin",action:"zoomIn"},{key:"zoomout",name:"缩小",icon:"zoomout",action:"zoomOut"},{key:"zoomreset",icon:"refresh",get name(){var s;try{const e=((s=fe())==null?void 0:s.data().scale)||1;return`${Math.round(e*100)}%`}catch(e){return console.error("获取缩放比例时出错:",e),"100%"}},action:"zoomReset"},{key:"fullScreen",name:"全屏",icon:"fullScreen",action:"FullScreen"},{key:"align",name:"对齐方式",icon:"align",children:[{key:"alignLeft",name:"左对齐",icon:"align-left",action:"alignLeft"},{key:"alignRight",name:"右对齐",icon:"align-right",action:"alignRight"},{key:"alignTop",name:"上对齐",icon:"border-top",action:"alignTop"},{key:"alignBottom",name:"下对齐",icon:"border-bottom",action:"alignBottom"},{key:"alignCenter",name:"居中对齐",icon:"align-center",action:"alignCenter"}]}],tool:[{key:"pen",name:"钢笔",icon:"pencil",action:"usePen"},{key:"pencil",name:"铅笔",icon:"pen",action:"usePencil"},{key:"addAnchor",name:"添加锚点",icon:"addAnchor",action:"addAnchor"},{key:"removeAnchor",name:"删除锚点",icon:"removeAnchor",action:"removeAnchor"},{key:"address",name:"鹰眼地图",icon:"address",action:"address"}],help:[{key:"shortcut",name:"快捷键",icon:"",action:"shortCut"}]}),di=Rp(),Ba={newFile(){confirm("确定要新建项目吗？当前项目的未保存内容将丢失。")&&(oi.currentProject.nodes=[],oi.currentProject.name="新建项目",oi.clearSelection())},async openFile(){const s=document.createElement("input");return s.type="file",s.accept=".json",new Promise(e=>{s.onchange=async t=>{var n;const i=(n=t.target.files)==null?void 0:n[0];if(i){const r=new FileReader;r.onload=async a=>{var c;const o=(c=a.target)==null?void 0:c.result;try{if(oi.importProject(o)){const l=fe();if(l){const h=JSON.parse(o);if(!(l!=null&&l.open))throw new Error("Meta2d instance not initialized properly");l.clear(),await l.open(h),l.centerView(),j.success("项目导入成功")}else j.error("画布未初始化")}else j.error("项目导入失败")}catch(l){console.error("导入项目出错:",l),j.error("导入项目失败：格式错误")}e()},r.readAsText(i)}else e()},s.click()})},loadFile:async()=>{Rn("loadFile")},saveFile:function(){try{const s=oi.exportProject(),e=new Blob([s],{type:"application/json"}),t=URL.createObjectURL(e),i=document.createElement("a");i.href=t,i.download=`${oi.currentProject.name}.json`,i.click(),URL.revokeObjectURL(t),j.success("项目保存成功")}catch(s){console.error("保存文件时出错:",s),j.error("保存文件失败")}},buildFile:function(){j.info("正在构建项目，请稍候..."),Ep()},copy:function(){const s=fe();s?s.copy():j.warning("画布未初始化")},cut:function(){const s=fe();s?s.cut():j.warning("画布未初始化")},paste:function(){const s=fe();s?s.paste():j.warning("画布未初始化")},undo:function(){const s=fe();s&&s.undo()},redo:function(){const s=fe();s&&s.redo()},selectAll:function(){const s=fe();s&&s.activeAll()},delete:function(){const s=fe();s&&s.delete()},down:function(){const s=fe();s&&s.down()},up:function(){Rn("up")},left:function(){Rn("left")},right:function(){Rn("right")},combine:function(){const s=fe();s&&Ua.pens&&s.combine(Ua.pens,void 0,!0)},uncombine:function(){const s=fe();s&&s.clearCombine()},zoomIn:function(){const s=fe();if(s){const e=s.data().scale||1,t=Math.min(e*1.2,1.5,2,3);s.scale(t)}},zoomOut:function(){const s=fe();if(s){const e=s.data().scale||1,t=Math.max(e*.8,.5,.1);s.scale(t)}},zoomReset:function(){const s=fe();s&&(s.scale(1),s.centerView())},FullScreen:function(){const s=fe(),e=!!document.fullscreenElement,t=()=>di.editor.find(n=>n.key==="fullScreen"),i=n=>{document.body.style.cursor=n?"default":"";const r=t();r&&(r.icon=n?"aim":"fullScreen",r.name=n?"退出全屏":"全屏"),s&&s.render()};e?document.exitFullscreen&&document.exitFullscreen().then(()=>i(!1)).catch(n=>{console.error("退出全屏模式失败:",n),j.error("退出全屏模式失败")}):document.documentElement.requestFullscreen?document.documentElement.requestFullscreen().then(()=>i(!0)).catch(n=>{console.error("进入全屏模式失败:",n),j.error("进入全屏模式失败")}):j.warning("您的浏览器不支持全屏功能")},saveAs:function(s){throw new Error("Function not implemented.")},alignLeft:function(){meta2d.alignNodesByFirst("left",meta2d.store.active)},alignRight:function(){meta2d.alignNodesByFirst("right",meta2d.store.active)},alignTop:function(){meta2d.alignNodesByFirst("top",meta2d.store.active)},alignBottom:function(){meta2d.alignNodesByFirst("bottom",meta2d.store.active)},alignCenter:function(){meta2d.alignNodesByFirst("center",meta2d.store.active)},usePen:function(){console.log(document.getElementById("__svg__icons__dom__")),meta2d.canvas.drawingLineName?(meta2d.drawLine(),meta2d.finishPencil()):meta2d.drawLine("curve")},usePencil:function(){meta2d.canvas.pencil?(meta2d.stopPencil(),meta2d.finishPencil()):meta2d.drawingPencil()},shortCut:function(){Lc.push("/shortcuts")},addAnchor:function(){const s=fe();s?s.toggleAnchorMode():j.warning("画布未初始化")},removeAnchor:function(){const s=fe();s?s.toggleAnchorMode():j.warning("画布未初始化")},address:function(){const s=fe();s?tc.toggleMap()?s.showMap():s.hideMap():j.warning("画布未初始化")}},Ip={class:"header-container"},Dp={class:"header"},Op={class:"header-actions"},$p={class:"user-info-wrapper"},Vp={class:"user-info"},Fp=["src"],Lp={class:"username"},Up={class:"header-tool"},Bp={class:"toolbar-left"},Np={class:"dropdown-trigger"},qp={class:"l-icon cursor-pointer","aria-hidden":"true"},zp=["xlink:href"],Gp={key:0,class:"mr-1 l-icon","aria-hidden":"true",style:{width:"14px",height:"14px"}},Yp=["xlink:href"],Xp=["onClick"],jp=["xlink:href"],Hp=["onClick"],Wp=["xlink:href"],Qp={class:"toolbar-center"},Kp={class:"project-name"},Jp={class:"toolbar-right"},Zp={class:"toolbar-right-group"},e_={key:0,class:"add-mode-indicator"},t_={class:"toolbar-right-group"},i_=Ze({__name:"Header",setup(s){const e=ni(),t=U(0),i=vt();eo();const n=Uc(),r=Bc();i.currentCanvas,i.switchCanvas;const a=()=>{console.log("查看用户信息",n.getUserInfo)},o=async()=>{fi.confirm("是否确认退出登录？",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{await n.logout().then(()=>{j.success("已退出登录"),r.push("/login")}).catch(h=>{j.error("退出登录失败，请重试"),console.error("Logout error:",h)})}).catch(()=>{})};ti(()=>{const h=setInterval(()=>{meta2d&&(clearInterval(h),c(meta2d.store.data.scale),meta2d.on("scale",c))},200)});const c=h=>{t.value=Math.round(h*100)},l=()=>{window.open("/preview","_blank")};return(h,u)=>{const f=ii,p=Sc,g=_n,d=Pc,m=Mc;return I(),O("div",Ip,[M("div",Dp,[u[10]||(u[10]=M("div",{class:"logo"},[M("img",{src:Mp})],-1)),M("div",Op,[w(D(Gt),{onCommand:u[0]||(u[0]=_=>D(li)(_.action,_.value))},{dropdown:P(()=>[w(D(Yt),null,{default:P(()=>[(I(!0),O(J,null,ie(D(di).header,(_,x)=>(I(),W(D(Tt),{key:x,command:_},{default:P(()=>[N(z(_.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:P(()=>[w(f,null,{default:P(()=>u[5]||(u[5]=[N(" 文件 ",-1)])),_:1,__:[5]})]),_:1}),w(D(Gt),{onCommand:u[1]||(u[1]=_=>D(li)(_.action,_.value))},{dropdown:P(()=>[w(D(Yt),null,{default:P(()=>[(I(!0),O(J,null,ie(D(di).tool,(_,x)=>(I(),W(D(Tt),{key:x,command:_},{default:P(()=>[N(z(_.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:P(()=>[w(f,null,{default:P(()=>u[6]||(u[6]=[N(" 编辑 ",-1)])),_:1,__:[6]})]),_:1}),w(D(Gt),{onCommand:u[2]||(u[2]=_=>D(li)(_.action,_.value))},{dropdown:P(()=>[w(D(Yt),null,{default:P(()=>[(I(!0),O(J,null,ie(D(di).help,(_,x)=>(I(),W(D(Tt),{key:x,command:_},{default:P(()=>[N(z(_.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:P(()=>[w(f,null,{default:P(()=>u[7]||(u[7]=[N(" 帮助 ",-1)])),_:1,__:[7]})]),_:1})]),M("div",$p,[w(D(Gt),{trigger:"hover",placement:"bottom-end"},{dropdown:P(()=>[w(D(Yt),null,{default:P(()=>[w(D(Tt),{onClick:a},{default:P(()=>u[8]||(u[8]=[N("用户信息",-1)])),_:1,__:[8]}),w(D(Tt),{onClick:o},{default:P(()=>u[9]||(u[9]=[N("退出登录",-1)])),_:1,__:[9]})]),_:1})]),default:P(()=>{var _;return[M("div",Vp,[M("img",{src:((_=D(n).getUserInfo.userInfo)==null?void 0:_.avatar)||"src/assets/defaultAvatar.png",class:"avatar"},null,8,Fp),M("span",Lp,z(D(n).getUserInfo.userInfo.username||"未知用户"),1)])]}),_:1})])]),u[14]||(u[14]=M("div",{class:"divider"},null,-1)),M("div",Up,[M("div",Bp,[(I(!0),O(J,null,ie(D(di).editor,(_,x)=>(I(),O("div",{key:_.key},[_.children&&_.children.length>0?(I(),W(D(Gt),{key:0,onCommand:u[3]||(u[3]=b=>D(li)(b.action,b.value)),trigger:"click",placement:"bottom"},{dropdown:P(()=>[w(D(Yt),null,{default:P(()=>[(I(!0),O(J,null,ie(_.children,b=>(I(),W(D(Tt),{key:b.key,command:b},{default:P(()=>[b.icon?(I(),O("svg",Gp,[M("use",{"xlink:href":"#i-"+b.icon},null,8,Yp)])):Q("",!0),N(" "+z(b.name),1)]),_:2},1032,["command"]))),128))]),_:2},1024)]),default:P(()=>[M("div",Np,[w(p,{content:_.name,index:x+1+""},{default:P(()=>[(I(),O("svg",qp,[M("use",{"xlink:href":"#i-"+_.icon},null,8,zp)]))]),_:2},1032,["content","index"])])]),_:2},1024)):(I(),W(p,{key:1,content:_.name,index:x+1+""},{default:P(()=>[(I(),O("svg",{class:"l-icon cursor-pointer","aria-hidden":"true",onClick:b=>D(li)(_.action,_.value)},[M("use",{"xlink:href":"#i-"+_.icon},null,8,jp)],8,Xp))]),_:2},1032,["content","index"]))]))),128)),u[11]||(u[11]=M("div",{class:"vertical-divider"},null,-1)),(I(!0),O(J,null,ie(D(di).tool,(_,x)=>(I(),O("div",{key:_.key},[w(p,{content:_.name,index:x+1+""},{default:P(()=>[(I(),O("svg",{class:Ge(["l-icon cursor-pointer",{pressed:_.key==="address"&&D(i).showMap}]),"aria-hidden":"true",onClick:b=>D(li)(_.action,_.value)},[M("use",{"xlink:href":"#i-"+_.icon},null,8,Wp)],10,Hp))]),_:2},1032,["content","index"])]))),128))]),M("div",Qp,[M("span",Kp,z(D(e).currentProject.name),1)]),M("div",Jp,[M("div",Zp,[D(e).addMode.isActive?(I(),O("div",e_,[w(d,{type:"warning",effect:"dark",closable:"",onClose:u[4]||(u[4]=_=>D(e).exitAddMode()),size:"small"},{default:P(()=>{var _;return[w(g,{size:"12"},{default:P(()=>[w(D(Ec))]),_:1}),N(" 添加模式: "+z((_=D(e).addMode.selectedTemplate)==null?void 0:_.name),1)]}),_:1}),u[12]||(u[12]=M("span",{class:"add-mode-tip"},"点击画布添加图元，或点击关闭退出",-1))])):Q("",!0)]),M("div",t_,[w(m,null,{default:P(()=>[w(f,{icon:D(Rc),onClick:l,type:"success"},{default:P(()=>u[13]||(u[13]=[N("预览",-1)])),_:1,__:[13]},8,["icon"])]),_:1})])])])])}}}),n_=Be(i_,[["__scopeId","data-v-f15ebe05"]]);class s_{planPath(e,t){console.log(`🗺️ 规划路径: 车辆 -> 轨道${t}`);const i=this.getVehiclePosition(e),n=Gn.getTrackElement(t);return n?this.createDirectPath(i,n):(console.error(`❌ 未找到轨道${t}`),{segments:[],totalDistance:0,valid:!1})}getVehiclePosition(e){const t=meta2d.getPenRect(e);return{x:t.x+t.width/2,y:t.y+t.height/2}}createDirectPath(e,t){const{start:i,end:n}=t.coordinates,r=this.calculateDistance(e,i),a=this.calculateDistance(e,n);console.log(`🎯 我的方案 - 距起点: ${r.toFixed(1)}, 距终点: ${a.toFixed(1)}`);const o=[],c=r<a,l=c?i:n,h=c?n:i,u=c?"forward":"reverse",f=c?r:a;return console.log(`📍 选择最近端点: ${c?"起点":"终点"}, 距离: ${f.toFixed(1)}`),f>15?(console.log(`🚀 添加平滑接近段: 当前位置 → ${c?"起点":"终点"}`),o.push({track:{...t,trackId:-1,mqttId:"smooth_approach"},direction:"forward",startPoint:e,endPoint:l}),console.log(`🛤️ 沿轨道移动: ${c?"起点":"终点"} → ${c?"终点":"起点"} (${u})`),o.push({track:t,direction:u,startPoint:l,endPoint:h})):f>3?(console.log(`✨ 距离很近(${f.toFixed(1)}px)，平滑过渡到轨道`),o.push({track:{...t,trackId:-1,mqttId:"micro_approach"},direction:"forward",startPoint:e,endPoint:l}),o.push({track:t,direction:u,startPoint:l,endPoint:h})):(console.log(`🎯 距离极近(${f.toFixed(1)}px)，直接从当前位置开始移动`),o.push({track:t,direction:u,startPoint:e,endPoint:h})),{segments:o,totalDistance:o.reduce((p,g)=>p+this.calculateDistance(g.startPoint,g.endPoint),0),valid:!0}}pointToLineDistance(e,t,i){const n=e.x-t.x,r=e.y-t.y,a=i.x-t.x,o=i.y-t.y,c=n*a+r*o,l=a*a+o*o;if(l===0)return Math.sqrt(n*n+r*r);let h=c/l,u,f;h<0?(u=t.x,f=t.y):h>1?(u=i.x,f=i.y):(u=t.x+h*a,f=t.y+h*o);const p=e.x-u,g=e.y-f;return Math.sqrt(p*p+g*g)}calculateDistance(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}convertToAnimationPath(e){return e.segments.map((t,i)=>{var n;if(t.track.trackId===-1)return{id:`temp_${i}`,name:"line",mqttId:t.track.mqttId,anchors:[{x:t.startPoint.x,y:t.startPoint.y},{x:t.endPoint.x,y:t.endPoint.y}],calculative:{worldAnchors:[{x:t.startPoint.x,y:t.startPoint.y},{x:t.endPoint.x,y:t.endPoint.y}]}};{const r={...t.track.element};return r.startPoint=t.startPoint,r.endPoint=t.endPoint,r.segmentDirection=t.direction,console.log("🎯 PathPlanningService传递路径段信息:",{id:r.id,direction:t.direction,startPoint:t.startPoint,endPoint:t.endPoint}),t.direction==="reverse"&&(r.anchors=[...r.anchors].reverse(),(n=r.calculative)!=null&&n.worldAnchors&&(r.calculative.worldAnchors=[...r.calculative.worldAnchors].reverse())),r}})}}const Na=new s_;class r_{constructor(){Ie(this,"mqttUnsubscribe",null);Ie(this,"isInitialized",!1)}initialize(){if(this.isInitialized){console.warn("车辆动画控制器已经初始化");return}console.log("🚗 初始化车辆动画控制器..."),this.mqttUnsubscribe=Ae.onMessage(e=>{this.handleMqttMessage(e)}),this.isInitialized=!0,console.log("✅ 车辆动画控制器初始化完成")}async handleMqttMessage(e){console.log("📨 VehicleAnimationController收到MQTT消息:",e);try{switch(e.action){case"move":await this.handleMoveCommand(e);break;case"stop":this.handleStopCommand(e);break;case"wait":this.handleWaitCommand(e);break;default:console.warn("未知的动作类型:",e.action)}}catch(t){console.error("处理MQTT消息时出错:",t)}}async handleMoveCommand(e){const{vehicleId:t,nextTrack:i,speed:n=1}=e;console.log(`🎯 执行移动指令: ${t} -> 轨道${i}`);try{console.log("🔍 开始扫描轨道映射..."),Gn.scanAndBuildTrackMapping();const r=this.findVehicleElement(t);if(!r){console.error(`❌ 未找到车辆${t}`);return}console.log("✅ 找到车辆:",r);const a=Na.planPath(r,i);if(!a.valid){console.error(`❌ 无法规划到轨道${i}的路径`);return}console.log("🗺️ 路径规划成功:",a),Ae.updateVehicleStatus(t,{status:"moving",currentTrack:i,progress:0}),console.log("🛑 停止当前动画..."),en.stopInteractiveAnimation();const o=Na.convertToAnimationPath(a),c={speed:n,ease:"power2.inOut",autoRotate:!0,onPathComplete:()=>{console.log(`🏁 车辆${t}到达轨道${i}`),Ae.updateVehicleStatus(t,{status:"arrived",progress:1})},onProgress:h=>{Ae.updateVehicleStatus(t,{progress:h})}};await en.createInteractiveAnimation(r,o,c)?console.log(`✅ 车辆${t}动画启动成功`):(console.error(`❌ 车辆${t}动画启动失败`),Ae.updateVehicleStatus(t,{status:"idle"}))}catch(r){console.error(`车辆${t}动画执行错误:`,r),Ae.updateVehicleStatus(t,{status:"idle"})}}handleStopCommand(e){const{vehicleId:t}=e;console.log(`⏹️ 执行停止指令: ${t}`),en.stopInteractiveAnimation(),Ae.updateVehicleStatus(t,{status:"idle",progress:0})}handleWaitCommand(e){const{vehicleId:t}=e;console.log(`⏸️ 执行等待指令: ${t}`),Ae.updateVehicleStatus(t,{status:"waiting"})}findVehicleElement(e){var n,r,a;const t=((a=(r=(n=window.meta2d)==null?void 0:n.store)==null?void 0:r.data)==null?void 0:a.pens)||[];let i=t.find(o=>{var c;return((c=o.hcmsData)==null?void 0:c.vehicleId)===e});return i?(console.log(`🎯 通过vehicleId找到车辆: ${e}`),i):(i=t.find(o=>this.isVehicleElement(o)),i?(console.log(`🎯 通过检测逻辑找到车辆: ${i.name||i.id}`),i):null)}isVehicleElement(e){var o,c,l,h;const t=((o=e.name)==null?void 0:o.toLowerCase())||"",i=((c=e.tags)==null?void 0:c.join(" ").toLowerCase())||"";return((l=e.hcmsData)==null?void 0:l.vehicleId)?!0:t==="circle"&&((h=e.hcmsData)!=null&&h.trackId)?!1:["vehicle","car","amr","robot"].some(u=>t.includes(u)||i.includes(u))?!0:t==="square"}destroy(){this.mqttUnsubscribe&&(this.mqttUnsubscribe(),this.mqttUnsubscribe=null),this.isInitialized=!1,console.log("🗑️ 车辆动画控制器已销毁")}}const qa=new r_,a_={class:"editor-container"},o_={class:"toolbar"},l_={class:"main-content"},c_={class:"canvas-area",role:"main","aria-label":"画布编辑区域"},h_={class:"status-bar"},u_={class:"status-left"},d_=["aria-label","title"],f_={class:"status-info"},m_={class:"status-info"},p_={class:"status-right"},__=["aria-label","title"],g_=1e3,y_=Ze({__name:"Meta2D",setup(s){const e=ni(),t=vt(),i=eo();Oc();const n=U("meta2d"),{selections:r}=Oi(),a=U(!0),o=U(!0),c=U(null),l=U(0),h=U(0),u=()=>{a.value=!a.value,setTimeout(()=>{const x=t.meta2dApp;x&&x.fitView()},300)},f=()=>{o.value=!o.value,setTimeout(()=>{const x=t.meta2dApp;x&&x.fitView()},300)};jt(()=>r.mode,x=>{const b=x===dt.File?"properties":"pen";i.setActiveRightTab(b)},{immediate:!0});const p=["scale","opened","undo","redo","add","delete","rotatePens","translatePens"],g=()=>{c.value&&clearTimeout(c.value),c.value=setTimeout(()=>{const x=t.meta2dApp;if(x)try{const b=x.data();l.value=b.scale,h.value=b.pens.length;const T={...b,mqtt:x.mqtt||{},mqttOptions:x.mqttOptions||{},mqttTopics:x.mqttTopics||[],https:x.https||[],websocket:x.websocket||"",websocketProtocols:x.websocketProtocols||[]};tn.save(T)}catch(b){console.error("自动保存失败:",b)}finally{c.value=null}},g_)},d=()=>{const x=t.meta2dApp;if(!x){console.warn("Meta2D 实例未初始化，无法设置自动保存");return}console.log("设置自动保存监听器"),p.forEach(b=>{x.on(b,g)})},m=()=>{const x=t.meta2dApp;x&&p.forEach(b=>{x.off(b,g)})},_=()=>{c.value&&(clearTimeout(c.value),c.value=null),m()};return ti(()=>{jt(()=>t.meta2dApp,x=>{x&&(console.log("Meta2D 实例已初始化，设置自动保存"),d(),qa.initialize())},{immediate:!0}),t.syncNodesToCanvas(),jt(()=>e.currentProject.nodes,()=>{t.syncNodesToCanvas()},{deep:!0})}),fn(()=>{_(),qa.destroy()}),(x,b)=>{const T=Dc,k=Ic,C=_n,y=Ya;return I(),O("div",a_,[M("div",o_,[w(n_)]),M("div",l_,[M("div",{class:Ge(["left-panel",{collapsed:!a.value}]),role:"complementary","aria-label":"图元库面板"},[At(w(oh,null,null,512),[[Ct,a.value]])],2),M("div",c_,[(I(),W(Ka(n.value==="meta2d"?$c:Pu),{key:n.value})),w(_p)]),M("div",{class:Ge(["right-panel",{collapsed:!o.value}]),role:"complementary","aria-label":"属性面板"},[o.value?(I(),W(k,{key:0,modelValue:D(i).activeRightTab,"onUpdate:modelValue":b[0]||(b[0]=v=>D(i).activeRightTab=v),type:"border-card"},{default:P(()=>[D(r).mode===D(dt).File?(I(),W(T,{key:0,label:"图纸",name:"properties"},{default:P(()=>[w(Eu)]),_:1})):Q("",!0),D(r).mode===D(dt).Pen?(I(),W(T,{key:1,label:"外观",name:"pen"},{default:P(()=>[w(fp)]),_:1})):Q("",!0),D(r).mode===D(dt).Pen?(I(),W(T,{key:2,label:"事件",name:"stype"},{default:P(()=>[w(Pp)]),_:1})):Q("",!0),D(r).mode===D(dt).Pen?(I(),W(T,{key:3,label:"数据",name:"data"},{default:P(()=>[w(zu)]),_:1})):Q("",!0),D(r).mode===D(dt).Pen?(I(),W(T,{key:4,label:"动画",name:"animation"},{default:P(()=>[w(id)]),_:1})):Q("",!0),D(r).mode===D(dt).File?(I(),W(T,{key:5,label:"通信",name:"communication"},{default:P(()=>[w(kd)]),_:1})):Q("",!0),D(r).mode===D(dt).File?(I(),W(T,{key:6,label:"MQTT测试",name:"mqtt"},{default:P(()=>[w(zd)]),_:1})):Q("",!0)]),_:1},8,["modelValue"])):Q("",!0)],2)]),M("div",h_,[M("div",u_,[M("button",{class:"panel-toggle-btn",type:"button",onClick:u,"aria-label":a.value?"收起左侧面板":"展开左侧面板",title:a.value?"收起左侧面板":"展开左侧面板"},[w(C,null,{default:P(()=>[a.value?(I(),W(D(Br),{key:0})):(I(),W(D(Nr),{key:1}))]),_:1})],8,d_),w(y,{direction:"vertical"}),M("span",f_," 缩放: "+z(Math.round(l.value*100))+"% ",1),w(y,{direction:"vertical"}),M("span",m_," 节点数: "+z(h.value),1)]),M("div",p_,[M("span",{class:Ge(["mode-indicator",{"edit-mode":D(e).isEditMode}])},z(D(e).isEditMode?"编辑模式":"预览模式"),3),w(y,{direction:"vertical"}),M("button",{class:"panel-toggle-btn",type:"button",onClick:f,"aria-label":o.value?"收起右侧面板":"展开右侧面板",title:o.value?"收起右侧面板":"展开右侧面板"},[w(C,null,{default:P(()=>[o.value?(I(),W(D(Nr),{key:0})):(I(),W(D(Br),{key:1}))]),_:1})],8,__)])])])}}}),v_=Be(y_,[["__scopeId","data-v-25fcd102"]]),E_=Object.freeze(Object.defineProperty({__proto__:null,default:v_},Symbol.toStringTag,{value:"Module"}));export{Fo as A,zn as B,si as C,Ai as D,de as E,ia as F,gh as G,Nh as H,To as I,Wh as J,S_ as K,hi as L,se as M,iu as N,xh as O,at as P,E_ as Q,ot as R,nn as S,me as T,zs as U,Lo as V,Tn as a,gn as b,Oo as c,Co as d,ht as e,it as f,vh as g,fo as h,It as i,Jh as j,_t as k,vu as l,sa as m,zr as n,xs as o,ra as p,tu as q,Dh as r,ao as s,Dn as t,yu as u,po as v,St as w,Je as x,xt as y,Cu as z};
