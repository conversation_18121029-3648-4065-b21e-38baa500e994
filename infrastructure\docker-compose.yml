services:
  mysql:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/mysql:8.4.5
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mj6CYh8R  # 设置root用户密码
      MYSQL_DATABASE: demo    # 初始化时自动创建一个名为 demo_db 的数据库
      MYSQL_USER: demo       # 初始化用户
      MYSQL_PASSWORD: demo  # 初始化用户密码
    ports:
      - "3306:3306"
    volumes:
      - ./mysql:/var/lib/mysql  # 将容器中的MySQL数据存储目录挂载到宿主机目录（持久化数据）
    networks:
      - hcms_net
 
  postgres:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/postgres:17.5 # Official PostgreSQL image
    container_name: postgres
    environment:
      POSTGRES_USER: postgres # Database username
      POSTGRES_PASSWORD: 6M5yQAtU # Database password
      POSTGRES_DB: demo # Default database name
    ports:
      - "5432:5432" # Map container port to host
    volumes:
      - ./postgres:/var/lib/postgresql/data # Persist data locally
    healthcheck:
      test: ["CMD","pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - hcms_net
  redis:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/redis:6.0.9-alpine
    container_name: redis
    restart: always
    ports:
      - "6379:6379"  
    volumes:
      - ./redis:/data
      - ./redis/redis.conf:/data/redis.conf
    # command: ["redis-server", "--appendonly", "yes", "/etc/redis/redis.conf"]  # 默认开启appendonly模式
    command: ["redis-server", "/data/redis.conf"]  # 默认开启appendonly模式
    networks:
      - hcms_net
  rabbitmq:
    image: rabbitmq:4-management
    container_name: rabbitmq
    restart: on-failure
    # ports:
    #   - "5672:5672"     # amqp port
    #   - "15672:15672"   # http web admin
    #   - "15692:15692"   # http/prometheus
    #   - "15675:15675"   # http/web-mqtt
    #   - "1883:1883"     # mqtt
    volumes:
      - ./rabbitmq/logs:/var/log/rabbitmq
      - ./rabbitmq/data:/var/lib/rabbitmq
      - ./rabbitmq/conf:/etc/rabbitmq
    network_mode: "host"
    # networks:
    #   - hcms_net
    privileged: true
  # rmqnamesrv:
  #   image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apache/rocketmq:5.2.0
  #   container_name: rmqnamesrv
  #   restart: no
  #   # ports:
  #   #   - 9876:9876
  #   environment:
  #     JAVA_OPT_EXT: -server -Xms256m -Xmx256m -Xmn128m
  #   volumes:
  #     - ./rocketmq/namesrv/logs:/home/<USER>/logs
  #     - ./rocketmq/namesrv/store:/home/<USER>/store
  #     - ./rocketmq/broker/conf:/home/<USER>/rocketmq-5.2.0/conf
  #   # networks:
  #   #   - rocketmq
  #   network_mode: "host"
  #   command: sh mqnamesrv
  #   privileged: true
  # rmqbroker:
  #   image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apache/rocketmq:5.2.0
  #   container_name: rmqbroker
  #   restart: no
  #   # ports:
  #   #   - 10909:10909
  #   #   - 10911:10911
  #   #   - 10912:10912
  #   #   - 8200:8200 
  #   #   - 8201:8201
  #   environment:
  #     NAMESRV_ADD: localhost:9876
  #     JAVA_OPT_EXT: -server -Xms300m -Xmx512m -Xmn200m
  #   depends_on:
  #     - rmqnamesrv
  #   volumes:
  #     - ./rocketmq/broker/logs:/home/<USER>/logs
  #     - ./rocketmq/broker/store:/home/<USER>/store
  #     - ./rocketmq/broker/conf:/home/<USER>/rocketmq-5.2.0/conf
  #     - ./rocketmq/broker/bin/runserver.sh:/home/<USER>/rocketmq-5.2.0/bin/runserver.sh
  #   # networks:
  #   #   - rocketmq
  #   network_mode: "host"
  #   command: sh mqbroker --enable-proxy
  #   privileged: true
  # rmqproxy:
  #   image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apache/rocketmq:5.2.0
  #   container_name: rmqproxy
  #   restart: no
  #   depends_on:
  #     - rmqbroker
  #     - rmqnamesrv
  #   ports:
  #     - 8200:8080
  #     - 8201:8081
  #   #restart: on-failure
  #   environment:
  #     - NAMESRV_ADDR=rmqnamesrv:9876
  #     - -Drocketmq.client.logRoot=/home/<USER>/logs
  #   command: sh mqproxy
  #   volumes:
  #     # - ./rocketmq/proxy/logs:/home/<USER>/logs
  #     # - ./rocketmq/proxy/store:/home/<USER>/store
  #     - ./rocketmq/broker/conf:/home/<USER>/rocketmq-5.2.0/conf
  #   networks:
  #     - rocketmq
  #   privileged: true
  # rmqdashboard:
  #   image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/apacherocketmq/rocketmq-dashboard:latest
  #   container_name: rmqdashboard
  #   restart: no
  #   depends_on:
  #     - rmqbroker
  #   # ports:
  #     # 修改了映射端口号
  #     # - 8202:8080
  #   environment:
  #     - JAVA_OPTS=-Drocketmq.namesrv.addr=127.0.0.1:9876 -Xms250m -Xmx512m
  #     - ROCKETMQ_CONSOLE_PORT=8202
  #   ulimits:
  #     nofile:
  #       soft: 1024
  #       hard: 2048
  #   # networks:
  #   #   - rocketmq
  #   network_mode: "host"
  #   privileged: true
  nacos:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nacos/nacos-server:v2.3.2
    container_name: nacos
    restart: no
    ports:
      - 8848:8848
      - 9848:9848
      - 9849:9849
      - 7848:7848
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - JVM_XMS=256m
      - JVM_XMX=256m
      - MODE=standalone
      # 使用 postgresql 作为数据库
      - SPRING_DATASOURCE_PLATFORM=postgresql
      # - DATABASE_SERVICE_HOST=postgres
      # - DATABASE_SERVICE_PORT=5432
      # - DATABASE_SERVICE_DB_NAME=nacos
      # - DATABASE_SERVICE_DB_USER=postgres
      # - DATABASE_SERVICE_DB_PASSWORD=6M5yQAtU
    volumes:
      - ./nacos/logs:/home/<USER>/logs
      - ./nacos/conf:/home/<USER>/conf
      - ./nacos/plugins:/home/<USER>/plugins
    # network_mode: "host"
    networks:
      - hcms_net
  nginx:
    image: swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nginx:1.28.0
    container_name: nginx
    restart: always
    # ports:
    #   - 80:80
    #   - 443:443
    volumes:
      - ./nginx/html:/usr/share/nginx/html
      - ./nginx/logs:/var/log/nginx
      - ./nginx:/etc/nginx
      - /home/<USER>/fe:/fe
      - /home/<USER>/report:/report
    environment:
      - NGINX_PORT=80
      - TZ=Asia/Shanghai
    privileged: true
    network_mode: "host"
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:9.0.3
    container_name: elasticsearch
    restart: always
    ports:
      - "9200:9200"  # HTTP API 端口
      - "9300:9300"  # 集群通信端口
    environment:
      - TZ=Asia/Shanghai
      - discovery.type=single-node  # 单节点模式，适合开发环境
      - ES_JAVA_OPTS=-Xms2g -Xmx2g  # JVM 内存设置
      - bootstrap.memory_lock=true  # 锁定内存，提高性能
      - ELASTIC_PASSWORD=cDtU8M9y  # 设置elastic用户密码
    volumes:
      - /etc/localtime:/etc/localtime:ro  # 只读挂载系统时间
      - ./elasticsearch/data:/usr/share/elasticsearch/data  # 数据目录
      - ./elasticsearch/logs:/usr/share/elasticsearch/logs   # 日志目录
      - ./elasticsearch/config:/usr/share/elasticsearch/config # 配置目录
    #ulimits:
      #memlock: -1:-1  # 禁用内存锁定限制
    #healthcheck:
      #test: ["CMD", "curl", "-f", "http://localhost:9200"]
      #interval: 30s
      #timeout: 10s
      #retries: 3
      #start_period: 60s
    privileged: true
    
networks:
  hcms_net:
    driver: bridge
  # rocketmq:
  #   driver: bridge
