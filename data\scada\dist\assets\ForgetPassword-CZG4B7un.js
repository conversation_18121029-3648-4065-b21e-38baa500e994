/* empty css                  *//* empty css                     */import{u as F}from"./index-CD6ICDO9.js";import{d as U,r as i,a as _,f as t,g as w,v as m,t as y,h as s,w as l,x as R,j as $,y as z,p as S,m as p,z as a,o as g}from"./element-plus-D-V1KzVw.js";import{_ as T}from"./_plugin-vue_export-helper-DlAUqK2U.js";const j={class:"login-container"},D={class:"login-form-wrapper"},L={class:"steps-container"},M={class:"step-number"},q={class:"step-number"},A={class:"step-number"},G={key:0,class:"step-content"},H={class:"verify-code-container"},J={key:1,class:"step-content"},K={key:2,class:"step-content success-content"},O=U({__name:"ForgetPassword",setup(Q){const k=F(),o=i(1),v=i(""),b=i(""),c=i(""),V=i(""),r=i(0),C=()=>{if(!v.value){a.error("请输入手机号码");return}if(!/^1[3-9]\d{9}$/.test(v.value)){a.error("请输入有效的手机号码");return}a.success("验证码发送成功"),r.value=60;const e=setInterval(()=>{r.value--,r.value<=0&&clearInterval(e)},1e3)},E=()=>{if(!v.value){a.error("请输入手机号码");return}if(!b.value){a.error("请输入验证码");return}a.success("验证成功"),o.value=2},I=()=>{if(!c.value){a.error("请输入新密码");return}if(c.value.length<6){a.error("密码长度不能少于6位");return}if(c.value!==V.value){a.error("两次输入的密码不一致");return}a.success("密码重置成功"),o.value=3},B=()=>{k.push("/login")},N=()=>{k.push("/login")};return(P,e)=>{const f=S,n=z,d=$,x=R;return g(),_("div",j,[t("div",D,[t("div",L,[t("div",{class:m(["step-item",{active:o.value===1}])},[t("div",M,y(o.value===1?"✓":"1"),1),e[5]||(e[5]=t("div",{class:"step-text"},"验证身份",-1))],2),t("div",{class:m(["step-line",{active:o.value>=2}])},null,2),t("div",{class:m(["step-item",{active:o.value===2}])},[t("div",q,y(o.value===2?"✓":"2"),1),e[6]||(e[6]=t("div",{class:"step-text"},"重置密码",-1))],2),t("div",{class:m(["step-line",{active:o.value>=3}])},null,2),t("div",{class:m(["step-item",{active:o.value===3}])},[t("div",A,y(o.value===3?"✓":"3"),1),e[7]||(e[7]=t("div",{class:"step-text"},"重置成功",-1))],2)]),o.value===1?(g(),_("div",G,[s(x,{class:"login-form",autocomplete:"off"},{default:l(()=>[s(n,null,{default:l(()=>[s(f,{modelValue:v.value,"onUpdate:modelValue":e[0]||(e[0]=u=>v.value=u),placeholder:"手机号码",type:"tel"},null,8,["modelValue"])]),_:1}),s(n,null,{default:l(()=>[t("div",H,[s(f,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=u=>b.value=u),placeholder:"短信验证码",type:"text"},null,8,["modelValue"]),s(d,{disabled:r.value>0,onClick:C,class:"verify-code-btn"},{default:l(()=>[p(y(r.value>0?`${r.value}秒后重新获取`:"获取验证码"),1)]),_:1},8,["disabled"])])]),_:1}),s(n,null,{default:l(()=>[s(d,{type:"primary",onClick:E,class:"next-btn"},{default:l(()=>e[8]||(e[8]=[p("下一步",-1)])),_:1,__:[8]})]),_:1}),s(n,null,{default:l(()=>[s(d,{onClick:B,class:"back-btn"},{default:l(()=>e[9]||(e[9]=[p("返回",-1)])),_:1,__:[9]})]),_:1})]),_:1})])):w("",!0),o.value===2?(g(),_("div",J,[s(x,{class:"login-form",autocomplete:"off"},{default:l(()=>[s(n,null,{default:l(()=>[s(f,{modelValue:c.value,"onUpdate:modelValue":e[2]||(e[2]=u=>c.value=u),placeholder:"新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),s(n,null,{default:l(()=>[s(f,{modelValue:V.value,"onUpdate:modelValue":e[3]||(e[3]=u=>V.value=u),placeholder:"确认新密码",type:"password","show-password":""},null,8,["modelValue"])]),_:1}),s(n,null,{default:l(()=>[s(d,{type:"primary",onClick:I,class:"next-btn"},{default:l(()=>e[10]||(e[10]=[p("确认重置",-1)])),_:1,__:[10]})]),_:1}),s(n,null,{default:l(()=>[s(d,{onClick:e[4]||(e[4]=u=>o.value=1),class:"back-btn"},{default:l(()=>e[11]||(e[11]=[p("返回",-1)])),_:1,__:[11]})]),_:1})]),_:1})])):w("",!0),o.value===3?(g(),_("div",K,[e[13]||(e[13]=t("div",{class:"success-icon"},"✓",-1)),e[14]||(e[14]=t("h3",{class:"success-title"},"密码重置成功",-1)),e[15]||(e[15]=t("p",{class:"success-message"},"您的密码已成功重置，请使用新密码登录",-1)),s(d,{type:"primary",onClick:N,class:"login-btn"},{default:l(()=>e[12]||(e[12]=[p("返回登录",-1)])),_:1,__:[12]})])):w("",!0)])])}}}),ee=T(O,[["__scopeId","data-v-712bb5b5"]]);export{ee as default};
