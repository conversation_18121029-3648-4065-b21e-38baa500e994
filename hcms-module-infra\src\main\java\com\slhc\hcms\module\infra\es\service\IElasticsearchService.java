package com.slhc.hcms.module.infra.es.service;

import com.alibaba.fastjson.JSONObject;

/**
 * Elasticsearch服务接口
 */
public interface IElasticsearchService {
    boolean createIndex(String indexName);
    boolean createIndex(String indexName,JSONObject mapping);
    boolean deleteIndex(String indexName);
    boolean saveData(String indexName, String typeName, String dataId, JSONObject data);
    boolean updateData(String indexName, String typeName, String dataId, JSONObject data);
    boolean deleteData(String indexName, String typeName, String dataId);
    JSONObject searchData(String indexName, String typeName, JSONObject query);
}