package com.slhc.hcms.module.infra.service.mqtt;

import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.MqttMessage;

/**
 * MQTT事件监听器接口
 * 用于处理接收到的MQTT消息
 */
public interface MqttEventListener extends IMqttMessageListener {

    /**
     * 获取QoS等级
     * 默认返回1
     * @return QoS等级 (0, 1, 2)
     */
    default int getQos() {
        return 1;
    }

    /**
     * 获取监听器关注的主题
     *
     * @return 主题字符串，支持通配符
     */
    String getTopic();

    /**
     * 获取监听器名称
     *
     * @return 监听器名称
     */
    String getListenerName();

    /**
     * 消息到达时的回调方法
     *
     * @param topic   消息主题
     * @param message 消息内容
     * @throws Exception 处理异常
     */
    @Override
    void messageArrived(String topic, MqttMessage message) throws Exception;

    /**
     * 监听器初始化方法
     */
    default void onListenerRegistered() {
        // 默认空实现，子类可重写
    }

    /**
     * 监听器销毁方法
     */
    default void onListenerUnregistered() {
        // 默认空实现，子类可重写
    }
}
