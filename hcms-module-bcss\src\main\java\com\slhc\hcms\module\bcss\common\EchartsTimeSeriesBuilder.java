package com.slhc.hcms.module.bcss.common;

import com.slhc.hcms.module.bcss.enums.TimeDimensionEnum;
import com.slhc.hcms.module.bcss.vo.BcssReportVo;
import com.slhc.hcms.module.bcss.vo.EchartsTimeSeriesVo;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * ECharts时间序列数据构建工具类
 * 提供24小时、1个月、1年维度的时间序列数据构建方法
 *
 * @author: HCS 2025-07-23
 */
public class EchartsTimeSeriesBuilder {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    /**
     * 构建时间序列数据（自动识别维度）
     */
    public static EchartsTimeSeriesVo build(List<BcssReportVo> rawData, TimeDimensionEnum dimension) {
        return switch (dimension) {
            case HOUR_24 -> buildHourSeries(rawData);
            case DAY_30 -> buildMonthSeries(rawData);
            case MONTH_12 -> buildYearSeries(rawData);
        };
    }

    /**
     * 天维度数据（单位：小时）
     */
    public static EchartsTimeSeriesVo buildHourSeries(List<BcssReportVo> rawData) {
        EchartsTimeSeriesVo dto = new EchartsTimeSeriesVo();
        dto.setDimensionType(TimeDimensionEnum.HOUR_24);
        dto.setTimeAxis(buildHourLabels());
        dto.setSeries(buildSeriesData(rawData,
                time -> time.getHour(),
                24,
                EchartsTimeSeriesBuilder::aggregateHourlyData
        ));
        dto.setTotalTaskCount(rawData.stream().mapToInt(BcssReportVo::getTaskQty).sum());
        dto.setTotalReceivedTaskCount(rawData.stream().mapToInt(BcssReportVo::getReceiveTaskQty).sum());
        return dto;
    }

    /**
     * 月维度数据（单位：天）
     */
    public static EchartsTimeSeriesVo buildMonthSeries(List<BcssReportVo> rawData) {
//        EchartsTimeSeriesVo dto = new EchartsTimeSeriesVo();
//        dto.setDimensionType(TimeDimensionEnum.DAY_30);
//        dto.setTimeAxis(buildDayLabels());
//        dto.setSeries(buildSeriesData(rawData,
//                time -> time.getDayOfMonth() - 1, // 调整为从0开始的索引
//                31,
//                EchartsTimeSeriesBuilder::aggregateDailyData
//        ));
//        return dto;

        if (rawData == null || rawData.isEmpty()) {
            // 如果没有数据，返回默认30天的空图表
            EchartsTimeSeriesVo dto = new EchartsTimeSeriesVo();
            dto.setDimensionType(TimeDimensionEnum.DAY_30);
            dto.setTimeAxis(buildDayLabels(30));
            dto.setSeries(buildSeriesData(rawData,
                    time -> time.getDayOfMonth() - 1,
                    30,
                    EchartsTimeSeriesBuilder::aggregateDailyData
            ));
            return dto;
        }

        // 从数据中提取第一个日期来确定月份天数
        String sampleDate = rawData.get(0).getDate();
        LocalDateTime dateTime = parseTime(sampleDate);
        int daysInMonth = dateTime.toLocalDate().lengthOfMonth();

        EchartsTimeSeriesVo dto = new EchartsTimeSeriesVo();
        dto.setDimensionType(TimeDimensionEnum.DAY_30);
        dto.setTimeAxis(buildDayLabels(daysInMonth));
        dto.setSeries(buildSeriesData(rawData,
                time -> time.getDayOfMonth() - 1,
                daysInMonth,
                EchartsTimeSeriesBuilder::aggregateDailyData
        ));
        return dto;
    }

    /**
     * 年维度数据（单位：月）
     */
    public static EchartsTimeSeriesVo buildYearSeries(List<BcssReportVo> rawData) {
        EchartsTimeSeriesVo dto = new EchartsTimeSeriesVo();
        dto.setDimensionType(TimeDimensionEnum.MONTH_12);
        dto.setTimeAxis(buildMonthLabels());
        dto.setSeries(buildSeriesData(rawData,
                time -> time.getMonthValue() - 1, // 调整为从0开始的索引
                12,
                EchartsTimeSeriesBuilder::aggregateMonthlyData
        ));
        return dto;
    }

    /**
     * 通用数据构建方法
     *
     * @param timeIndexMapper 时间到数组索引的映射函数
     * @param timeSlotCount   时间分段总数（24/30/12）
     * @param aggregator      数据聚合策略
     */
    private static List<EchartsTimeSeriesVo.SeriesData> buildSeriesData(
            List<BcssReportVo> rawData,
            Function<LocalDateTime, Integer> timeIndexMapper,
            int timeSlotCount,
            Function<List<BcssReportVo>, BcssReportVo> aggregator) {

        // 1. 按时间分组
        Map<Integer, List<BcssReportVo>> groupedData = rawData.stream()
                .collect(Collectors.groupingBy(
                        item -> timeIndexMapper.apply(parseTime(item.getDate()))
                ));

//        if (timeSlotCount != 24) {
//            timeIndex = timeIndex - 1;
//        }
        // 2. 初始化所有指标系列
        Map<String, int[]> seriesMap = initSeriesMap(timeSlotCount);

        // 3. 填充数据
        groupedData.forEach((timeIndex, items) -> {
            BcssReportVo aggregated = aggregator.apply(items);
            seriesMap.get("taskQty")[timeIndex] = aggregated.getTaskQty();
            seriesMap.get("finishTaskQty")[timeIndex] = aggregated.getFinishTaskQty();
            seriesMap.get("cancelTaskQty")[timeIndex] = aggregated.getCancelTaskQty();
            seriesMap.get("timeoutTaskQty")[timeIndex] = aggregated.getTimeoutTaskQty();
            seriesMap.get("receiveTaskQty")[timeIndex] = aggregated.getReceiveTaskQty();
            seriesMap.get("receiveFinishTaskQty")[timeIndex] = aggregated.getReceiveFinishTaskQty();
        });

        // 4. 转换为DTO格式
        return convertToSeriesList(seriesMap);
    }

    // ======================= 数据聚合策略 =======================

    /**
     * 按小时聚合（取最后一条记录）
     */
    private static BcssReportVo aggregateHourlyData(List<BcssReportVo> items) {
        return items.get(items.size() - 1); // 假设数据已按时间排序
    }

    /**
     * 按天聚合（求和）
     */
    private static BcssReportVo aggregateDailyData(List<BcssReportVo> items) {
        BcssReportVo result = new BcssReportVo();
        items.forEach(item -> {
            result.setTaskQty(result.getTaskQty() + item.getTaskQty());
            result.setFinishTaskQty(result.getFinishTaskQty() + item.getFinishTaskQty());
            result.setCancelTaskQty(result.getCancelTaskQty() + item.getCancelTaskQty());
            result.setTimeoutTaskQty(result.getTimeoutTaskQty() + item.getTimeoutTaskQty());
            result.setReceiveTaskQty(result.getReceiveTaskQty() + item.getReceiveTaskQty());
            result.setReceiveFinishTaskQty(result.getReceiveFinishTaskQty() + item.getReceiveFinishTaskQty());
        });
        return result;
    }

    /**
     * 按月聚合（求和）
     */
    private static BcssReportVo aggregateMonthlyData(List<BcssReportVo> items) {
        return aggregateDailyData(items); // 复用相同逻辑
    }

    // ======================= 辅助方法 =======================

    /**
     * 初始化系列数据容器
     */
    private static Map<String, int[]> initSeriesMap(int size) {
        Map<String, int[]> map = new LinkedHashMap<>();
        map.put("taskQty", new int[size]);
        map.put("finishTaskQty", new int[size]);
        map.put("cancelTaskQty", new int[size]);
        map.put("timeoutTaskQty", new int[size]);
        map.put("receiveTaskQty", new int[size]);
        map.put("receiveFinishTaskQty", new int[size]);
        return map;
    }

    /**
     * 转换为系列列表
     */
    private static List<EchartsTimeSeriesVo.SeriesData> convertToSeriesList(Map<String, int[]> seriesMap) {
        Map<String, String> nameMapping = Map.of(
                "taskQty", "任务总数",
                "finishTaskQty", "已完成",
                "cancelTaskQty", "已取消",
                "timeoutTaskQty", "超时任务",
                "receiveTaskQty", "接收任务",
                "receiveFinishTaskQty", "接收完成"
        );

        return seriesMap.entrySet().stream()
                .map(entry -> {
                    EchartsTimeSeriesVo.SeriesData series = new EchartsTimeSeriesVo.SeriesData();
                    series.setName(nameMapping.get(entry.getKey()));
                    series.setData(Arrays.stream(entry.getValue()).boxed().collect(Collectors.toList()));
                    return series;
                })
                .collect(Collectors.toList());
    }

    /**
     * 时间标签生成
     */
    private static List<String> buildHourLabels() {
        return IntStream.range(0, 24)
                .mapToObj(h -> String.format("%02d:00", h))
                .collect(Collectors.toList());
    }

    private static List<String> buildDayLabels() {
        return IntStream.range(1, 31)
                .mapToObj(d -> String.format("%02d日", d))
                .collect(Collectors.toList());
    }

    private static List<String> buildDayLabels(int days) {
        return IntStream.rangeClosed(1, days)
                .mapToObj(d -> String.format("%02d日", d))
                .collect(Collectors.toList());
    }

    private static List<String> buildMonthLabels() {
        return IntStream.range(1, 13)
                .mapToObj(m -> String.format("%02d月", m))
                .collect(Collectors.toList());
    }

    /**
     * 时间解析
     */
    private static LocalDateTime parseTime(String dateStr) {
        return LocalDateTime.parse(dateStr, DATE_FORMATTER);
    }
}
