package com.slhc.hcms.module.uniscan.enums;

import lombok.Getter;

/**
 * UniScan任务追溯状态枚举
 * <AUTHOR>
 */
@Getter
public enum UniscTaskStatusEnum {
    
    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    
    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),
    
    /**
     * 同步HIS失败
     */
    HIS_SYNC_FAILED(3, "同步HIS失败");
    
    private final Integer code;
    private final String message;
    
    UniscTaskStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 根据code获取枚举
     */
    public static UniscTaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UniscTaskStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据code获取消息
     */
    public static String getMessageByCode(Integer code) {
        UniscTaskStatusEnum status = getByCode(code);
        return status != null ? status.message : null;
    }
}