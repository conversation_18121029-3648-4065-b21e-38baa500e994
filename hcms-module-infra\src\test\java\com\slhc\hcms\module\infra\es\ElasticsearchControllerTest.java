package com.slhc.hcms.module.infra.es;

import com.alibaba.fastjson.JSONObject;
import com.slhc.hcms.module.infra.es.controller.ElasticsearchController;
import com.slhc.hcms.module.infra.es.service.impl.ElasticsearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class ElasticsearchControllerTest {

    private MockMvc mockMvc;

    @Mock
    private ElasticsearchServiceImpl elasticsearchService;

    @InjectMocks
    private ElasticsearchController elasticsearchController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(elasticsearchController).build();
    }

    @Test
    void testCreateIndex_ShouldReturnSuccess() throws Exception {
        // Arrange
        when(elasticsearchService.createIndex("test_index")).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/es/create-index")
                        .param("indexName", "test_index"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        verify(elasticsearchService, times(1)).createIndex("test_index");
    }

    @Test
    void testSaveData_ShouldReturnSuccess() throws Exception {
        // Arrange
        JSONObject data = new JSONObject();
        data.put("name", "John Doe");

        when(elasticsearchService.saveData("test_index", "user", "1", data)).thenReturn(true);

        // Act & Assert
        mockMvc.perform(post("/es/save-data")
                        .param("indexName", "test_index")
                        .param("typeName", "user")
                        .param("dataId", "1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(data.toJSONString()))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        verify(elasticsearchService, times(1)).saveData("test_index", "user", "1", data);
    }

    @Test
    void testSearchData_ShouldReturnJsonResponse() throws Exception {
        // Arrange
        JSONObject query = new JSONObject();
        query.put("query", "john");

        JSONObject response = new JSONObject();
        response.put("hits", new JSONObject());

        when(elasticsearchService.searchData("test_index", "user", query)).thenReturn(response);

        // Act & Assert
        mockMvc.perform(post("/es/search")
                        .param("indexName", "test_index")
                        .param("typeName", "user")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(query.toJSONString()))
                .andExpect(status().isOk())
                .andExpect(content().json(response.toJSONString()));

        verify(elasticsearchService, times(1)).searchData("test_index", "user", query);
    }
}
