//package com.slhc.hcms.moduels.infra.es.test;
//
//import org.elasticsearch.Version;
//import org.elasticsearch.client.RequestOptions;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.elasticsearch.client.core.MainResponse;
////import org.junit.Assume;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.context.TestConfiguration;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Primary;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.io.IOException;
//
//import static junit.framework.Assert.assertNotNull;
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.mock;
//
//
//@SpringBootTest(classes = {
//        org.jeecg.JeecgSystemApplication.class,
//        com.slhc.hcms.moduels.infra.es.config.ElasticsearchConfig.class
////        org.jeecg.modules.infra.config.ElasticsearchConfig.class
//})
//@ActiveProfiles("test")
//public class EsConnectionTest {
//
//    @Autowired
//    private RestHighLevelClient client;
//
//    @TestConfiguration
//    static class TestConfig {
//        @Bean
//        @Primary
//        public RestHighLevelClient mockClient() {
//            return mock(RestHighLevelClient.class); // 用于隔离测试
//        }
//    }
//
//    @Test
//    void testRealConnection() throws IOException {
////        Assume.assumeTrue(
////                "集成测试环境".equals(System.getenv("TEST_ENV")),
////                "仅在集成测试环境运行"
////        );
////
////        MainResponse response = client.info(RequestOptions.DEFAULT);
////        assertFalse(response.getClusterName().toString().isEmpty());
//    }
//
//    @Test
//    void testMockClient() {
////        RestHighLevelClient mockClient = this.client;
////        when(mockClient.info(any())).thenReturn(
////                new MainResponse("test-cluster",
////                        new Version(7,15,2),
////                        "test-node",
////                        "mock-uuid")
////        );
////
////        assertDoesNotThrow(() -> {
////            MainResponse response = mockClient.info(RequestOptions.DEFAULT);
////            assertEquals("test-cluster", response.getClusterName());
////        });
//    }
//}
