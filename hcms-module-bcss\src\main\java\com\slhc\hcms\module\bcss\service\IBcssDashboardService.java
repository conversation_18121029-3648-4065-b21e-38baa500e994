package com.slhc.hcms.module.bcss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.slhc.hcms.module.bcss.dto.MonitorRedisMessage;
import com.slhc.hcms.module.bcss.entity.BcssDashboard;

public interface IBcssDashboardService extends IService<BcssDashboard> {
    /**
     * 处理仪表盘数据请求
     *
     * @param request
     */
    BcssDashboard processStationData(BcssDashboard request);

    void recieveRedisMessage(MonitorRedisMessage monitorRedisMessage);
}
