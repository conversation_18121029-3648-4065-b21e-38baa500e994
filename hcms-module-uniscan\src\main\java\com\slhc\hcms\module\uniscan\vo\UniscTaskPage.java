package com.slhc.hcms.module.uniscan.vo;

import java.util.List;

import com.slhc.hcms.module.uniscan.entity.UniscTaskDetail;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: UniScan任务表
 * @Author: jeecg-boot
 * @Date:   2025-09-22
 * @Version: V1.0
 */
@Data
@Schema(description="UniScan任务表")
public class UniscTaskPage {

	/**主键*/
	@Schema(description = "主键")
    private java.lang.String id;
	/**删除状态*/
	@Schema(description = "删除状态")
    private java.lang.Integer delFlag;
	/**处方单号*/
	@Excel(name = "处方单号", width = 15)
	@Schema(description = "处方单号")
    private java.lang.String prescriptionNumber;
	/**摆药数量**/
	@Excel(name = "摆药数量", width = 15)
	@Schema(description = "摆药数量")
	private Integer count;
	/**状态 1:已创建、2:进行中、3:已完成*/
	@Excel(name = "任务状态", width = 15, dicCode = "unisc_task_status")
	@Schema(description = "任务状态")
	@Dict(dicCode = "unisc_task_status")
	private Integer status;
	/**UniScan 设备名称*/
	@Excel(name = "UniScan 设备名称", width = 15)
	@Schema(description = "UniScan 设备名称")
    private java.lang.String deviceName;
	/**创建人*/
	@Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**租户Id*/
	@Schema(description = "租户Id")
    private java.lang.String tenantId;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@Schema(description = "备注")
    private java.lang.String remark;

	@ExcelCollection(name="UniScan任务明细表")
	@Schema(description = "UniScan任务明细表")
	private List<UniscTaskDetail> uniscTaskDetailList;

}
