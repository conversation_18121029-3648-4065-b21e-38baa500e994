package com.slhc.hcms.module.bcss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.slhc.hcms.module.bcss.common.BusConstant;
import com.slhc.hcms.module.bcss.dto.ReceiveMQTasKError;
import com.slhc.hcms.module.bcss.entity.BcssBox;
import com.slhc.hcms.module.bcss.entity.BcssTask;
import com.slhc.hcms.module.bcss.entity.BcssTaskLog;
import com.slhc.hcms.module.bcss.entity.BcssTaskPath;
import com.slhc.hcms.module.bcss.mapper.BcssBoxMapper;
import com.slhc.hcms.module.bcss.mapper.BcssTaskLogMapper;
import com.slhc.hcms.module.bcss.mapper.BcssTaskMapper;
import com.slhc.hcms.module.bcss.service.IBcssTaskPathService;
import com.slhc.hcms.module.bcss.service.IBcssTaskService;
import com.slhc.hcms.module.bcss.vo.BcssTaskDetailVo;
import com.slhc.hcms.module.infra.service.mqtt.IMqttService;
import com.slhc.hcms.module.infra.service.mqtt.MqttEventListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.jeecg.common.exception.JeecgBootBizTipException;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.message.websocket.WebSocket;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 任务表
 * @Author: jeecg-boot
 * @Date: 2025-07-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class BcssTaskServiceImpl extends ServiceImpl<BcssTaskMapper, BcssTask> implements IBcssTaskService {

    @Autowired
    private BcssTaskLogMapper bcssTaskLogMapper;

    @Autowired
    private BcssBoxMapper bcssBoxMapper;

    @Autowired
    private IBcssTaskPathService ibcssTaskPathService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WebSocket webSocket;

    @Autowired
    private IMqttService mqttService;

    @PostConstruct
    public void initSubscriptions() {
        try {
            // 创建MQTT事件监听器来订阅主题
            MqttEventListener listener = new MqttEventListener() {
                @Override
                public String getTopic() {
                    return "bcss/v1/slhc/rcs/createorder/error";
                }

                @Override
                public String getListenerName() {
                    return "BcssTaskErrorListener";
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    try {
                        String payload = new String(message.getPayload());
                        log.info("收到MQTT消息 - 主题: {}, 内容: {}", topic, payload);
                        receiveMQ(payload);
                    } catch (Exception e) {
                        log.error("处理MQTT消息失败: {}", e.getMessage(), e);
                    }
                }
            };
//             实际调用订阅方法
            mqttService.subscribe(listener);


            log.info("MQTT主题订阅成功：bcss/v1/slhc/rcs/createorder/error");
        } catch (Exception e) {
            log.error("MQTT主题订阅失败：bcss/v1/slhc/rcs/createorder/error，错误：{}", e.getMessage(), e);
        }
    }


    public boolean createBcssTask(BcssTask bcssTask) {
        return createBcssTask(bcssTask, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean createBcssTask(BcssTask bcssTask, BcssBox bcssBox) {
        boolean isCreateBox = false;
        // 查询所有路径估算耗时
        List<BcssTaskPath> bcssTaskPaths = ibcssTaskPathService.getBaseMapper().selectList(new LambdaQueryWrapper<BcssTaskPath>()
                .eq(BcssTaskPath::getDelFlag, BusConstant.IS_NOT_DELETE));
        Map<String, BcssTaskPath> mapBcssTaskPath = bcssTaskPaths.stream().collect(Collectors.toMap(BcssTaskPath::getPath, Function.identity()));
        if (bcssBox == null) {
            isCreateBox = true;
            // 找到指定任务对应的箱子
            bcssBox = bcssBoxMapper.selectOne(new LambdaQueryWrapper<BcssBox>().eq(BcssBox::getBarcode, bcssTask.getBoxBarcode()));
        }
        if (bcssBox != null && !BusConstant.NO_TASK.equalsIgnoreCase(bcssBox.getTaskId()))
            throw new JeecgBootBizTipException("箱子:" + bcssTask.getBoxBarcode() + "已经绑定了其他任务");
        // 设置创建人信息和更新人信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            bcssTask.setCreateBy(sysUser.getUsername());
            bcssTask.setUpdateBy(sysUser.getUsername());
            bcssTask.setTenantId(sysUser.getRelTenantIds()); // 设置租户ID
        }
        // 设置任务初始状态
        bcssTask.setStatus(BusConstant.TASK_CREATE); // 1-已创建
        // 设置任务类型
        //根据barcode来判断箱子类型
        if (bcssTask.getTaskType() == null)
            if (bcssTask.getBoxBarcode().startsWith("2")) bcssTask.setTaskType(BusConstant.FIX_TASK);
            else bcssTask.setTaskType(BusConstant.TEMP_TASK);

        String taskPath = StringUtils.leftPad(bcssTask.getSourStationId(), 4, '0')
                + StringUtils.leftPad(bcssTask.getDestStationId(), 4, '0');
        BcssTaskPath bcssTaskPath = mapBcssTaskPath.get(taskPath);

        bcssTask.setEstimatedTime(bcssTaskPath == null ? 0 : bcssTaskPath.getConsumeTime());
        bcssTask.setStartTime(new Date());
        bcssTask.setCreateTime(new Date());
        bcssTask.setUpdateTime(new Date());
        bcssTask.setIsTimeout(0);
        // 保存到数据库
        save(bcssTask);
        if (!isCreateBox) bcssBoxMapper.update(null, Wrappers.<BcssBox>lambdaUpdate()
                .set(BcssBox::getTaskId, bcssTask.getId()).eq(BcssBox::getId, bcssBox.getId()));
        // 预留通信接口
//        String url = "http://10.86.40.116:55200/rcs/v1/createorder";
//        RcsTaskCreateReqDTO rcsTaskCreateReqDTO = new RcsTaskCreateReqDTO();
//        rcsTaskCreateReqDTO.setTaskId(bcssTask.getId());
//        rcsTaskCreateReqDTO.setBoxid(bcssBox.getBarcode());
//        rcsTaskCreateReqDTO.setStartSiteId("st-" + bcssTask.getSourStationId());
//        rcsTaskCreateReqDTO.setEndSiteId("st-" + bcssTask.getDestStationId());
//        RcsTaskCreateRespDTO rcsTaskCreateRespDTO = restTemplate.postForObject(url, rcsTaskCreateReqDTO, RcsTaskCreateRespDTO.class);
//        if(rcsTaskCreateRespDTO.getCode().equals("500"))
//            throw new JeecgBootBizTipException("RCS系统创建任务失败");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BcssTask modifyTaskDestination(String taskId, String newDestinationId) {
        BcssTask task = getById(taskId);
        if (task == null) throw new RuntimeException("没有找到指定任务！");

        // 2=已完成, 3=已取消
        if (Arrays.asList(2, 3).contains(task.getStatus()))
            throw new RuntimeException("该任务当前状态不允许修改目的地！");

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        task.setDestStationId(newDestinationId);
        if (sysUser != null) task.setUpdateBy(sysUser.getUsername());

        updateById(task);

        // TODO: 调用MQTT服务，通知硬件任务目的地已变更
        System.out.println("MQTT Placeholder: Task destination changed notification for task ID: " + task.getId());

        return task;
    }

    @Transactional
    public void cancelTask(String taskId, boolean isRcs) {
        BcssTask task = getById(taskId);
        if (task == null) throw new JeecgBootBizTipException("没有找到指定任务！");
        //找到指定任务对应的箱子
        BcssBox bcssBox = bcssBoxMapper.selectOne(new LambdaQueryWrapper<BcssBox>().eq(BcssBox::getTaskId, taskId));
        if (bcssBox == null) throw new JeecgBootBizTipException("该任务没有绑定箱子！");
        if (isRcs) task.setUpdateBy("RCS");
        else {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) task.setUpdateBy(sysUser.getUsername());
        }
        task.setStatus(BusConstant.TASK_CANCEL);

        updateById(task);
        bcssBoxMapper.update(null, Wrappers.<BcssBox>lambdaUpdate()
                .set(BcssBox::getTaskId, BusConstant.NO_TASK).eq(BcssBox::getId, bcssBox.getId()));

        // TODO: 调用MQTT服务，通知硬件任务已取消
        log.info("MQTT Placeholder: Task cancelled notification for task ID: " + task.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void finishTask(String taskId) {
        BcssTask task = getById(taskId);
        if (task == null) throw new JeecgBootBizTipException("没有找到指定任务！");
        //找到指定任务对应的箱子
        BcssBox bcssBox = bcssBoxMapper.selectOne(new LambdaQueryWrapper<BcssBox>().eq(BcssBox::getTaskId, taskId));
        if (bcssBox == null) throw new JeecgBootBizTipException("该任务没有绑定箱子！");

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) task.setUpdateBy(sysUser.getUsername());
        task.setStatus(BusConstant.TASK_FINISH);
        task.setFinishTime(new Date());
        if (task.getStartTime() != null) {
            long consume = (task.getFinishTime().getTime() - task.getStartTime().getTime()) / 1000;
            task.setConsumeTime((int) consume);
        }

        updateById(task);
        bcssBoxMapper.update(null, Wrappers.<BcssBox>lambdaUpdate()
                .set(BcssBox::getTaskId, BusConstant.NO_TASK).eq(BcssBox::getId, bcssBox.getId()));

        // TODO: 调用MQTT服务，通知硬件任务已手动完成
        log.info("MQTT Placeholder: Task finished notification for task ID: " + task.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskWithLogic(BcssTask bcssTask) {
        //从数据库获取权威的任务对象
        BcssTask taskInDb = getById(bcssTask.getId());
        if (taskInDb == null) throw new RuntimeException("没有找到要更新的任务！");
        //将前端允许编辑的字段同步到 taskInDb 对象
        taskInDb.setRemark(bcssTask.getRemark());
        taskInDb.setDestStationId(bcssTask.getDestStationId());
        taskInDb.setDestStationName(bcssTask.getDestStationName());

        if (bcssTask.getTaskType() == null)
            if (bcssTask.getBoxBarcode().startsWith("2")) taskInDb.setTaskType(BusConstant.FIX_TASK);
            else taskInDb.setTaskType(BusConstant.TEMP_TASK);
        else taskInDb.setTaskType(bcssTask.getTaskType());

        //设置更新人信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) taskInDb.setUpdateBy(sysUser.getUsername());
        //保存我们处理过的、可信的 taskInDb 对象
        return updateById(taskInDb);
    }

    @Override
    public BcssTaskDetailVo getTaskDetailById(String taskId) {
        BcssTask task = getById(taskId);
        if (task == null) return null;

        QueryWrapper<BcssTaskLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.orderByDesc("create_time");
        List<BcssTaskLog> logs = bcssTaskLogMapper.selectList(queryWrapper);

        BcssTaskDetailVo detailVo = new BcssTaskDetailVo();
        BeanUtils.copyProperties(task, detailVo);
        detailVo.setLogs(logs);

        return detailVo;
    }

    public void receiveMQ(String message) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        ReceiveMQTasKError receiveMQTasKError = mapper.readValue(message, ReceiveMQTasKError.class);
        try {
            cancelTask(receiveMQTasKError.getTaskId(), true);
            webSocket.pushMessage(receiveMQTasKError.getStationId(), mapper.writeValueAsString(receiveMQTasKError));
        } catch (Exception e) {
            receiveMQTasKError.setErrorMessage(e.getMessage());
            webSocket.pushMessage(receiveMQTasKError.getStationId(), mapper.writeValueAsString(receiveMQTasKError));
        }

    }
}
